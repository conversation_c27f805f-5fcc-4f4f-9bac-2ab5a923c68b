<?php
/**
 * ===================================
 * إعدادات الخادم - Server Configuration
 * نظام قمة الوعد للسفريات
 * ===================================
 */

// إعدادات قاعدة البيانات
define('DB_HOST', 'localhost');
define('DB_NAME', 'qimat_alwaed_accounting');
define('DB_USER', 'qimat_user');
define('DB_PASS', 'secure_password_2024');
define('DB_CHARSET', 'utf8mb4');

// إعدادات الأمان
define('SECRET_KEY', 'qimat_alwaed_secret_key_2024_very_secure');
define('JWT_SECRET', 'jwt_secret_key_qimat_alwaed_2024');
define('ENCRYPTION_KEY', 'encryption_key_qimat_alwaed_2024_secure');
define('SALT', 'qimat_alwaed_salt_2024');

// إعدادات الجلسة
define('SESSION_TIMEOUT', 28800); // 8 ساعات
define('SESSION_NAME', 'QIMAT_ALWAED_SESSION');
define('COOKIE_LIFETIME', 86400); // 24 ساعة

// إعدادات الأمان المتقدمة
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOCKOUT_DURATION', 900); // 15 دقيقة
define('ENABLE_TWO_FACTOR', false);
define('ENABLE_IP_WHITELIST', false);
define('ENABLE_RATE_LIMITING', true);
define('MAX_REQUESTS_PER_MINUTE', 60);

// إعدادات البريد الإلكتروني
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '<EMAIL>');
define('SMTP_PASSWORD', 'email_password');
define('SMTP_ENCRYPTION', 'tls');
define('FROM_EMAIL', '<EMAIL>');
define('FROM_NAME', 'قمة الوعد للسفريات');

// إعدادات الملفات
define('UPLOAD_PATH', 'uploads/');
define('MAX_FILE_SIZE', 10485760); // 10 MB
define('ALLOWED_EXTENSIONS', 'jpg,jpeg,png,pdf,doc,docx,xls,xlsx');

// إعدادات النظام
define('SYSTEM_NAME', 'قمة الوعد للسفريات');
define('SYSTEM_VERSION', '2.0.0');
define('SYSTEM_URL', 'https://qimat-alwaed.com');
define('API_URL', 'https://api.qimat-alwaed.com');
define('ADMIN_EMAIL', '<EMAIL>');

// إعدادات التطوير
define('DEBUG_MODE', false);
define('LOG_ERRORS', true);
define('LOG_PATH', 'logs/');
define('ENABLE_PROFILING', false);

// إعدادات التخزين المؤقت
define('ENABLE_CACHE', true);
define('CACHE_LIFETIME', 3600); // ساعة واحدة
define('CACHE_PATH', 'cache/');

// إعدادات النسخ الاحتياطية
define('BACKUP_PATH', 'backups/');
define('AUTO_BACKUP', true);
define('BACKUP_INTERVAL', 86400); // يومياً

// إعدادات SSL
define('FORCE_SSL', true);
define('SSL_CERT_PATH', '/path/to/ssl/cert.pem');
define('SSL_KEY_PATH', '/path/to/ssl/private.key');

// إعدادات CORS
define('CORS_ALLOWED_ORIGINS', 'https://qimat-alwaed.com,https://www.qimat-alwaed.com');
define('CORS_ALLOWED_METHODS', 'GET,POST,PUT,DELETE,OPTIONS');
define('CORS_ALLOWED_HEADERS', 'Content-Type,Authorization,X-Requested-With');

// إعدادات المنطقة الزمنية
date_default_timezone_set('Asia/Riyadh');

// إعدادات اللغة
define('DEFAULT_LANGUAGE', 'ar');
define('SUPPORTED_LANGUAGES', 'ar,en');

// إعدادات الدفع (إذا لزم الأمر)
define('PAYMENT_GATEWAY', 'none'); // none, stripe, paypal, etc.
define('PAYMENT_CURRENCY', 'SAR');

// إعدادات API الخارجية
define('GOOGLE_MAPS_API_KEY', 'your_google_maps_api_key');
define('WEATHER_API_KEY', 'your_weather_api_key');

// إعدادات الإشعارات
define('ENABLE_PUSH_NOTIFICATIONS', false);
define('FIREBASE_SERVER_KEY', 'your_firebase_server_key');
define('ENABLE_SMS_NOTIFICATIONS', false);
define('SMS_API_KEY', 'your_sms_api_key');

// إعدادات المراقبة
define('ENABLE_MONITORING', true);
define('MONITORING_ENDPOINT', 'https://monitoring.qimat-alwaed.com');
define('HEALTH_CHECK_INTERVAL', 300); // 5 دقائق

// إعدادات الأداء
define('ENABLE_GZIP', true);
define('ENABLE_MINIFICATION', true);
define('ENABLE_CDN', false);
define('CDN_URL', 'https://cdn.qimat-alwaed.com');

// إعدادات الأمان الإضافية
define('ENABLE_CSRF_PROTECTION', true);
define('ENABLE_XSS_PROTECTION', true);
define('ENABLE_CLICKJACKING_PROTECTION', true);
define('ENABLE_CONTENT_TYPE_SNIFFING_PROTECTION', true);

// إعدادات قاعدة البيانات المتقدمة
define('DB_POOL_SIZE', 10);
define('DB_TIMEOUT', 30);
define('DB_RETRY_ATTEMPTS', 3);
define('ENABLE_DB_ENCRYPTION', true);

// إعدادات التحليلات
define('ENABLE_ANALYTICS', false);
define('GOOGLE_ANALYTICS_ID', 'GA_TRACKING_ID');
define('ENABLE_USER_TRACKING', false);

// إعدادات الصيانة
define('MAINTENANCE_MODE', false);
define('MAINTENANCE_MESSAGE', 'النظام تحت الصيانة. سيعود قريباً.');
define('MAINTENANCE_ALLOWED_IPS', '127.0.0.1,::1');

// إعدادات التحديث التلقائي
define('ENABLE_AUTO_UPDATE', false);
define('UPDATE_CHECK_INTERVAL', 86400); // يومياً
define('UPDATE_SERVER_URL', 'https://updates.qimat-alwaed.com');

// إعدادات الشركة
define('COMPANY_NAME', 'قمة الوعد للسفريات');
define('COMPANY_ADDRESS', 'الرياض، المملكة العربية السعودية');
define('COMPANY_PHONE', '+966123456789');
define('COMPANY_EMAIL', '<EMAIL>');
define('COMPANY_WEBSITE', 'https://qimat-alwaed.com');
define('COMPANY_LOGO', 'assets/images/logo.png');

// إعدادات التراخيص
define('LICENSE_KEY', 'QIMAT_ALWAED_LICENSE_2024');
define('LICENSE_EXPIRY', '2025-12-31');
define('MAX_USERS', 100);
define('MAX_BOOKINGS_PER_MONTH', 10000);

// إعدادات التكامل
define('ENABLE_API', true);
define('API_VERSION', 'v1');
define('API_RATE_LIMIT', 1000); // طلبات في الساعة
define('ENABLE_WEBHOOKS', false);

// إعدادات الأرشفة
define('ENABLE_ARCHIVING', true);
define('ARCHIVE_AFTER_DAYS', 365); // سنة واحدة
define('ARCHIVE_PATH', 'archive/');

// إعدادات التقارير
define('ENABLE_ADVANCED_REPORTS', true);
define('REPORT_CACHE_LIFETIME', 1800); // 30 دقيقة
define('MAX_REPORT_RECORDS', 50000);

// إعدادات الأمان للملفات
define('DISABLE_FILE_EXECUTION', true);
define('SCAN_UPLOADED_FILES', true);
define('QUARANTINE_PATH', 'quarantine/');

// إعدادات الشبكة
define('NETWORK_TIMEOUT', 30);
define('MAX_CONCURRENT_CONNECTIONS', 100);
define('ENABLE_KEEP_ALIVE', true);

// إعدادات الذاكرة
define('MEMORY_LIMIT', '512M');
define('MAX_EXECUTION_TIME', 300); // 5 دقائق

// إعدادات الأخطاء
define('ERROR_REPORTING_LEVEL', E_ALL & ~E_NOTICE);
define('LOG_LEVEL', 'INFO'); // DEBUG, INFO, WARNING, ERROR
define('MAX_LOG_SIZE', 10485760); // 10 MB

// إعدادات النسخ الاحتياطية المتقدمة
define('BACKUP_ENCRYPTION', true);
define('BACKUP_COMPRESSION', true);
define('REMOTE_BACKUP_ENABLED', false);
define('REMOTE_BACKUP_SERVER', 'backup.qimat-alwaed.com');

// إعدادات الاختبار
define('TESTING_MODE', false);
define('TEST_DATABASE', 'qimat_alwaed_test');
define('ENABLE_UNIT_TESTS', false);

// إعدادات المطورين
define('DEVELOPER_MODE', false);
define('SHOW_DEBUG_INFO', false);
define('ENABLE_QUERY_LOG', false);

// إعدادات الأمان النهائية
define('SECURITY_LEVEL', 'HIGH');
define('ENABLE_INTRUSION_DETECTION', true);
define('ENABLE_HONEYPOT', false);
define('SECURITY_SCAN_INTERVAL', 3600); // ساعة واحدة

// تحميل الإعدادات المخصصة إذا وجدت
if (file_exists(__DIR__ . '/config.local.php')) {
    include __DIR__ . '/config.local.php';
}

// التحقق من الإعدادات المطلوبة
if (!defined('DB_HOST') || !defined('DB_NAME') || !defined('SECRET_KEY')) {
    die('إعدادات قاعدة البيانات أو الأمان غير مكتملة');
}

// إعداد معالج الأخطاء
if (LOG_ERRORS) {
    ini_set('log_errors', 1);
    ini_set('error_log', LOG_PATH . 'php_errors.log');
}

// إعداد حد الذاكرة
ini_set('memory_limit', MEMORY_LIMIT);
ini_set('max_execution_time', MAX_EXECUTION_TIME);

// إعداد الجلسة الآمنة
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', FORCE_SSL ? 1 : 0);
ini_set('session.use_strict_mode', 1);
ini_set('session.cookie_samesite', 'Strict');

// إعداد headers الأمان
if (ENABLE_CSRF_PROTECTION) {
    header('X-Frame-Options: DENY');
}
if (ENABLE_XSS_PROTECTION) {
    header('X-XSS-Protection: 1; mode=block');
}
if (ENABLE_CONTENT_TYPE_SNIFFING_PROTECTION) {
    header('X-Content-Type-Options: nosniff');
}

// إعداد HTTPS
if (FORCE_SSL && !isset($_SERVER['HTTPS'])) {
    $redirectURL = 'https://' . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
    header("Location: $redirectURL");
    exit();
}

// إعداد CORS
if (isset($_SERVER['HTTP_ORIGIN'])) {
    $allowedOrigins = explode(',', CORS_ALLOWED_ORIGINS);
    if (in_array($_SERVER['HTTP_ORIGIN'], $allowedOrigins)) {
        header('Access-Control-Allow-Origin: ' . $_SERVER['HTTP_ORIGIN']);
    }
}

header('Access-Control-Allow-Methods: ' . CORS_ALLOWED_METHODS);
header('Access-Control-Allow-Headers: ' . CORS_ALLOWED_HEADERS);

// إعداد وضع الصيانة
if (MAINTENANCE_MODE) {
    $allowedIPs = explode(',', MAINTENANCE_ALLOWED_IPS);
    $clientIP = $_SERVER['REMOTE_ADDR'] ?? '';
    
    if (!in_array($clientIP, $allowedIPs)) {
        http_response_code(503);
        echo MAINTENANCE_MESSAGE;
        exit();
    }
}

// تسجيل بداية الطلب
if (ENABLE_MONITORING) {
    $requestStart = microtime(true);
    register_shutdown_function(function() use ($requestStart) {
        $executionTime = microtime(true) - $requestStart;
        error_log("Request execution time: {$executionTime}s");
    });
}
?>
