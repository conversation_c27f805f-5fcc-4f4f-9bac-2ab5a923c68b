/**
 * ===================================
 * نظام الأمان والحماية - Security System
 * ===================================
 */

window.Security = {
    // إعدادات الأمان المحسنة
    config: {
        enableCSRFProtection: true,
        enableXSSProtection: true,
        enableInputSanitization: true,
        enableSQLInjectionProtection: true,
        enableRateLimiting: true,
        enableIPWhitelist: false,
        enableGeoBlocking: false,
        sessionTimeout: 30 * 60 * 1000, // 30 دقيقة
        maxLoginAttempts: 5,
        lockoutDuration: 15 * 60 * 1000, // 15 دقيقة
        passwordMinLength: 8,
        requireStrongPassword: true,
        enableAuditLog: true,
        enableEncryption: true,
        encryptionKey: 'qimat_alwaed_2024_secure_key',
        enableTwoFactor: false,
        enableBruteForceProtection: true,
        maxRequestsPerMinute: 60,
        enableContentSecurityPolicy: true,
        enableSecureHeaders: true
    },

    // بيانات الأمان
    data: {
        csrfToken: null,
        sessionId: null,
        loginAttempts: {},
        auditLog: [],
        blockedIPs: new Set(),
        suspiciousActivities: []
    },

    // حالة النظام المحسنة
    state: {
        isInitialized: false,
        lastSecurityCheck: null,
        securityLevel: 'high',
        threatLevel: 'low',
        activeThreats: [],
        blockedIPs: new Set(),
        rateLimitData: new Map(),
        encryptionEnabled: true,
        lastThreatScan: null,
        securityAlerts: []
    },

    /**
     * تهيئة نظام الأمان
     */
    init: function() {
        console.log('🔒 تهيئة نظام الأمان والحماية');
        
        try {
            // إنشاء CSRF Token
            this.generateCSRFToken();
            
            // إنشاء Session ID
            this.generateSessionId();
            
            // إعداد حماية XSS
            this.setupXSSProtection();
            
            // إعداد مراقبة الأنشطة المشبوهة
            this.setupSecurityMonitoring();
            
            // تحميل بيانات الأمان المحفوظة
            this.loadSecurityData();
            
            // بدء فحص الأمان الدوري
            this.startSecurityChecks();
            
            this.state.isInitialized = true;
            console.log('✅ تم تهيئة نظام الأمان بنجاح');
            
        } catch (error) {
            console.error('❌ خطأ في تهيئة نظام الأمان:', error);
        }
    },

    /**
     * إنشاء CSRF Token
     */
    generateCSRFToken: function() {
        this.data.csrfToken = this.generateSecureToken(32);
        
        // إضافة التوكن لجميع النماذج
        this.addCSRFTokenToForms();
        
        // حفظ التوكن في التخزين المحلي
        sessionStorage.setItem('csrf_token', this.data.csrfToken);
    },

    /**
     * إنشاء Session ID
     */
    generateSessionId: function() {
        this.data.sessionId = this.generateSecureToken(48);
        sessionStorage.setItem('session_id', this.data.sessionId);
    },

    /**
     * إنشاء توكن آمن
     */
    generateSecureToken: function(length = 32) {
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
        let token = '';
        
        for (let i = 0; i < length; i++) {
            token += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        
        return token + Date.now().toString(36);
    },

    /**
     * إضافة CSRF Token للنماذج
     */
    addCSRFTokenToForms: function() {
        if (!this.config.enableCSRFProtection) return;
        
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            // إزالة التوكن القديم إن وجد
            const oldToken = form.querySelector('input[name="csrf_token"]');
            if (oldToken) {
                oldToken.remove();
            }
            
            // إضافة التوكن الجديد
            const tokenInput = document.createElement('input');
            tokenInput.type = 'hidden';
            tokenInput.name = 'csrf_token';
            tokenInput.value = this.data.csrfToken;
            form.appendChild(tokenInput);
        });
    },

    /**
     * التحقق من CSRF Token
     */
    validateCSRFToken: function(token) {
        if (!this.config.enableCSRFProtection) return true;
        
        return token === this.data.csrfToken;
    },

    /**
     * إعداد حماية XSS
     */
    setupXSSProtection: function() {
        if (!this.config.enableXSSProtection) return;
        
        // مراقبة تغييرات DOM للكشف عن XSS
        const observer = new MutationObserver((mutations) => {
            mutations.forEach(mutation => {
                mutation.addedNodes.forEach(node => {
                    if (node.nodeType === Node.ELEMENT_NODE) {
                        this.scanForXSS(node);
                    }
                });
            });
        });
        
        observer.observe(document.body, {
            childList: true,
            subtree: true
        });
    },

    /**
     * فحص XSS في العنصر
     */
    scanForXSS: function(element) {
        const dangerousPatterns = [
            /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
            /javascript:/gi,
            /on\w+\s*=/gi,
            /<iframe\b[^>]*>/gi,
            /<object\b[^>]*>/gi,
            /<embed\b[^>]*>/gi
        ];
        
        const content = element.innerHTML;
        
        dangerousPatterns.forEach(pattern => {
            if (pattern.test(content)) {
                console.warn('⚠️ محتوى مشبوه تم اكتشافه:', element);
                this.logSecurityEvent('xss_attempt', {
                    element: element.tagName,
                    content: content.substring(0, 100)
                });
                
                // إزالة المحتوى المشبوه
                element.innerHTML = this.sanitizeHTML(content);
            }
        });
    },

    /**
     * تنظيف HTML من المحتوى الضار
     */
    sanitizeHTML: function(html) {
        if (!this.config.enableInputSanitization) return html;
        
        // إزالة العلامات الخطيرة
        const dangerousTags = ['script', 'iframe', 'object', 'embed', 'form', 'input'];
        let sanitized = html;
        
        dangerousTags.forEach(tag => {
            const regex = new RegExp(`<${tag}\\b[^>]*>.*?<\\/${tag}>`, 'gi');
            sanitized = sanitized.replace(regex, '');
            
            const selfClosingRegex = new RegExp(`<${tag}\\b[^>]*\\/?>`, 'gi');
            sanitized = sanitized.replace(selfClosingRegex, '');
        });
        
        // إزالة الأحداث JavaScript
        sanitized = sanitized.replace(/on\w+\s*=\s*["'][^"']*["']/gi, '');
        sanitized = sanitized.replace(/javascript:/gi, '');
        
        return sanitized;
    },

    /**
     * تنظيف النص من المحتوى الضار
     */
    sanitizeInput: function(input) {
        if (!this.config.enableInputSanitization) return input;
        
        if (typeof input !== 'string') return input;
        
        return input
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#x27;')
            .replace(/\//g, '&#x2F;');
    },

    /**
     * إعداد مراقبة الأمان
     */
    setupSecurityMonitoring: function() {
        // مراقبة محاولات تسجيل الدخول
        document.addEventListener('login:attempt', (event) => {
            this.trackLoginAttempt(event.detail);
        });
        
        // مراقبة الأنشطة المشبوهة
        document.addEventListener('click', (event) => {
            this.monitorUserActivity(event);
        });
        
        // مراقبة تغيير URL
        window.addEventListener('popstate', (event) => {
            this.validateNavigation(event);
        });
    },

    /**
     * تتبع محاولات تسجيل الدخول
     */
    trackLoginAttempt: function(attemptData) {
        const ip = this.getClientIP();
        const username = attemptData.username;
        
        if (!this.data.loginAttempts[ip]) {
            this.data.loginAttempts[ip] = {
                count: 0,
                lastAttempt: null,
                lockedUntil: null
            };
        }
        
        const attempts = this.data.loginAttempts[ip];
        
        if (attemptData.success) {
            // نجح تسجيل الدخول - إعادة تعيين المحاولات
            attempts.count = 0;
            attempts.lockedUntil = null;
        } else {
            // فشل تسجيل الدخول
            attempts.count++;
            attempts.lastAttempt = Date.now();
            
            if (attempts.count >= this.config.maxLoginAttempts) {
                attempts.lockedUntil = Date.now() + this.config.lockoutDuration;
                this.data.blockedIPs.add(ip);
                
                this.logSecurityEvent('account_lockout', {
                    ip: ip,
                    username: username,
                    attempts: attempts.count
                });
            }
        }
        
        this.saveSecurityData();
    },

    /**
     * فحص ما إذا كان IP محظور
     */
    isIPBlocked: function(ip = null) {
        ip = ip || this.getClientIP();
        
        const attempts = this.data.loginAttempts[ip];
        if (!attempts) return false;
        
        if (attempts.lockedUntil && Date.now() < attempts.lockedUntil) {
            return true;
        }
        
        // إزالة الحظر إذا انتهت المدة
        if (attempts.lockedUntil && Date.now() >= attempts.lockedUntil) {
            attempts.lockedUntil = null;
            attempts.count = 0;
            this.data.blockedIPs.delete(ip);
            this.saveSecurityData();
        }
        
        return false;
    },

    /**
     * مراقبة نشاط المستخدم
     */
    monitorUserActivity: function(event) {
        // فحص الأنشطة المشبوهة
        const suspiciousPatterns = [
            // النقر السريع المتكرر
            { type: 'rapid_clicking', threshold: 10, timeWindow: 1000 },
            // محاولة الوصول لعناصر مخفية
            { type: 'hidden_element_access', pattern: /display:\s*none|visibility:\s*hidden/ }
        ];
        
        // يمكن إضافة منطق مراقبة أكثر تفصيلاً هنا
    },

    /**
     * التحقق من صحة التنقل
     */
    validateNavigation: function(event) {
        // فحص محاولات التنقل المشبوهة
        const currentURL = window.location.href;
        
        // فحص URL للكشف عن محاولات الحقن
        if (this.containsSuspiciousContent(currentURL)) {
            this.logSecurityEvent('suspicious_navigation', {
                url: currentURL,
                timestamp: Date.now()
            });
            
            // إعادة توجيه لصفحة آمنة
            window.location.href = '/';
        }
    },

    /**
     * فحص المحتوى المشبوه
     */
    containsSuspiciousContent: function(content) {
        const suspiciousPatterns = [
            /<script/i,
            /javascript:/i,
            /data:text\/html/i,
            /vbscript:/i,
            /%3Cscript/i,
            /&lt;script/i
        ];
        
        return suspiciousPatterns.some(pattern => pattern.test(content));
    },

    /**
     * تسجيل حدث أمني
     */
    logSecurityEvent: function(type, data) {
        if (!this.config.enableAuditLog) return;
        
        const event = {
            id: this.generateSecureToken(16),
            type: type,
            data: data,
            timestamp: new Date().toISOString(),
            ip: this.getClientIP(),
            userAgent: navigator.userAgent,
            sessionId: this.data.sessionId
        };
        
        this.data.auditLog.push(event);
        
        // الاحتفاظ بآخر 1000 حدث فقط
        if (this.data.auditLog.length > 1000) {
            this.data.auditLog = this.data.auditLog.slice(-1000);
        }
        
        console.warn(`🔒 حدث أمني: ${type}`, data);
        
        // حفظ السجل
        this.saveSecurityData();
    },

    /**
     * الحصول على IP العميل (محاكاة)
     */
    getClientIP: function() {
        // في بيئة حقيقية، سيتم الحصول على IP من الخادم
        return 'localhost';
    },

    /**
     * بدء فحوصات الأمان الدورية
     */
    startSecurityChecks: function() {
        // فحص كل 5 دقائق
        setInterval(() => {
            this.performSecurityCheck();
        }, 5 * 60 * 1000);
        
        // فحص انتهاء الجلسة كل دقيقة
        setInterval(() => {
            this.checkSessionTimeout();
        }, 60 * 1000);
    },

    /**
     * تنفيذ فحص أمني
     */
    performSecurityCheck: function() {
        this.state.lastSecurityCheck = Date.now();
        
        // فحص سلامة التوكنات
        this.validateTokens();
        
        // فحص الأنشطة المشبوهة
        this.analyzeSuspiciousActivities();
        
        // تنظيف البيانات القديمة
        this.cleanupOldData();
    },

    /**
     * فحص انتهاء الجلسة
     */
    checkSessionTimeout: function() {
        const lastActivity = localStorage.getItem('last_activity');
        if (lastActivity) {
            const timeSinceLastActivity = Date.now() - parseInt(lastActivity);
            
            if (timeSinceLastActivity > this.config.sessionTimeout) {
                this.logSecurityEvent('session_timeout', {
                    lastActivity: new Date(parseInt(lastActivity)).toISOString()
                });
                
                // تسجيل خروج تلقائي
                if (window.Auth && window.Auth.logout) {
                    window.Auth.logout();
                }
            }
        }
    },

    /**
     * التحقق من صحة التوكنات
     */
    validateTokens: function() {
        // التحقق من CSRF Token
        const storedToken = sessionStorage.getItem('csrf_token');
        if (storedToken !== this.data.csrfToken) {
            this.generateCSRFToken();
        }
        
        // التحقق من Session ID
        const storedSessionId = sessionStorage.getItem('session_id');
        if (storedSessionId !== this.data.sessionId) {
            this.generateSessionId();
        }
    },

    /**
     * تحليل الأنشطة المشبوهة
     */
    analyzeSuspiciousActivities: function() {
        // تحليل سجل الأحداث للكشف عن أنماط مشبوهة
        const recentEvents = this.data.auditLog.filter(event => {
            return Date.now() - new Date(event.timestamp).getTime() < 60 * 60 * 1000; // آخر ساعة
        });
        
        // فحص تكرار الأحداث المشبوهة
        const eventCounts = {};
        recentEvents.forEach(event => {
            eventCounts[event.type] = (eventCounts[event.type] || 0) + 1;
        });
        
        // إنذار إذا كان هناك نشاط مشبوه مكثف
        Object.keys(eventCounts).forEach(eventType => {
            if (eventCounts[eventType] > 10) {
                this.logSecurityEvent('suspicious_pattern_detected', {
                    eventType: eventType,
                    count: eventCounts[eventType],
                    timeWindow: '1 hour'
                });
            }
        });
    },

    /**
     * تنظيف البيانات القديمة
     */
    cleanupOldData: function() {
        const oneWeekAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
        
        // تنظيف سجل الأحداث القديمة
        this.data.auditLog = this.data.auditLog.filter(event => {
            return new Date(event.timestamp).getTime() > oneWeekAgo;
        });
        
        // تنظيف محاولات تسجيل الدخول القديمة
        Object.keys(this.data.loginAttempts).forEach(ip => {
            const attempts = this.data.loginAttempts[ip];
            if (attempts.lastAttempt && attempts.lastAttempt < oneWeekAgo) {
                delete this.data.loginAttempts[ip];
            }
        });
        
        this.saveSecurityData();
    },

    /**
     * حفظ بيانات الأمان
     */
    saveSecurityData: function() {
        try {
            const dataToSave = {
                loginAttempts: this.data.loginAttempts,
                auditLog: this.data.auditLog.slice(-100), // حفظ آخر 100 حدث فقط
                blockedIPs: Array.from(this.data.blockedIPs),
                timestamp: Date.now()
            };
            
            localStorage.setItem('security_data', JSON.stringify(dataToSave));
        } catch (error) {
            console.error('❌ خطأ في حفظ بيانات الأمان:', error);
        }
    },

    /**
     * تحميل بيانات الأمان
     */
    loadSecurityData: function() {
        try {
            const savedData = localStorage.getItem('security_data');
            if (savedData) {
                const parsed = JSON.parse(savedData);
                
                this.data.loginAttempts = parsed.loginAttempts || {};
                this.data.auditLog = parsed.auditLog || [];
                this.data.blockedIPs = new Set(parsed.blockedIPs || []);
                
                console.log('📦 تم تحميل بيانات الأمان');
            }
        } catch (error) {
            console.error('❌ خطأ في تحميل بيانات الأمان:', error);
        }
    },

    /**
     * الحصول على تقرير الأمان
     */
    getSecurityReport: function() {
        return {
            timestamp: new Date().toISOString(),
            securityLevel: this.state.securityLevel,
            stats: {
                totalEvents: this.data.auditLog.length,
                blockedIPs: this.data.blockedIPs.size,
                activeAttempts: Object.keys(this.data.loginAttempts).length
            },
            recentEvents: this.data.auditLog.slice(-10),
            recommendations: this.getSecurityRecommendations()
        };
    },

    /**
     * الحصول على توصيات الأمان
     */
    getSecurityRecommendations: function() {
        const recommendations = [];
        
        // فحص قوة كلمة المرور
        if (!this.config.requireStrongPassword) {
            recommendations.push({
                type: 'password_policy',
                message: 'يُنصح بتفعيل سياسة كلمات المرور القوية',
                priority: 'medium'
            });
        }
        
        // فحص عدد الأحداث الأمنية
        if (this.data.auditLog.length > 500) {
            recommendations.push({
                type: 'security_events',
                message: 'عدد كبير من الأحداث الأمنية - يُنصح بالمراجعة',
                priority: 'high'
            });
        }
        
        return recommendations;
    },

    /**
     * تصدير بيانات الأمان
     */
    exportSecurityData: function() {
        const exportData = {
            timestamp: new Date().toISOString(),
            report: this.getSecurityReport(),
            config: this.config,
            auditLog: this.data.auditLog
        };
        
        return JSON.stringify(exportData, null, 2);
    }
};

// تصدير نظام الأمان للاستخدام العام
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Security;
}
