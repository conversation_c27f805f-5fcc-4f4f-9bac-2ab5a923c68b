/**
 * ===================================
 * نظام إدارة المستخدمين المتقدم - Advanced User Management System
 * ===================================
 */

window.UserManagement = {
    // إعدادات النظام
    config: {
        defaultSubscriptionDays: 30,
        maxSubscriptionDays: 365,
        passwordMinLength: 8,
        maxLoginAttempts: 5,
        lockoutDuration: 15 * 60 * 1000, // 15 دقيقة
        sessionTimeout: 8 * 60 * 60 * 1000 // 8 ساعات
    },

    // حالة النظام
    state: {
        users: [],
        permissions: [],
        currentUser: null,
        isLoading: false,
        filters: {
            role: '',
            status: '',
            subscription: ''
        }
    },

    // الصلاحيات المتاحة
    availablePermissions: {
        modules: [
            { id: 'dashboard', name: 'لوحة التحكم', description: 'الوصول للوحة التحكم الرئيسية' },
            { id: 'customers', name: 'إدارة العملاء', description: 'إدارة بيانات العملاء والحجوزات' },
            { id: 'suppliers', name: 'إدارة الموردين', description: 'إدارة بيانات الموردين والمدفوعات' },
            { id: 'agents', name: 'إدارة الوكلاء', description: 'إدارة بيانات الوكلاء والعمولات' },
            { id: 'bookings', name: 'إدارة الحجوزات', description: 'إنشاء وإدارة الحجوزات' },
            { id: 'accounting', name: 'النظام المحاسبي', description: 'إدارة الحسابات والقيود المحاسبية' },
            { id: 'reports', name: 'التقارير', description: 'عرض وتصدير التقارير' },
            { id: 'settings', name: 'الإعدادات', description: 'إدارة إعدادات النظام' },
            { id: 'user_management', name: 'إدارة المستخدمين', description: 'إضافة وإدارة المستخدمين' }
        ],
        actions: ['read', 'write', 'update', 'delete', 'export', 'print']
    },

    /**
     * تهيئة نظام إدارة المستخدمين
     */
    init: function() {
        console.log('🔄 تهيئة نظام إدارة المستخدمين');
        
        try {
            // تحميل البيانات
            this.loadUsers();
            this.loadPermissions();
            
            // تهيئة الأحداث
            this.initEvents();
            
            console.log('✅ تم تهيئة نظام إدارة المستخدمين بنجاح');
            
        } catch (error) {
            console.error('❌ خطأ في تهيئة نظام إدارة المستخدمين:', error);
        }
    },

    /**
     * تحميل قائمة المستخدمين
     */
    loadUsers: function() {
        this.state.isLoading = true;
        
        try {
            // محاكاة تحميل البيانات من قاعدة البيانات
            const users = this.getUsersFromStorage();
            this.state.users = users;
            
            // تحديث الواجهة
            this.renderUsersList();
            
        } catch (error) {
            console.error('خطأ في تحميل المستخدمين:', error);
            this.showNotification('خطأ في تحميل بيانات المستخدمين', 'error');
        } finally {
            this.state.isLoading = false;
        }
    },

    /**
     * تحميل الصلاحيات
     */
    loadPermissions: function() {
        try {
            // محاكاة تحميل الصلاحيات من قاعدة البيانات
            const permissions = this.getPermissionsFromStorage();
            this.state.permissions = permissions;
            
        } catch (error) {
            console.error('خطأ في تحميل الصلاحيات:', error);
        }
    },

    /**
     * عرض واجهة إدارة المستخدمين
     */
    show: function() {
        const content = `
            <div class="user-management-container">
                <div class="page-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h2><i class="fas fa-users me-2"></i>إدارة المستخدمين</h2>
                            <p class="text-muted">إدارة المستخدمين والصلاحيات المحددة بالأيام</p>
                        </div>
                        <button class="btn btn-primary" onclick="UserManagement.showAddUserModal()">
                            <i class="fas fa-user-plus me-2"></i>إضافة مستخدم جديد
                        </button>
                    </div>
                </div>

                <!-- فلاتر البحث -->
                <div class="card mb-4">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-3">
                                <label class="form-label">البحث</label>
                                <input type="text" class="form-control" id="userSearchInput" 
                                       placeholder="البحث بالاسم أو البريد الإلكتروني">
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">الدور</label>
                                <select class="form-select" id="roleFilter">
                                    <option value="">جميع الأدوار</option>
                                    <option value="admin">مدير</option>
                                    <option value="accountant">محاسب</option>
                                    <option value="agent">وكيل</option>
                                    <option value="employee">موظف</option>
                                    <option value="viewer">مشاهد</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">الحالة</label>
                                <select class="form-select" id="statusFilter">
                                    <option value="">جميع الحالات</option>
                                    <option value="active">نشط</option>
                                    <option value="inactive">غير نشط</option>
                                    <option value="expired">منتهي الصلاحية</option>
                                </select>
                            </div>
                            <div class="col-md-2">
                                <label class="form-label">الاشتراك</label>
                                <select class="form-select" id="subscriptionFilter">
                                    <option value="">جميع الاشتراكات</option>
                                    <option value="active">نشط</option>
                                    <option value="expiring">ينتهي قريباً</option>
                                    <option value="expired">منتهي</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label class="form-label">&nbsp;</label>
                                <div class="d-flex gap-2">
                                    <button class="btn btn-outline-primary" onclick="UserManagement.applyFilters()">
                                        <i class="fas fa-search"></i> بحث
                                    </button>
                                    <button class="btn btn-outline-secondary" onclick="UserManagement.clearFilters()">
                                        <i class="fas fa-times"></i> مسح
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- قائمة المستخدمين -->
                <div class="card">
                    <div class="card-body">
                        <div id="usersTableContainer">
                            ${this.renderUsersTable()}
                        </div>
                    </div>
                </div>
            </div>

            <!-- نافذة إضافة/تعديل مستخدم -->
            ${this.renderUserModal()}
            
            <!-- نافذة إدارة الصلاحيات -->
            ${this.renderPermissionsModal()}
        `;

        // عرض المحتوى
        if (window.UI && window.UI.showPage) {
            window.UI.showPage(content);
        } else {
            document.getElementById('main-content').innerHTML = content;
        }

        // تهيئة الأحداث
        this.initEvents();
    },

    /**
     * عرض جدول المستخدمين
     */
    renderUsersTable: function() {
        const users = this.getFilteredUsers();
        
        if (users.length === 0) {
            return `
                <div class="text-center py-5">
                    <i class="fas fa-users fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">لا توجد مستخدمين</h5>
                    <p class="text-muted">ابدأ بإضافة مستخدم جديد</p>
                </div>
            `;
        }

        let tableHTML = `
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>المستخدم</th>
                            <th>الدور</th>
                            <th>الاشتراك</th>
                            <th>آخر دخول</th>
                            <th>الحالة</th>
                            <th>الإجراءات</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        users.forEach(user => {
            const subscriptionStatus = this.getSubscriptionStatus(user);
            const statusBadge = this.getStatusBadge(user);
            
            tableHTML += `
                <tr>
                    <td>
                        <div class="d-flex align-items-center">
                            <div class="avatar-sm me-3">
                                <div class="avatar-title bg-primary rounded-circle">
                                    ${user.full_name.charAt(0)}
                                </div>
                            </div>
                            <div>
                                <h6 class="mb-0">${user.full_name}</h6>
                                <small class="text-muted">${user.email}</small>
                            </div>
                        </div>
                    </td>
                    <td>
                        <span class="badge bg-info">${this.getRoleDisplayName(user.role)}</span>
                    </td>
                    <td>
                        <div>
                            <small class="text-muted">من: ${this.formatDate(user.subscription_start_date)}</small><br>
                            <small class="text-muted">إلى: ${this.formatDate(user.subscription_end_date)}</small><br>
                            <span class="badge ${subscriptionStatus.class}">${subscriptionStatus.text}</span>
                        </div>
                    </td>
                    <td>
                        <small class="text-muted">
                            ${user.last_login ? this.formatDateTime(user.last_login) : 'لم يسجل دخول'}
                        </small>
                    </td>
                    <td>${statusBadge}</td>
                    <td>
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="UserManagement.editUser(${user.id})" 
                                    title="تعديل">
                                <i class="fas fa-edit"></i>
                            </button>
                            <button class="btn btn-outline-success" onclick="UserManagement.managePermissions(${user.id})" 
                                    title="إدارة الصلاحيات">
                                <i class="fas fa-key"></i>
                            </button>
                            <button class="btn btn-outline-warning" onclick="UserManagement.resetPassword(${user.id})" 
                                    title="إعادة تعيين كلمة المرور">
                                <i class="fas fa-lock"></i>
                            </button>
                            ${user.is_active ? 
                                `<button class="btn btn-outline-danger" onclick="UserManagement.deactivateUser(${user.id})" title="إلغاء التفعيل">
                                    <i class="fas fa-ban"></i>
                                </button>` :
                                `<button class="btn btn-outline-success" onclick="UserManagement.activateUser(${user.id})" title="تفعيل">
                                    <i class="fas fa-check"></i>
                                </button>`
                            }
                        </div>
                    </td>
                </tr>
            `;
        });

        tableHTML += `
                    </tbody>
                </table>
            </div>
        `;

        return tableHTML;
    },

    /**
     * عرض نافذة إضافة مستخدم جديد
     */
    showAddUserModal: function() {
        this.state.currentUser = null;
        this.showUserModal();
    },

    /**
     * تعديل مستخدم
     */
    editUser: function(userId) {
        const user = this.state.users.find(u => u.id === userId);
        if (user) {
            this.state.currentUser = user;
            this.showUserModal();
        }
    },

    /**
     * عرض نافذة المستخدم (إضافة/تعديل)
     */
    showUserModal: function() {
        const isEdit = this.state.currentUser !== null;
        const user = this.state.currentUser || {};

        const modalHTML = `
            <div class="modal fade" id="userModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-user me-2"></i>
                                ${isEdit ? 'تعديل مستخدم' : 'إضافة مستخدم جديد'}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="userForm">
                                <div class="row">
                                    <!-- البيانات الأساسية -->
                                    <div class="col-md-6">
                                        <h6 class="text-primary mb-3">البيانات الأساسية</h6>

                                        <div class="mb-3">
                                            <label class="form-label">الاسم الكامل <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="fullName"
                                                   value="${user.full_name || ''}" required>
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label">اسم المستخدم <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="username"
                                                   value="${user.username || ''}" required>
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label">البريد الإلكتروني <span class="text-danger">*</span></label>
                                            <input type="email" class="form-control" id="email"
                                                   value="${user.email || ''}" required>
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label">رقم الهاتف</label>
                                            <input type="tel" class="form-control" id="phone"
                                                   value="${user.phone || ''}">
                                        </div>

                                        ${!isEdit ? `
                                        <div class="mb-3">
                                            <label class="form-label">كلمة المرور <span class="text-danger">*</span></label>
                                            <div class="input-group">
                                                <input type="password" class="form-control" id="password" required>
                                                <button type="button" class="btn btn-outline-secondary"
                                                        onclick="UserManagement.togglePasswordVisibility('password')">
                                                    <i class="fas fa-eye"></i>
                                                </button>
                                            </div>
                                            <small class="text-muted">يجب أن تكون 8 أحرف على الأقل</small>
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label">تأكيد كلمة المرور <span class="text-danger">*</span></label>
                                            <input type="password" class="form-control" id="confirmPassword" required>
                                        </div>
                                        ` : ''}
                                    </div>

                                    <!-- الصلاحيات والاشتراك -->
                                    <div class="col-md-6">
                                        <h6 class="text-primary mb-3">الصلاحيات والاشتراك</h6>

                                        <div class="mb-3">
                                            <label class="form-label">الدور <span class="text-danger">*</span></label>
                                            <select class="form-select" id="role" required>
                                                <option value="">اختر الدور</option>
                                                <option value="admin" ${user.role === 'admin' ? 'selected' : ''}>مدير</option>
                                                <option value="accountant" ${user.role === 'accountant' ? 'selected' : ''}>محاسب</option>
                                                <option value="agent" ${user.role === 'agent' ? 'selected' : ''}>وكيل</option>
                                                <option value="employee" ${user.role === 'employee' ? 'selected' : ''}>موظف</option>
                                                <option value="viewer" ${user.role === 'viewer' ? 'selected' : ''}>مشاهد</option>
                                            </select>
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label">تاريخ بداية الاشتراك <span class="text-danger">*</span></label>
                                            <input type="date" class="form-control" id="subscriptionStartDate"
                                                   value="${user.subscription_start_date || new Date().toISOString().split('T')[0]}" required>
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label">مدة الاشتراك (بالأيام) <span class="text-danger">*</span></label>
                                            <input type="number" class="form-control" id="subscriptionDays"
                                                   value="${user.subscription_days || this.config.defaultSubscriptionDays}"
                                                   min="1" max="${this.config.maxSubscriptionDays}" required>
                                            <small class="text-muted">الحد الأقصى ${this.config.maxSubscriptionDays} يوم</small>
                                        </div>

                                        <div class="mb-3">
                                            <label class="form-label">تاريخ انتهاء الاشتراك</label>
                                            <input type="date" class="form-control" id="subscriptionEndDate"
                                                   value="${user.subscription_end_date || ''}" readonly>
                                        </div>

                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input type="checkbox" class="form-check-input" id="isActive"
                                                       ${user.is_active !== false ? 'checked' : ''}>
                                                <label class="form-check-label" for="isActive">
                                                    حساب نشط
                                                </label>
                                            </div>
                                        </div>

                                        <div class="mb-3">
                                            <div class="form-check">
                                                <input type="checkbox" class="form-check-input" id="twoFactorEnabled"
                                                       ${user.two_factor_enabled ? 'checked' : ''}>
                                                <label class="form-check-label" for="twoFactorEnabled">
                                                    تفعيل المصادقة الثنائية
                                                </label>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- الصلاحيات السريعة -->
                                <div class="row mt-4">
                                    <div class="col-12">
                                        <h6 class="text-primary mb-3">الصلاحيات السريعة</h6>
                                        <div class="row">
                                            ${this.renderQuickPermissions(user)}
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                            <button type="button" class="btn btn-primary" onclick="UserManagement.saveUser()">
                                <i class="fas fa-save me-2"></i>
                                ${isEdit ? 'تحديث' : 'إضافة'}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // إضافة النافذة للصفحة
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // عرض النافذة
        const modal = new bootstrap.Modal(document.getElementById('userModal'));
        modal.show();

        // تهيئة الأحداث
        this.initUserModalEvents();

        // إزالة النافذة عند الإغلاق
        document.getElementById('userModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * عرض الصلاحيات السريعة
     */
    renderQuickPermissions: function(user) {
        let html = '';

        this.availablePermissions.modules.forEach(module => {
            const isChecked = user.permissions && user.permissions.includes(module.id);

            html += `
                <div class="col-md-4 mb-2">
                    <div class="form-check">
                        <input type="checkbox" class="form-check-input permission-checkbox"
                               id="perm_${module.id}" value="${module.id}" ${isChecked ? 'checked' : ''}>
                        <label class="form-check-label" for="perm_${module.id}">
                            ${module.name}
                        </label>
                    </div>
                </div>
            `;
        });

        return html;
    },

    /**
     * تهيئة أحداث نافذة المستخدم
     */
    initUserModalEvents: function() {
        // حساب تاريخ انتهاء الاشتراك تلقائياً
        const startDateInput = document.getElementById('subscriptionStartDate');
        const daysInput = document.getElementById('subscriptionDays');
        const endDateInput = document.getElementById('subscriptionEndDate');

        const calculateEndDate = () => {
            const startDate = new Date(startDateInput.value);
            const days = parseInt(daysInput.value) || 0;

            if (startDate && days > 0) {
                const endDate = new Date(startDate);
                endDate.setDate(endDate.getDate() + days);
                endDateInput.value = endDate.toISOString().split('T')[0];
            }
        };

        startDateInput.addEventListener('change', calculateEndDate);
        daysInput.addEventListener('input', calculateEndDate);

        // حساب التاريخ عند التحميل
        calculateEndDate();
    },

    /**
     * حفظ المستخدم
     */
    saveUser: function() {
        const form = document.getElementById('userForm');
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        const isEdit = this.state.currentUser !== null;
        const userData = this.getUserDataFromForm();

        // التحقق من صحة البيانات
        const validation = this.validateUserData(userData, isEdit);
        if (!validation.isValid) {
            this.showNotification(validation.message, 'error');
            return;
        }

        try {
            if (isEdit) {
                this.updateUser(userData);
            } else {
                this.createUser(userData);
            }

            // إغلاق النافذة
            const modal = bootstrap.Modal.getInstance(document.getElementById('userModal'));
            modal.hide();

            // تحديث القائمة
            this.loadUsers();

            this.showNotification(
                isEdit ? 'تم تحديث المستخدم بنجاح' : 'تم إضافة المستخدم بنجاح',
                'success'
            );

        } catch (error) {
            console.error('خطأ في حفظ المستخدم:', error);
            this.showNotification('حدث خطأ أثناء حفظ البيانات', 'error');
        }
    },

    /**
     * الحصول على بيانات المستخدم من النموذج
     */
    getUserDataFromForm: function() {
        const selectedPermissions = Array.from(document.querySelectorAll('.permission-checkbox:checked'))
            .map(cb => cb.value);

        return {
            fullName: document.getElementById('fullName').value.trim(),
            username: document.getElementById('username').value.trim(),
            email: document.getElementById('email').value.trim(),
            phone: document.getElementById('phone').value.trim(),
            password: document.getElementById('password')?.value,
            role: document.getElementById('role').value,
            subscriptionStartDate: document.getElementById('subscriptionStartDate').value,
            subscriptionDays: parseInt(document.getElementById('subscriptionDays').value),
            subscriptionEndDate: document.getElementById('subscriptionEndDate').value,
            isActive: document.getElementById('isActive').checked,
            twoFactorEnabled: document.getElementById('twoFactorEnabled').checked,
            permissions: selectedPermissions
        };
    },

    /**
     * التحقق من صحة بيانات المستخدم
     */
    validateUserData: function(userData, isEdit) {
        // التحقق من الحقول المطلوبة
        if (!userData.fullName || !userData.username || !userData.email || !userData.role) {
            return { isValid: false, message: 'يرجى ملء جميع الحقول المطلوبة' };
        }

        // التحقق من كلمة المرور للمستخدمين الجدد
        if (!isEdit && (!userData.password || userData.password.length < this.config.passwordMinLength)) {
            return { isValid: false, message: `كلمة المرور يجب أن تكون ${this.config.passwordMinLength} أحرف على الأقل` };
        }

        // التحقق من تأكيد كلمة المرور
        if (!isEdit) {
            const confirmPassword = document.getElementById('confirmPassword').value;
            if (userData.password !== confirmPassword) {
                return { isValid: false, message: 'كلمة المرور وتأكيدها غير متطابقين' };
            }
        }

        // التحقق من صحة البريد الإلكتروني
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(userData.email)) {
            return { isValid: false, message: 'البريد الإلكتروني غير صحيح' };
        }

        // التحقق من تفرد اسم المستخدم
        const existingUser = this.state.users.find(u =>
            u.username === userData.username &&
            (!isEdit || u.id !== this.state.currentUser.id)
        );
        if (existingUser) {
            return { isValid: false, message: 'اسم المستخدم موجود مسبقاً' };
        }

        // التحقق من تفرد البريد الإلكتروني
        const existingEmail = this.state.users.find(u =>
            u.email === userData.email &&
            (!isEdit || u.id !== this.state.currentUser.id)
        );
        if (existingEmail) {
            return { isValid: false, message: 'البريد الإلكتروني موجود مسبقاً' };
        }

        return { isValid: true };
    },

    /**
     * إنشاء مستخدم جديد
     */
    createUser: function(userData) {
        const newUser = {
            id: Date.now(),
            fullName: userData.fullName,
            username: userData.username,
            email: userData.email,
            phone: userData.phone,
            role: userData.role,
            status: userData.isActive ? 'active' : 'inactive',
            department: this.getDepartmentByRole(userData.role),
            subscriptionStartDate: new Date(userData.subscriptionStartDate),
            subscriptionDays: userData.subscriptionDays,
            subscriptionEndDate: new Date(userData.subscriptionEndDate),
            twoFactorEnabled: userData.twoFactorEnabled,
            permissions: userData.permissions,
            createdAt: new Date(),
            lastActivity: new Date(),
            loginAttempts: 0,
            isOnline: false,
            avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(userData.fullName)}&background=2c5aa0&color=fff`
        };

        this.state.users.push(newUser);
    },

    /**
     * تحديث مستخدم موجود
     */
    updateUser: function(userData) {
        const userIndex = this.state.users.findIndex(u => u.id === this.state.currentUser.id);
        if (userIndex !== -1) {
            this.state.users[userIndex] = {
                ...this.state.users[userIndex],
                fullName: userData.fullName,
                username: userData.username,
                email: userData.email,
                phone: userData.phone,
                role: userData.role,
                status: userData.isActive ? 'active' : 'inactive',
                department: this.getDepartmentByRole(userData.role),
                subscriptionStartDate: new Date(userData.subscriptionStartDate),
                subscriptionDays: userData.subscriptionDays,
                subscriptionEndDate: new Date(userData.subscriptionEndDate),
                twoFactorEnabled: userData.twoFactorEnabled,
                permissions: userData.permissions,
                avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(userData.fullName)}&background=2c5aa0&color=fff`
            };
        }
    },

    /**
     * الحصول على القسم حسب الدور
     */
    getDepartmentByRole: function(role) {
        const roleDepartmentMap = {
            admin: 'management',
            manager: 'management',
            accountant: 'accounting',
            sales: 'sales',
            employee: 'operations',
            viewer: 'support'
        };
        return roleDepartmentMap[role] || 'operations';
    },

    /**
     * تبديل رؤية كلمة المرور
     */
    togglePasswordVisibility: function(inputId) {
        const input = document.getElementById(inputId);
        const icon = input.nextElementSibling.querySelector('i');

        if (input.type === 'password') {
            input.type = 'text';
            icon.className = 'fas fa-eye-slash';
        } else {
            input.type = 'password';
            icon.className = 'fas fa-eye';
        }
    },

    /**
     * حذف مستخدم
     */
    deleteUser: function(userId) {
        if (confirm('هل أنت متأكد من حذف هذا المستخدم؟')) {
            this.state.users = this.state.users.filter(u => u.id !== userId);
            this.applyFilters();
            this.updateStatistics();
            this.renderUsers();
            this.showNotification('تم حذف المستخدم بنجاح', 'success');
        }
    },

    /**
     * عرض مستخدم
     */
    viewUser: function(userId) {
        const user = this.state.users.find(u => u.id === userId);
        if (!user) return;

        this.showUserDetailsModal(user);
    },

    /**
     * عرض نافذة تفاصيل المستخدم
     */
    showUserDetailsModal: function(user) {
        const modalHTML = `
            <div class="modal fade" id="userDetailsModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-user me-2"></i>
                                تفاصيل المستخدم
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-4 text-center mb-4">
                                    <img src="${user.avatar}" alt="${user.fullName}"
                                         class="rounded-circle mb-3" width="120" height="120">
                                    <h5>${user.fullName}</h5>
                                    <p class="text-muted">@${user.username}</p>
                                    <span class="badge bg-${this.getStatusBadgeColor(user.status)} fs-6">
                                        ${this.getStatusDisplayName(user.status)}
                                    </span>
                                </div>
                                <div class="col-md-8">
                                    <div class="row">
                                        <div class="col-sm-6 mb-3">
                                            <strong>البريد الإلكتروني:</strong><br>
                                            <span class="text-muted">${user.email}</span>
                                        </div>
                                        <div class="col-sm-6 mb-3">
                                            <strong>رقم الهاتف:</strong><br>
                                            <span class="text-muted">${user.phone || 'غير محدد'}</span>
                                        </div>
                                        <div class="col-sm-6 mb-3">
                                            <strong>الدور:</strong><br>
                                            <span class="badge bg-${this.getRoleBadgeColor(user.role)}">
                                                ${this.config.roles[user.role] || user.role}
                                            </span>
                                        </div>
                                        <div class="col-sm-6 mb-3">
                                            <strong>القسم:</strong><br>
                                            <span class="text-muted">${this.getDepartmentDisplayName(user.department)}</span>
                                        </div>
                                        <div class="col-sm-6 mb-3">
                                            <strong>تاريخ الإنشاء:</strong><br>
                                            <span class="text-muted">${this.formatDate(user.createdAt)}</span>
                                        </div>
                                        <div class="col-sm-6 mb-3">
                                            <strong>آخر نشاط:</strong><br>
                                            <span class="text-muted">${this.formatDate(user.lastActivity)}</span>
                                        </div>
                                        <div class="col-sm-6 mb-3">
                                            <strong>المصادقة الثنائية:</strong><br>
                                            <span class="badge bg-${user.twoFactorEnabled ? 'success' : 'secondary'}">
                                                ${user.twoFactorEnabled ? 'مفعلة' : 'غير مفعلة'}
                                            </span>
                                        </div>
                                        <div class="col-sm-6 mb-3">
                                            <strong>حالة الاتصال:</strong><br>
                                            <span class="badge bg-${user.isOnline ? 'success' : 'secondary'}">
                                                ${user.isOnline ? 'متصل' : 'غير متصل'}
                                            </span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <hr>

                            <div class="row">
                                <div class="col-12">
                                    <h6 class="text-primary mb-3">الصلاحيات</h6>
                                    <div class="row">
                                        ${user.permissions ? user.permissions.map(perm => `
                                            <div class="col-md-4 mb-2">
                                                <span class="badge bg-info">${perm}</span>
                                            </div>
                                        `).join('') : '<div class="col-12"><span class="text-muted">لا توجد صلاحيات محددة</span></div>'}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
                            <button type="button" class="btn btn-primary" onclick="UserManagement.editUser(${user.id}); bootstrap.Modal.getInstance(document.getElementById('userDetailsModal')).hide();">
                                <i class="fas fa-edit me-2"></i>تعديل
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // إزالة النافذة السابقة إن وجدت
        const existingModal = document.getElementById('userDetailsModal');
        if (existingModal) {
            existingModal.remove();
        }

        // إضافة النافذة الجديدة
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // إظهار النافذة
        const modal = new bootstrap.Modal(document.getElementById('userDetailsModal'));
        modal.show();

        // إزالة النافذة عند الإغلاق
        document.getElementById('userDetailsModal').addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * الوظائف المساعدة
     */
    getStatusBadgeColor: function(status) {
        const colors = {
            active: 'success',
            inactive: 'secondary',
            locked: 'danger',
            pending: 'warning'
        };
        return colors[status] || 'secondary';
    },

    getStatusDisplayName: function(status) {
        const names = {
            active: 'نشط',
            inactive: 'غير نشط',
            locked: 'مقفل',
            pending: 'في الانتظار'
        };
        return names[status] || status;
    },

    getRoleBadgeColor: function(role) {
        const colors = {
            admin: 'danger',
            manager: 'primary',
            accountant: 'info',
            sales: 'success',
            employee: 'warning',
            viewer: 'secondary'
        };
        return colors[role] || 'secondary';
    },

    getDepartmentDisplayName: function(department) {
        const names = {
            management: 'الإدارة',
            sales: 'المبيعات',
            accounting: 'المحاسبة',
            operations: 'العمليات',
            support: 'الدعم الفني'
        };
        return names[department] || department;
    },

    formatDate: function(date) {
        if (!date) return '-';
        return new Date(date).toLocaleDateString('ar-SA');
    },

    showNotification: function(message, type = 'info') {
        if (window.AdvancedApp && window.AdvancedApp.showNotification) {
            window.AdvancedApp.showNotification(message, type);
        } else {
            alert(message);
        }
    },

    exportUsers: function() {
        this.showNotification('جاري تصدير بيانات المستخدمين...', 'info');

        setTimeout(() => {
            this.showNotification('تم تصدير بيانات المستخدمين بنجاح', 'success');
        }, 2000);
    },

    bulkAction: function(action) {
        const selectedUsers = this.state.selectedUsers;
        if (selectedUsers.length === 0) {
            this.showNotification('يرجى تحديد مستخدمين للتنفيذ', 'warning');
            return;
        }

        let message = '';
        switch (action) {
            case 'activate':
                message = `تفعيل ${selectedUsers.length} مستخدم`;
                break;
            case 'deactivate':
                message = `إلغاء تفعيل ${selectedUsers.length} مستخدم`;
                break;
            case 'lock':
                message = `قفل ${selectedUsers.length} مستخدم`;
                break;
            case 'delete':
                message = `حذف ${selectedUsers.length} مستخدم`;
                break;
        }

        if (confirm(`هل أنت متأكد من ${message}؟`)) {
            // تنفيذ الإجراء
            selectedUsers.forEach(userId => {
                const userIndex = this.state.users.findIndex(u => u.id === userId);
                if (userIndex !== -1) {
                    if (action === 'delete') {
                        this.state.users.splice(userIndex, 1);
                    } else {
                        let status = 'active';
                        if (action === 'deactivate') status = 'inactive';
                        if (action === 'lock') status = 'locked';

                        this.state.users[userIndex].status = status;
                    }
                }
            });

            this.applyFilters();
            this.updateStatistics();
            this.renderUsers();
            this.showNotification(`تم ${message} بنجاح`, 'success');
        }
    },

    showRoleManagement: function() {
        this.showNotification('سيتم تطوير إدارة الأدوار قريباً', 'info');
    },

    showPermissions: function() {
        this.showNotification('سيتم تطوير إدارة الصلاحيات قريباً', 'info');
    },

    showSecuritySettings: function() {
        this.showNotification('سيتم تطوير إعدادات الأمان قريباً', 'info');
    },

    loadCurrentUser: function() {
        const userData = localStorage.getItem('currentUser');
        if (userData) {
            try {
                this.state.currentUser = JSON.parse(userData);
            } catch (error) {
                console.error('خطأ في تحميل بيانات المستخدم الحالي:', error);
            }
        }
    }
};
