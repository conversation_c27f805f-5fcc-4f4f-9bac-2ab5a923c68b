/**
 * ===================================
 * نظام التشفير المتقدم - Advanced Encryption System
 * ===================================
 */

window.Encryption = {
    // إعدادات التشفير
    config: {
        algorithm: 'AES-GCM',
        keyLength: 256,
        ivLength: 12,
        tagLength: 16,
        iterations: 100000,
        saltLength: 32,
        encoding: 'base64'
    },

    // مفاتيح التشفير
    keys: {
        masterKey: null,
        sessionKey: null,
        dataKey: null
    },

    // حالة النظام
    state: {
        isInitialized: false,
        encryptionEnabled: true,
        keyRotationInterval: 24 * 60 * 60 * 1000, // 24 ساعة
        lastKeyRotation: null
    },

    /**
     * تهيئة نظام التشفير
     */
    init: function() {
        console.log('🔄 تهيئة نظام التشفير');
        
        try {
            // توليد المفاتيح
            this.generateKeys();
            
            // تفعيل دوران المفاتيح
            this.startKeyRotation();
            
            this.state.isInitialized = true;
            console.log('✅ تم تهيئة نظام التشفير بنجاح');
            
        } catch (error) {
            console.error('❌ خطأ في تهيئة نظام التشفير:', error);
        }
    },

    /**
     * توليد المفاتيح
     */
    generateKeys: function() {
        try {
            // توليد المفتاح الرئيسي
            this.keys.masterKey = this.generateSecureKey();
            
            // توليد مفتاح الجلسة
            this.keys.sessionKey = this.generateSecureKey();
            
            // توليد مفتاح البيانات
            this.keys.dataKey = this.generateSecureKey();
            
            this.state.lastKeyRotation = new Date();
            
        } catch (error) {
            console.error('خطأ في توليد المفاتيح:', error);
            throw error;
        }
    },

    /**
     * توليد مفتاح آمن
     */
    generateSecureKey: function() {
        const array = new Uint8Array(32);
        crypto.getRandomValues(array);
        return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
    },

    /**
     * تشفير البيانات
     */
    encrypt: function(data, keyType = 'dataKey') {
        if (!this.state.encryptionEnabled) {
            return data;
        }

        try {
            const key = this.keys[keyType];
            if (!key) {
                throw new Error('مفتاح التشفير غير متوفر');
            }

            // تحويل البيانات إلى نص
            const plaintext = typeof data === 'string' ? data : JSON.stringify(data);
            
            // توليد IV عشوائي
            const iv = crypto.getRandomValues(new Uint8Array(this.config.ivLength));
            
            // تشفير البيانات (محاكاة - في التطبيق الحقيقي استخدم Web Crypto API)
            const encrypted = this.simpleEncrypt(plaintext, key, iv);
            
            // دمج IV مع البيانات المشفرة
            const combined = new Uint8Array(iv.length + encrypted.length);
            combined.set(iv);
            combined.set(encrypted, iv.length);
            
            // تحويل إلى base64
            return btoa(String.fromCharCode.apply(null, combined));
            
        } catch (error) {
            console.error('خطأ في تشفير البيانات:', error);
            throw error;
        }
    },

    /**
     * فك تشفير البيانات
     */
    decrypt: function(encryptedData, keyType = 'dataKey') {
        if (!this.state.encryptionEnabled) {
            return encryptedData;
        }

        try {
            const key = this.keys[keyType];
            if (!key) {
                throw new Error('مفتاح فك التشفير غير متوفر');
            }

            // تحويل من base64
            const combined = new Uint8Array(atob(encryptedData).split('').map(char => char.charCodeAt(0)));
            
            // استخراج IV
            const iv = combined.slice(0, this.config.ivLength);
            
            // استخراج البيانات المشفرة
            const encrypted = combined.slice(this.config.ivLength);
            
            // فك التشفير
            const decrypted = this.simpleDecrypt(encrypted, key, iv);
            
            // محاولة تحويل إلى JSON إذا أمكن
            try {
                return JSON.parse(decrypted);
            } catch {
                return decrypted;
            }
            
        } catch (error) {
            console.error('خطأ في فك تشفير البيانات:', error);
            throw error;
        }
    },

    /**
     * تشفير بسيط (محاكاة)
     */
    simpleEncrypt: function(plaintext, key, iv) {
        // هذه محاكاة بسيطة - في التطبيق الحقيقي استخدم Web Crypto API
        const keyBytes = new TextEncoder().encode(key);
        const plaintextBytes = new TextEncoder().encode(plaintext);
        const encrypted = new Uint8Array(plaintextBytes.length);
        
        for (let i = 0; i < plaintextBytes.length; i++) {
            encrypted[i] = plaintextBytes[i] ^ keyBytes[i % keyBytes.length] ^ iv[i % iv.length];
        }
        
        return encrypted;
    },

    /**
     * فك تشفير بسيط (محاكاة)
     */
    simpleDecrypt: function(encrypted, key, iv) {
        // هذه محاكاة بسيطة - في التطبيق الحقيقي استخدم Web Crypto API
        const keyBytes = new TextEncoder().encode(key);
        const decrypted = new Uint8Array(encrypted.length);
        
        for (let i = 0; i < encrypted.length; i++) {
            decrypted[i] = encrypted[i] ^ keyBytes[i % keyBytes.length] ^ iv[i % iv.length];
        }
        
        return new TextDecoder().decode(decrypted);
    },

    /**
     * تشفير كلمة المرور
     */
    hashPassword: function(password, salt = null) {
        try {
            // توليد salt إذا لم يتم توفيره
            if (!salt) {
                salt = this.generateSalt();
            }

            // محاكاة PBKDF2 (في التطبيق الحقيقي استخدم Web Crypto API)
            let hash = password + salt;
            for (let i = 0; i < this.config.iterations; i++) {
                hash = this.simpleHash(hash);
            }

            return {
                hash: hash,
                salt: salt,
                iterations: this.config.iterations
            };
            
        } catch (error) {
            console.error('خطأ في تشفير كلمة المرور:', error);
            throw error;
        }
    },

    /**
     * التحقق من كلمة المرور
     */
    verifyPassword: function(password, storedHash, salt) {
        try {
            const computed = this.hashPassword(password, salt);
            return computed.hash === storedHash;
            
        } catch (error) {
            console.error('خطأ في التحقق من كلمة المرور:', error);
            return false;
        }
    },

    /**
     * توليد salt
     */
    generateSalt: function() {
        const array = new Uint8Array(this.config.saltLength);
        crypto.getRandomValues(array);
        return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
    },

    /**
     * hash بسيط (محاكاة)
     */
    simpleHash: function(input) {
        let hash = 0;
        for (let i = 0; i < input.length; i++) {
            const char = input.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // تحويل إلى 32bit integer
        }
        return Math.abs(hash).toString(16);
    },

    /**
     * تشفير البيانات الحساسة
     */
    encryptSensitiveData: function(data) {
        return this.encrypt(data, 'masterKey');
    },

    /**
     * فك تشفير البيانات الحساسة
     */
    decryptSensitiveData: function(encryptedData) {
        return this.decrypt(encryptedData, 'masterKey');
    },

    /**
     * دوران المفاتيح
     */
    startKeyRotation: function() {
        setInterval(() => {
            this.rotateKeys();
        }, this.state.keyRotationInterval);
    },

    /**
     * تدوير المفاتيح
     */
    rotateKeys: function() {
        try {
            console.log('🔄 تدوير مفاتيح التشفير');
            
            // حفظ المفاتيح القديمة للبيانات الموجودة
            const oldKeys = { ...this.keys };
            
            // توليد مفاتيح جديدة
            this.generateKeys();
            
            // تحديث البيانات المشفرة (إذا لزم الأمر)
            this.updateEncryptedData(oldKeys);
            
            console.log('✅ تم تدوير المفاتيح بنجاح');
            
        } catch (error) {
            console.error('❌ خطأ في تدوير المفاتيح:', error);
        }
    },

    /**
     * تحديث البيانات المشفرة
     */
    updateEncryptedData: function(oldKeys) {
        // تحديث البيانات المشفرة بالمفاتيح الجديدة
        // هذا مثال بسيط - في التطبيق الحقيقي قم بتحديث جميع البيانات المشفرة
        try {
            const encryptedItems = ['user_sessions', 'sensitive_settings'];
            
            encryptedItems.forEach(item => {
                const data = localStorage.getItem(item);
                if (data) {
                    try {
                        // فك التشفير بالمفتاح القديم
                        const decrypted = this.decrypt(data, oldKeys.dataKey);
                        
                        // إعادة التشفير بالمفتاح الجديد
                        const reencrypted = this.encrypt(decrypted);
                        
                        // حفظ البيانات المحدثة
                        localStorage.setItem(item, reencrypted);
                        
                    } catch (error) {
                        console.warn(`تعذر تحديث البيانات المشفرة لـ ${item}:`, error);
                    }
                }
            });
            
        } catch (error) {
            console.error('خطأ في تحديث البيانات المشفرة:', error);
        }
    },

    /**
     * التحقق من سلامة البيانات
     */
    verifyDataIntegrity: function(data, checksum) {
        const computed = this.calculateChecksum(data);
        return computed === checksum;
    },

    /**
     * حساب checksum
     */
    calculateChecksum: function(data) {
        return this.simpleHash(JSON.stringify(data));
    },

    /**
     * تنظيف المفاتيح من الذاكرة
     */
    clearKeys: function() {
        this.keys.masterKey = null;
        this.keys.sessionKey = null;
        this.keys.dataKey = null;
    }
};
