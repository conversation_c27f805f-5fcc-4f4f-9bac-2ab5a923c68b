/**
 * ===================================
 * لوحة التحكم المتقدمة - Advanced Dashboard Component
 * قمة الوعد للسفريات
 * ===================================
 */

window.AdvancedDashboard = {
    // إعدادات لوحة التحكم
    config: {
        refreshInterval: 30000, // 30 ثانية
        animationDuration: 500,
        chartColors: {
            primary: '#2c5aa0',
            success: '#28a745',
            warning: '#ffc107',
            danger: '#dc3545',
            info: '#17a2b8'
        }
    },

    // حالة لوحة التحكم
    state: {
        isLoading: false,
        lastUpdate: null,
        activeFilters: {},
        selectedPeriod: 'month'
    },

    // بيانات لوحة التحكم
    data: {
        statistics: {},
        charts: {},
        recentActivities: [],
        notifications: []
    },

    /**
     * تهيئة لوحة التحكم المتقدمة
     */
    init: function() {
        console.log('🚀 تهيئة لوحة التحكم المتقدمة');
        
        try {
            // تحميل البيانات الأولية
            this.loadDashboardData();
            
            // تهيئة الأحداث
            this.initEvents();
            
            // بدء التحديث التلقائي
            this.startAutoRefresh();
            
            console.log('✅ تم تهيئة لوحة التحكم بنجاح');
            
        } catch (error) {
            console.error('❌ خطأ في تهيئة لوحة التحكم:', error);
        }
    },

    /**
     * عرض لوحة التحكم الرئيسية
     */
    show: function() {
        const content = `
            <div class="dashboard-container fade-in">
                <!-- Header Section -->
                <div class="dashboard-header mb-4">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h1 class="dashboard-title">
                                <i class="fas fa-tachometer-alt me-3"></i>
                                لوحة التحكم الرئيسية
                            </h1>
                            <p class="dashboard-subtitle text-muted">
                                مرحباً بك في نظام قمة الوعد للسفريات
                            </p>
                        </div>
                        <div class="col-md-6 text-md-end">
                            <div class="dashboard-controls">
                                <select class="form-select d-inline-block w-auto me-2" id="periodSelector">
                                    <option value="today">اليوم</option>
                                    <option value="week">هذا الأسبوع</option>
                                    <option value="month" selected>هذا الشهر</option>
                                    <option value="quarter">هذا الربع</option>
                                    <option value="year">هذا العام</option>
                                </select>
                                <button class="btn btn-primary-enhanced" onclick="AdvancedDashboard.refreshData()">
                                    <i class="fas fa-sync-alt me-2"></i>تحديث
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stat-card slide-in-up">
                            <div class="stat-icon primary">
                                <i class="fas fa-calendar-check"></i>
                            </div>
                            <div class="stat-number" id="totalBookings">0</div>
                            <div class="stat-label">إجمالي الحجوزات</div>
                            <div class="stat-change positive">
                                <i class="fas fa-arrow-up"></i>
                                <span id="bookingsChange">+12%</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stat-card slide-in-up" style="animation-delay: 0.1s">
                            <div class="stat-icon success">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                            <div class="stat-number" id="totalRevenue">0</div>
                            <div class="stat-label">إجمالي الإيرادات</div>
                            <div class="stat-change positive">
                                <i class="fas fa-arrow-up"></i>
                                <span id="revenueChange">+8%</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stat-card slide-in-up" style="animation-delay: 0.2s">
                            <div class="stat-icon warning">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-number" id="totalCustomers">0</div>
                            <div class="stat-label">عدد العملاء</div>
                            <div class="stat-change positive">
                                <i class="fas fa-arrow-up"></i>
                                <span id="customersChange">+15%</span>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-3 col-md-6 mb-4">
                        <div class="stat-card slide-in-up" style="animation-delay: 0.3s">
                            <div class="stat-icon info">
                                <i class="fas fa-chart-line"></i>
                            </div>
                            <div class="stat-number" id="conversionRate">0%</div>
                            <div class="stat-label">معدل التحويل</div>
                            <div class="stat-change positive">
                                <i class="fas fa-arrow-up"></i>
                                <span id="conversionChange">+3%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Charts Section -->
                <div class="row mb-4">
                    <div class="col-xl-8 mb-4">
                        <div class="chart-container">
                            <div class="chart-header">
                                <h5 class="chart-title">
                                    <i class="fas fa-chart-area me-2"></i>
                                    تحليل المبيعات والإيرادات
                                </h5>
                                <div class="chart-controls">
                                    <button class="chart-btn active" data-chart="revenue">الإيرادات</button>
                                    <button class="chart-btn" data-chart="bookings">الحجوزات</button>
                                    <button class="chart-btn" data-chart="customers">العملاء</button>
                                </div>
                            </div>
                            <div class="chart-body">
                                <canvas id="mainChart" width="400" height="200"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-4 mb-4">
                        <div class="chart-container">
                            <div class="chart-header">
                                <h5 class="chart-title">
                                    <i class="fas fa-chart-pie me-2"></i>
                                    توزيع أنواع الحجوزات
                                </h5>
                            </div>
                            <div class="chart-body">
                                <canvas id="pieChart" width="300" height="300"></canvas>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Recent Activities and Quick Actions -->
                <div class="row">
                    <div class="col-xl-8 mb-4">
                        <div class="dashboard-card">
                            <div class="card-header-enhanced">
                                <h5>
                                    <i class="fas fa-history"></i>
                                    الأنشطة الأخيرة
                                </h5>
                                <a href="#" class="btn btn-sm btn-outline-primary">عرض الكل</a>
                            </div>
                            <div class="card-body-enhanced">
                                <div id="recentActivities">
                                    ${this.renderRecentActivities()}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-xl-4 mb-4">
                        <div class="dashboard-card">
                            <div class="card-header-enhanced">
                                <h5>
                                    <i class="fas fa-bolt"></i>
                                    إجراءات سريعة
                                </h5>
                            </div>
                            <div class="card-body-enhanced">
                                <div class="d-grid gap-2">
                                    <button class="btn btn-primary-enhanced" onclick="showAddBooking()">
                                        <i class="fas fa-plus me-2"></i>حجز جديد
                                    </button>
                                    <button class="btn btn-success-enhanced" onclick="showAddCustomer()">
                                        <i class="fas fa-user-plus me-2"></i>عميل جديد
                                    </button>
                                    <button class="btn btn-enhanced" style="background: linear-gradient(135deg, #ffc107, #ffb300); color: white;" onclick="showReports()">
                                        <i class="fas fa-chart-bar me-2"></i>التقارير
                                    </button>
                                    <button class="btn btn-enhanced" style="background: linear-gradient(135deg, #17a2b8, #0288d1); color: white;" onclick="showSettings()">
                                        <i class="fas fa-cog me-2"></i>الإعدادات
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Notifications Panel -->
                <div class="row">
                    <div class="col-12">
                        <div class="dashboard-card">
                            <div class="card-header-enhanced">
                                <h5>
                                    <i class="fas fa-bell"></i>
                                    الإشعارات والتنبيهات
                                </h5>
                                <span class="badge bg-danger" id="notificationCount">3</span>
                            </div>
                            <div class="card-body-enhanced">
                                <div id="notificationsPanel">
                                    ${this.renderNotifications()}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // عرض المحتوى
        if (window.UI && window.UI.showPage) {
            window.UI.showPage(content);
        } else {
            document.getElementById('main-content').innerHTML = content;
        }

        // تهيئة الرسوم البيانية
        this.initCharts();
        
        // تحميل البيانات
        this.loadDashboardData();
        
        // تهيئة الأحداث
        this.initEvents();
    },

    /**
     * تحميل بيانات لوحة التحكم
     */
    loadDashboardData: function() {
        this.state.isLoading = true;
        
        try {
            // محاكاة تحميل البيانات
            setTimeout(() => {
                // إحصائيات وهمية
                this.data.statistics = {
                    totalBookings: 1247,
                    totalRevenue: 485000,
                    totalCustomers: 892,
                    conversionRate: 68
                };

                // تحديث الإحصائيات في الواجهة
                this.updateStatistics();
                
                // تحديث الرسوم البيانية
                this.updateCharts();
                
                this.state.isLoading = false;
                this.state.lastUpdate = new Date();
                
            }, 1000);
            
        } catch (error) {
            console.error('خطأ في تحميل بيانات لوحة التحكم:', error);
            this.state.isLoading = false;
        }
    },

    /**
     * تحديث الإحصائيات
     */
    updateStatistics: function() {
        const stats = this.data.statistics;
        
        // تحديث الأرقام مع تأثير العد التدريجي
        this.animateNumber('totalBookings', stats.totalBookings);
        this.animateNumber('totalRevenue', stats.totalRevenue, 'currency');
        this.animateNumber('totalCustomers', stats.totalCustomers);
        this.animateNumber('conversionRate', stats.conversionRate, 'percentage');
    },

    /**
     * تحريك الأرقام تدريجياً
     */
    animateNumber: function(elementId, targetValue, format = 'number') {
        const element = document.getElementById(elementId);
        if (!element) return;

        const startValue = 0;
        const duration = 2000;
        const startTime = performance.now();

        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);
            
            // تأثير easing
            const easeOutQuart = 1 - Math.pow(1 - progress, 4);
            const currentValue = startValue + (targetValue - startValue) * easeOutQuart;
            
            // تنسيق القيمة
            let displayValue;
            switch (format) {
                case 'currency':
                    displayValue = new Intl.NumberFormat('ar-SA', {
                        style: 'currency',
                        currency: 'SAR',
                        minimumFractionDigits: 0
                    }).format(currentValue);
                    break;
                case 'percentage':
                    displayValue = Math.round(currentValue) + '%';
                    break;
                default:
                    displayValue = Math.round(currentValue).toLocaleString('ar-SA');
            }
            
            element.textContent = displayValue;
            
            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };

        requestAnimationFrame(animate);
    },

    /**
     * عرض الأنشطة الأخيرة
     */
    renderRecentActivities: function() {
        const activities = [
            {
                icon: 'fas fa-plus text-success',
                title: 'حجز جديد',
                description: 'تم إنشاء حجز جديد للعميل أحمد محمد',
                time: 'منذ 5 دقائق',
                type: 'booking'
            },
            {
                icon: 'fas fa-dollar-sign text-primary',
                title: 'دفعة جديدة',
                description: 'تم استلام دفعة بقيمة 15,000 ريال',
                time: 'منذ 15 دقيقة',
                type: 'payment'
            },
            {
                icon: 'fas fa-user-plus text-info',
                title: 'عميل جديد',
                description: 'تم تسجيل عميل جديد: سارة أحمد',
                time: 'منذ 30 دقيقة',
                type: 'customer'
            },
            {
                icon: 'fas fa-edit text-warning',
                title: 'تعديل حجز',
                description: 'تم تعديل تفاصيل الحجز رقم #1234',
                time: 'منذ ساعة',
                type: 'edit'
            }
        ];

        return activities.map(activity => `
            <div class="activity-item d-flex align-items-center p-3 border-bottom">
                <div class="activity-icon me-3">
                    <i class="${activity.icon} fa-lg"></i>
                </div>
                <div class="activity-content flex-grow-1">
                    <h6 class="activity-title mb-1">${activity.title}</h6>
                    <p class="activity-description text-muted mb-0">${activity.description}</p>
                </div>
                <div class="activity-time">
                    <small class="text-muted">${activity.time}</small>
                </div>
            </div>
        `).join('');
    },

    /**
     * عرض الإشعارات
     */
    renderNotifications: function() {
        const notifications = [
            {
                type: 'warning',
                icon: 'fas fa-exclamation-triangle',
                title: 'تنبيه: حجوزات تحتاج متابعة',
                message: 'يوجد 5 حجوزات تحتاج إلى متابعة وتأكيد',
                action: 'عرض الحجوزات'
            },
            {
                type: 'info',
                icon: 'fas fa-info-circle',
                title: 'تحديث النظام',
                message: 'يتوفر تحديث جديد للنظام مع مميزات محسنة',
                action: 'تحديث الآن'
            },
            {
                type: 'success',
                icon: 'fas fa-check-circle',
                title: 'تم إكمال النسخ الاحتياطي',
                message: 'تم إنشاء نسخة احتياطية بنجاح في الساعة 2:00 ص',
                action: 'عرض التفاصيل'
            }
        ];

        return notifications.map(notification => `
            <div class="alert alert-${notification.type} alert-dismissible fade show" role="alert">
                <div class="d-flex align-items-center">
                    <i class="${notification.icon} fa-lg me-3"></i>
                    <div class="flex-grow-1">
                        <h6 class="alert-heading mb-1">${notification.title}</h6>
                        <p class="mb-2">${notification.message}</p>
                        <button class="btn btn-sm btn-outline-${notification.type}">${notification.action}</button>
                    </div>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            </div>
        `).join('');
    },

    /**
     * تهيئة الرسوم البيانية
     */
    initCharts: function() {
        // الرسم البياني الرئيسي
        const mainCtx = document.getElementById('mainChart');
        if (mainCtx) {
            this.charts.mainChart = new Chart(mainCtx, {
                type: 'line',
                data: {
                    labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                    datasets: [{
                        label: 'الإيرادات',
                        data: [65000, 78000, 85000, 92000, 88000, 95000],
                        borderColor: this.config.chartColors.primary,
                        backgroundColor: this.config.chartColors.primary + '20',
                        tension: 0.4,
                        fill: true
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                callback: function(value) {
                                    return value.toLocaleString('ar-SA') + ' ر.س';
                                }
                            }
                        }
                    }
                }
            });
        }

        // الرسم البياني الدائري
        const pieCtx = document.getElementById('pieChart');
        if (pieCtx) {
            this.charts.pieChart = new Chart(pieCtx, {
                type: 'doughnut',
                data: {
                    labels: ['تذاكر طيران', 'فنادق', 'حج وعمرة', 'تأشيرات', 'أخرى'],
                    datasets: [{
                        data: [45, 25, 15, 10, 5],
                        backgroundColor: [
                            this.config.chartColors.primary,
                            this.config.chartColors.success,
                            this.config.chartColors.warning,
                            this.config.chartColors.info,
                            this.config.chartColors.danger
                        ]
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }
    },

    /**
     * تحديث الرسوم البيانية
     */
    updateCharts: function() {
        // تحديث بيانات الرسوم البيانية
        if (this.charts.mainChart) {
            // بيانات وهمية جديدة
            const newData = [70000, 82000, 89000, 96000, 92000, 98000];
            this.charts.mainChart.data.datasets[0].data = newData;
            this.charts.mainChart.update('active');
        }
    },

    /**
     * تهيئة الأحداث
     */
    initEvents: function() {
        // تغيير الفترة الزمنية
        const periodSelector = document.getElementById('periodSelector');
        if (periodSelector) {
            periodSelector.addEventListener('change', (e) => {
                this.state.selectedPeriod = e.target.value;
                this.loadDashboardData();
            });
        }

        // أزرار التحكم في الرسوم البيانية
        document.querySelectorAll('.chart-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                // إزالة الفئة النشطة من جميع الأزرار
                document.querySelectorAll('.chart-btn').forEach(b => b.classList.remove('active'));

                // إضافة الفئة النشطة للزر المحدد
                e.target.classList.add('active');

                // تحديث الرسم البياني
                const chartType = e.target.getAttribute('data-chart');
                this.updateMainChart(chartType);
            });
        });
    },

    /**
     * تحديث الرسم البياني الرئيسي
     */
    updateMainChart: function(type) {
        if (!this.charts.mainChart) return;

        let data, label, color;

        switch (type) {
            case 'revenue':
                data = [70000, 82000, 89000, 96000, 92000, 98000];
                label = 'الإيرادات';
                color = this.config.chartColors.primary;
                break;
            case 'bookings':
                data = [120, 145, 160, 175, 168, 185];
                label = 'الحجوزات';
                color = this.config.chartColors.success;
                break;
            case 'customers':
                data = [85, 92, 98, 105, 102, 110];
                label = 'العملاء الجدد';
                color = this.config.chartColors.warning;
                break;
        }

        this.charts.mainChart.data.datasets[0].data = data;
        this.charts.mainChart.data.datasets[0].label = label;
        this.charts.mainChart.data.datasets[0].borderColor = color;
        this.charts.mainChart.data.datasets[0].backgroundColor = color + '20';
        this.charts.mainChart.update('active');
    },

    /**
     * تحديث البيانات
     */
    refreshData: function() {
        // إظهار مؤشر التحميل
        this.showLoadingIndicator();

        // تحميل البيانات الجديدة
        this.loadDashboardData();

        // إخفاء مؤشر التحميل
        setTimeout(() => {
            this.hideLoadingIndicator();
            this.showNotification('تم تحديث البيانات بنجاح', 'success');
        }, 1500);
    },

    /**
     * إظهار مؤشر التحميل
     */
    showLoadingIndicator: function() {
        const indicators = document.querySelectorAll('.stat-number');
        indicators.forEach(indicator => {
            indicator.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        });
    },

    /**
     * إخفاء مؤشر التحميل
     */
    hideLoadingIndicator: function() {
        // سيتم استبدال المؤشرات بالأرقام الجديدة في updateStatistics
    },

    /**
     * بدء التحديث التلقائي
     */
    startAutoRefresh: function() {
        setInterval(() => {
            if (!this.state.isLoading) {
                this.loadDashboardData();
            }
        }, this.config.refreshInterval);
    },

    /**
     * إظهار إشعار
     */
    showNotification: function(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; left: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check' : type === 'error' ? 'times' : 'info'}-circle me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(notification);

        // إزالة الإشعار تلقائياً
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 5000);
    },

    /**
     * تصدير البيانات
     */
    exportData: function(format = 'excel') {
        this.showNotification('جاري تصدير البيانات...', 'info');

        // محاكاة عملية التصدير
        setTimeout(() => {
            this.showNotification(`تم تصدير البيانات بصيغة ${format.toUpperCase()} بنجاح`, 'success');
        }, 2000);
    },

    /**
     * طباعة التقرير
     */
    printReport: function() {
        window.print();
    }
};
