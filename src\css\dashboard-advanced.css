/**
 * ===================================
 * لوحة التحكم المتقدمة - Advanced Dashboard Styles
 * قمة الوعد للسفريات
 * ===================================
 */

:root {
    --primary-color: #2c5aa0;
    --primary-dark: #1e3c72;
    --secondary-color: #f8f9fa;
    --accent-color: #28a745;
    --accent-light: #34ce57;
    --warning-color: #ffc107;
    --danger-color: #dc3545;
    --info-color: #17a2b8;
    --dark-color: #343a40;
    --light-color: #ffffff;
    --text-dark: #2c3e50;
    --text-light: #6c757d;
    --text-muted: #8e9aaf;
    --border-color: #e3e6f0;
    --shadow-light: 0 0.15rem 1.75rem 0 rgba(58, 59, 69, 0.15);
    --shadow-medium: 0 0.25rem 2rem 0 rgba(58, 59, 69, 0.2);
    --shadow-heavy: 0 0.5rem 3rem 0 rgba(58, 59, 69, 0.25);
    --border-radius: 0.75rem;
    --border-radius-lg: 1rem;
    --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Enhanced Body and Layout */
body {
    font-family: 'Cairo', sans-serif;
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    color: var(--text-dark);
    line-height: 1.6;
}

.main-wrapper {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Enhanced Navbar */
.navbar-enhanced {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
    box-shadow: var(--shadow-medium);
    padding: 1rem 0;
    position: sticky;
    top: 0;
    z-index: 1030;
    backdrop-filter: blur(10px);
}

.navbar-brand-enhanced {
    font-size: 1.5rem;
    font-weight: 700;
    color: white !important;
    text-decoration: none;
    display: flex;
    align-items: center;
    transition: var(--transition);
}

.navbar-brand-enhanced:hover {
    transform: scale(1.05);
    color: var(--warning-color) !important;
}

.navbar-brand-enhanced i {
    margin-left: 0.5rem;
    font-size: 1.8rem;
}

/* Enhanced Navigation Links */
.nav-link-enhanced {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
    padding: 0.75rem 1rem !important;
    border-radius: var(--border-radius);
    margin: 0 0.25rem;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.nav-link-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.nav-link-enhanced:hover::before {
    left: 100%;
}

.nav-link-enhanced:hover,
.nav-link-enhanced.active {
    background: rgba(255, 255, 255, 0.15);
    color: white !important;
    transform: translateY(-2px);
}

.nav-link-enhanced i {
    margin-left: 0.5rem;
    font-size: 1.1rem;
}

/* Enhanced Dropdown Menus */
.dropdown-menu-enhanced {
    background: white;
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-medium);
    padding: 0.5rem 0;
    margin-top: 0.5rem;
    min-width: 250px;
}

.dropdown-item-enhanced {
    padding: 0.75rem 1.5rem;
    color: var(--text-dark);
    font-weight: 500;
    transition: var(--transition);
    border-left: 3px solid transparent;
}

.dropdown-item-enhanced:hover {
    background: var(--secondary-color);
    color: var(--primary-color);
    border-left-color: var(--primary-color);
    transform: translateX(5px);
}

.dropdown-item-enhanced i {
    width: 20px;
    text-align: center;
    margin-left: 0.5rem;
}

/* Enhanced Main Content */
.main-content {
    flex: 1;
    padding: 2rem;
    background: transparent;
}

/* Enhanced Dashboard Cards */
.dashboard-card {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
    transition: var(--transition);
    overflow: hidden;
    position: relative;
}

.dashboard-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    transform: scaleX(0);
    transition: var(--transition);
}

.dashboard-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-heavy);
}

.dashboard-card:hover::before {
    transform: scaleX(1);
}

.card-header-enhanced {
    background: linear-gradient(135deg, var(--secondary-color) 0%, #e9ecef 100%);
    border-bottom: 1px solid var(--border-color);
    padding: 1.5rem;
    font-weight: 600;
    color: var(--text-dark);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.card-header-enhanced h5 {
    margin: 0;
    display: flex;
    align-items: center;
}

.card-header-enhanced i {
    margin-left: 0.75rem;
    color: var(--primary-color);
    font-size: 1.2rem;
}

.card-body-enhanced {
    padding: 2rem;
}

/* Enhanced Statistics Cards */
.stat-card {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    text-align: center;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
    opacity: 0;
    transition: var(--transition);
}

.stat-card:hover::before {
    opacity: 0.05;
}

.stat-card:hover {
    transform: translateY(-10px);
    box-shadow: var(--shadow-heavy);
}

.stat-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
    position: relative;
    z-index: 2;
}

.stat-icon.primary {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
}

.stat-icon.success {
    background: linear-gradient(135deg, var(--accent-color), var(--accent-light));
}

.stat-icon.warning {
    background: linear-gradient(135deg, var(--warning-color), #ffb300);
}

.stat-icon.danger {
    background: linear-gradient(135deg, var(--danger-color), #c62828);
}

.stat-icon.info {
    background: linear-gradient(135deg, var(--info-color), #0288d1);
}

.stat-number {
    font-size: 2.5rem;
    font-weight: 900;
    color: var(--text-dark);
    margin-bottom: 0.5rem;
    position: relative;
    z-index: 2;
}

.stat-label {
    font-size: 1rem;
    color: var(--text-light);
    font-weight: 500;
    position: relative;
    z-index: 2;
}

.stat-change {
    font-size: 0.875rem;
    font-weight: 600;
    margin-top: 0.5rem;
    position: relative;
    z-index: 2;
}

.stat-change.positive {
    color: var(--accent-color);
}

.stat-change.negative {
    color: var(--danger-color);
}

.stat-change i {
    margin-left: 0.25rem;
}

/* Enhanced Charts Container */
.chart-container {
    background: white;
    border-radius: var(--border-radius-lg);
    padding: 2rem;
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
    margin-bottom: 2rem;
}

.chart-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.chart-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-dark);
    margin: 0;
}

.chart-controls {
    display: flex;
    gap: 0.5rem;
}

.chart-btn {
    padding: 0.5rem 1rem;
    border: 1px solid var(--border-color);
    background: white;
    color: var(--text-light);
    border-radius: var(--border-radius);
    font-size: 0.875rem;
    font-weight: 500;
    transition: var(--transition);
    cursor: pointer;
}

.chart-btn:hover,
.chart-btn.active {
    background: var(--primary-color);
    color: white;
    border-color: var(--primary-color);
}

/* Enhanced Tables */
.table-enhanced {
    background: white;
    border-radius: var(--border-radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-light);
    border: 1px solid var(--border-color);
}

.table-enhanced .table {
    margin: 0;
}

.table-enhanced .table thead th {
    background: linear-gradient(135deg, var(--secondary-color) 0%, #e9ecef 100%);
    border-bottom: 2px solid var(--border-color);
    font-weight: 600;
    color: var(--text-dark);
    padding: 1rem;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table-enhanced .table tbody td {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
}

.table-enhanced .table tbody tr {
    transition: var(--transition);
}

.table-enhanced .table tbody tr:hover {
    background: rgba(44, 90, 160, 0.05);
}

/* Enhanced Buttons */
.btn-enhanced {
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    font-weight: 600;
    font-size: 0.875rem;
    transition: var(--transition);
    border: none;
    position: relative;
    overflow: hidden;
}

.btn-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-enhanced:hover::before {
    left: 100%;
}

.btn-primary-enhanced {
    background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
    color: white;
}

.btn-primary-enhanced:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(44, 90, 160, 0.3);
}

.btn-success-enhanced {
    background: linear-gradient(135deg, var(--accent-color), var(--accent-light));
    color: white;
}

.btn-success-enhanced:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
}

/* Enhanced Sidebar (if needed) */
.sidebar-enhanced {
    background: white;
    box-shadow: var(--shadow-medium);
    border-radius: var(--border-radius-lg);
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.sidebar-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-dark);
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-color);
}

.sidebar-item {
    padding: 0.75rem 1rem;
    margin: 0.25rem 0;
    border-radius: var(--border-radius);
    color: var(--text-light);
    text-decoration: none;
    transition: var(--transition);
    display: flex;
    align-items: center;
}

.sidebar-item:hover {
    background: var(--secondary-color);
    color: var(--primary-color);
    transform: translateX(5px);
}

.sidebar-item i {
    width: 20px;
    margin-left: 0.75rem;
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
    .main-content {
        padding: 1rem;
    }
    
    .stat-card {
        margin-bottom: 1rem;
    }
    
    .chart-container {
        padding: 1rem;
    }
    
    .card-body-enhanced {
        padding: 1rem;
    }
    
    .navbar-brand-enhanced {
        font-size: 1.25rem;
    }
    
    .nav-link-enhanced {
        padding: 0.5rem 0.75rem !important;
    }
}

/* Enhanced Loading States */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* Enhanced Animations */
.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

.slide-in-up {
    animation: slideInUp 0.6s ease-out;
}

.scale-in {
    animation: scaleIn 0.4s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes scaleIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}
