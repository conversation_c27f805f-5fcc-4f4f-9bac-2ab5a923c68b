-- قاعدة بيانات نظام قمة الوعد المحاسبي
-- نظام محاسبي لمكاتب السفريات والحج والعمرة

-- إنشاء قاعدة البيانات
CREATE DATABASE IF NOT EXISTS qimat_alwaed_accounting 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE qimat_alwaed_accounting;

-- جدول المستخدمين المحسن
CREATE TABLE users (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    role ENUM('admin', 'accountant', 'agent', 'employee', 'viewer') DEFAULT 'employee',
    phone VARCHAR(20),
    is_active BOOLEAN DEFAULT TRUE,
    subscription_start_date DATE NULL,
    subscription_end_date DATE NULL,
    subscription_days INT DEFAULT 0,
    max_login_attempts INT DEFAULT 5,
    login_attempts INT DEFAULT 0,
    last_login_attempt TIMESTAMP NULL,
    account_locked_until TIMESTAMP NULL,
    two_factor_enabled BOOLEAN DEFAULT FALSE,
    two_factor_secret VARCHAR(32) NULL,
    last_login TIMESTAMP NULL,
    last_activity TIMESTAMP NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    created_by INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- جدول العملاء
CREATE TABLE customers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    customer_code VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    phone VARCHAR(20),
    email VARCHAR(100),
    national_id VARCHAR(20),
    passport_number VARCHAR(20),
    address TEXT,
    city VARCHAR(50),
    country VARCHAR(50) DEFAULT 'السعودية',
    customer_type ENUM('individual', 'company', 'agent') DEFAULT 'individual',
    credit_limit DECIMAL(15,2) DEFAULT 0,
    current_balance DECIMAL(15,2) DEFAULT 0,
    status ENUM('active', 'inactive', 'blocked') DEFAULT 'active',
    notes TEXT,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- جدول الموردين
CREATE TABLE suppliers (
    id INT PRIMARY KEY AUTO_INCREMENT,
    supplier_code VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    contact_person VARCHAR(100),
    phone VARCHAR(20),
    email VARCHAR(100),
    address TEXT,
    city VARCHAR(50),
    country VARCHAR(50),
    supplier_type ENUM('airline', 'hotel', 'transport', 'visa', 'insurance', 'other') DEFAULT 'other',
    payment_terms VARCHAR(100),
    credit_limit DECIMAL(15,2) DEFAULT 0,
    current_balance DECIMAL(15,2) DEFAULT 0,
    status ENUM('active', 'inactive') DEFAULT 'active',
    notes TEXT,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- جدول الوكلاء
CREATE TABLE agents (
    id INT PRIMARY KEY AUTO_INCREMENT,
    agent_code VARCHAR(20) UNIQUE NOT NULL,
    name VARCHAR(100) NOT NULL,
    contact_person VARCHAR(100),
    phone VARCHAR(20),
    email VARCHAR(100),
    address TEXT,
    city VARCHAR(50),
    country VARCHAR(50),
    commission_rate DECIMAL(5,2) DEFAULT 0,
    commission_type ENUM('percentage', 'fixed') DEFAULT 'percentage',
    payment_terms VARCHAR(100),
    current_balance DECIMAL(15,2) DEFAULT 0,
    status ENUM('active', 'inactive') DEFAULT 'active',
    notes TEXT,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- جدول دليل الحسابات
CREATE TABLE chart_of_accounts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    account_code VARCHAR(20) UNIQUE NOT NULL,
    account_name VARCHAR(100) NOT NULL,
    account_type ENUM('asset', 'liability', 'equity', 'revenue', 'expense') NOT NULL,
    parent_account_id INT NULL,
    level INT DEFAULT 1,
    is_active BOOLEAN DEFAULT TRUE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_account_id) REFERENCES chart_of_accounts(id)
);

-- جدول القيود المحاسبية
CREATE TABLE journal_entries (
    id INT PRIMARY KEY AUTO_INCREMENT,
    entry_number VARCHAR(20) UNIQUE NOT NULL,
    entry_date DATE NOT NULL,
    description TEXT NOT NULL,
    reference_type ENUM('booking', 'payment', 'receipt', 'adjustment', 'other') DEFAULT 'other',
    reference_id INT NULL,
    total_debit DECIMAL(15,2) NOT NULL,
    total_credit DECIMAL(15,2) NOT NULL,
    status ENUM('draft', 'posted', 'cancelled') DEFAULT 'draft',
    created_by INT,
    approved_by INT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id),
    FOREIGN KEY (approved_by) REFERENCES users(id)
);

-- جدول تفاصيل القيود المحاسبية
CREATE TABLE journal_entry_details (
    id INT PRIMARY KEY AUTO_INCREMENT,
    journal_entry_id INT NOT NULL,
    account_id INT NOT NULL,
    debit_amount DECIMAL(15,2) DEFAULT 0,
    credit_amount DECIMAL(15,2) DEFAULT 0,
    description TEXT,
    FOREIGN KEY (journal_entry_id) REFERENCES journal_entries(id) ON DELETE CASCADE,
    FOREIGN KEY (account_id) REFERENCES chart_of_accounts(id)
);

-- جدول أنواع الخدمات
CREATE TABLE service_types (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    category ENUM('flight', 'hotel', 'visa', 'transport', 'hajj', 'umrah', 'package', 'other') NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- جدول الحجوزات
CREATE TABLE bookings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    booking_number VARCHAR(20) UNIQUE NOT NULL,
    customer_id INT NOT NULL,
    agent_id INT NULL,
    booking_date DATE NOT NULL,
    travel_date DATE,
    return_date DATE NULL,
    service_type_id INT NOT NULL,
    destination VARCHAR(100),
    number_of_passengers INT DEFAULT 1,
    total_amount DECIMAL(15,2) NOT NULL,
    paid_amount DECIMAL(15,2) DEFAULT 0,
    remaining_amount DECIMAL(15,2) GENERATED ALWAYS AS (total_amount - paid_amount) STORED,
    commission_amount DECIMAL(15,2) DEFAULT 0,
    status ENUM('pending', 'confirmed', 'cancelled', 'completed') DEFAULT 'pending',
    notes TEXT,
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    FOREIGN KEY (agent_id) REFERENCES agents(id),
    FOREIGN KEY (service_type_id) REFERENCES service_types(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- جدول تفاصيل الحجوزات
CREATE TABLE booking_details (
    id INT PRIMARY KEY AUTO_INCREMENT,
    booking_id INT NOT NULL,
    passenger_name VARCHAR(100) NOT NULL,
    passport_number VARCHAR(20),
    nationality VARCHAR(50),
    date_of_birth DATE,
    gender ENUM('male', 'female'),
    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE
);

-- جدول المدفوعات
CREATE TABLE payments (
    id INT PRIMARY KEY AUTO_INCREMENT,
    payment_number VARCHAR(20) UNIQUE NOT NULL,
    booking_id INT NULL,
    customer_id INT NULL,
    supplier_id INT NULL,
    agent_id INT NULL,
    payment_date DATE NOT NULL,
    amount DECIMAL(15,2) NOT NULL,
    payment_method ENUM('cash', 'bank_transfer', 'credit_card', 'check', 'other') NOT NULL,
    payment_type ENUM('receipt', 'payment') NOT NULL,
    reference_number VARCHAR(50),
    description TEXT,
    status ENUM('pending', 'completed', 'cancelled') DEFAULT 'completed',
    created_by INT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (booking_id) REFERENCES bookings(id),
    FOREIGN KEY (customer_id) REFERENCES customers(id),
    FOREIGN KEY (supplier_id) REFERENCES suppliers(id),
    FOREIGN KEY (agent_id) REFERENCES agents(id),
    FOREIGN KEY (created_by) REFERENCES users(id)
);

-- جدول المخزون
CREATE TABLE inventory_items (
    id INT PRIMARY KEY AUTO_INCREMENT,
    item_code VARCHAR(20) UNIQUE NOT NULL,
    item_name VARCHAR(100) NOT NULL,
    category ENUM('ticket', 'voucher', 'package', 'service', 'other') DEFAULT 'other',
    description TEXT,
    unit_price DECIMAL(15,2) DEFAULT 0,
    quantity_on_hand INT DEFAULT 0,
    minimum_quantity INT DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- جدول إعدادات النظام
CREATE TABLE system_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    setting_type ENUM('string', 'number', 'boolean', 'json') DEFAULT 'string',
    description TEXT,
    updated_by INT,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (updated_by) REFERENCES users(id)
);

-- إدراج البيانات الأساسية
INSERT INTO service_types (name, category, description) VALUES
('تذاكر طيران', 'flight', 'حجز تذاكر الطيران الداخلية والدولية'),
('حجز فنادق', 'hotel', 'حجز الفنادق والإقامة'),
('استخراج تأشيرات', 'visa', 'خدمات استخراج التأشيرات'),
('حج', 'hajj', 'خدمات الحج'),
('عمرة', 'umrah', 'خدمات العمرة'),
('باقات سياحية', 'package', 'الباقات السياحية الشاملة');

-- إدراج حسابات أساسية
INSERT INTO chart_of_accounts (account_code, account_name, account_type, level) VALUES
('1000', 'الأصول', 'asset', 1),
('1100', 'الأصول المتداولة', 'asset', 2),
('1110', 'النقدية', 'asset', 3),
('1120', 'العملاء', 'asset', 3),
('2000', 'الخصوم', 'liability', 1),
('2100', 'الخصوم المتداولة', 'liability', 2),
('2110', 'الموردين', 'liability', 3),
('3000', 'حقوق الملكية', 'equity', 1),
('4000', 'الإيرادات', 'revenue', 1),
('4100', 'إيرادات الخدمات', 'revenue', 2),
('5000', 'المصروفات', 'expense', 1),
('5100', 'مصروفات التشغيل', 'expense', 2);

-- إدراج إعدادات النظام الأساسية
INSERT INTO system_settings (setting_key, setting_value, setting_type, description) VALUES
('company_name', 'قيمة الوعد للسفريات', 'string', 'اسم الشركة'),
('company_address', 'الرياض، المملكة العربية السعودية', 'string', 'عنوان الشركة'),
('company_phone', '+966123456789', 'string', 'هاتف الشركة'),
('company_email', '<EMAIL>', 'string', 'بريد الشركة الإلكتروني'),
('default_currency', 'SAR', 'string', 'العملة الافتراضية'),
('tax_rate', '15', 'number', 'معدل الضريبة المضافة');

-- إدراج إعدادات الأمان الأساسية
INSERT INTO security_settings (setting_name, setting_value, setting_type, description, is_system) VALUES
('max_login_attempts', '5', 'integer', 'الحد الأقصى لمحاولات تسجيل الدخول', TRUE),
('account_lockout_duration', '15', 'integer', 'مدة قفل الحساب بالدقائق', TRUE),
('session_timeout', '480', 'integer', 'مهلة انتهاء الجلسة بالدقائق', TRUE),
('password_min_length', '8', 'integer', 'الحد الأدنى لطول كلمة المرور', TRUE),
('require_strong_password', 'true', 'boolean', 'يتطلب كلمة مرور قوية', TRUE),
('enable_two_factor', 'false', 'boolean', 'تفعيل المصادقة الثنائية', TRUE),
('enable_ip_whitelist', 'false', 'boolean', 'تفعيل قائمة IP المسموحة', TRUE),
('enable_audit_log', 'true', 'boolean', 'تفعيل سجل المراجعة', TRUE),
('max_concurrent_sessions', '3', 'integer', 'الحد الأقصى للجلسات المتزامنة', TRUE),
('enable_email_notifications', 'true', 'boolean', 'تفعيل إشعارات البريد الإلكتروني', TRUE);

-- إدراج مستخدم المدير الافتراضي
INSERT INTO users (username, email, password_hash, full_name, role, is_active, subscription_start_date, subscription_end_date, subscription_days, created_by) VALUES
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', 'مدير النظام', 'admin', TRUE, CURDATE(), DATE_ADD(CURDATE(), INTERVAL 365 DAY), 365, NULL);

-- إدراج الصلاحيات الأساسية للمدير
INSERT INTO user_permissions (user_id, permission_name, permission_type, can_read, can_write, can_update, can_delete, can_export, can_print, granted_by) VALUES
(1, 'system_admin', 'module', TRUE, TRUE, TRUE, TRUE, TRUE, TRUE, NULL),
(1, 'user_management', 'module', TRUE, TRUE, TRUE, TRUE, TRUE, TRUE, NULL),
(1, 'customers', 'module', TRUE, TRUE, TRUE, TRUE, TRUE, TRUE, NULL),
(1, 'suppliers', 'module', TRUE, TRUE, TRUE, TRUE, TRUE, TRUE, NULL),
(1, 'agents', 'module', TRUE, TRUE, TRUE, TRUE, TRUE, TRUE, NULL),
(1, 'bookings', 'module', TRUE, TRUE, TRUE, TRUE, TRUE, TRUE, NULL),
(1, 'accounting', 'module', TRUE, TRUE, TRUE, TRUE, TRUE, TRUE, NULL),
(1, 'reports', 'module', TRUE, TRUE, TRUE, TRUE, TRUE, TRUE, NULL),
(1, 'settings', 'module', TRUE, TRUE, TRUE, TRUE, TRUE, TRUE, NULL);

-- جدول صلاحيات المستخدمين
CREATE TABLE user_permissions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    permission_name VARCHAR(100) NOT NULL,
    permission_type ENUM('module', 'action', 'data') DEFAULT 'action',
    resource_name VARCHAR(100) NULL,
    can_read BOOLEAN DEFAULT FALSE,
    can_write BOOLEAN DEFAULT FALSE,
    can_update BOOLEAN DEFAULT FALSE,
    can_delete BOOLEAN DEFAULT FALSE,
    can_export BOOLEAN DEFAULT FALSE,
    can_print BOOLEAN DEFAULT FALSE,
    start_date DATE NULL,
    end_date DATE NULL,
    is_active BOOLEAN DEFAULT TRUE,
    granted_by INT NULL,
    granted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (granted_by) REFERENCES users(id),
    UNIQUE KEY unique_user_permission (user_id, permission_name, resource_name)
);

-- جدول جلسات المستخدمين
CREATE TABLE user_sessions (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    session_token VARCHAR(255) UNIQUE NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT NULL,
    login_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_activity TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    expires_at TIMESTAMP NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    logout_time TIMESTAMP NULL,
    logout_reason ENUM('manual', 'timeout', 'forced', 'expired') NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- جدول سجل نشاط المستخدمين
CREATE TABLE user_activity_log (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NOT NULL,
    session_id INT NULL,
    action_type ENUM('login', 'logout', 'create', 'read', 'update', 'delete', 'export', 'print', 'error') NOT NULL,
    module_name VARCHAR(100) NULL,
    resource_name VARCHAR(100) NULL,
    resource_id INT NULL,
    description TEXT NULL,
    ip_address VARCHAR(45) NULL,
    user_agent TEXT NULL,
    request_data JSON NULL,
    response_status ENUM('success', 'error', 'warning') DEFAULT 'success',
    error_message TEXT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (session_id) REFERENCES user_sessions(id) ON DELETE SET NULL
);

-- جدول إعدادات الأمان
CREATE TABLE security_settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_name VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT NOT NULL,
    setting_type ENUM('boolean', 'integer', 'string', 'json') DEFAULT 'string',
    description TEXT NULL,
    is_system BOOLEAN DEFAULT FALSE,
    updated_by INT NULL,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (updated_by) REFERENCES users(id)
);

-- جدول محاولات تسجيل الدخول المشبوهة
CREATE TABLE suspicious_login_attempts (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) NULL,
    email VARCHAR(100) NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent TEXT NULL,
    attempt_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    failure_reason ENUM('invalid_credentials', 'account_locked', 'account_disabled', 'too_many_attempts', 'suspicious_activity') NOT NULL,
    is_blocked BOOLEAN DEFAULT FALSE,
    blocked_until TIMESTAMP NULL,
    country VARCHAR(50) NULL,
    city VARCHAR(50) NULL
);

-- جدول الإشعارات
CREATE TABLE notifications (
    id INT PRIMARY KEY AUTO_INCREMENT,
    user_id INT NULL,
    title VARCHAR(200) NOT NULL,
    message TEXT NOT NULL,
    type ENUM('info', 'success', 'warning', 'error', 'security') DEFAULT 'info',
    priority ENUM('low', 'medium', 'high', 'urgent') DEFAULT 'medium',
    is_read BOOLEAN DEFAULT FALSE,
    is_system BOOLEAN DEFAULT FALSE,
    action_url VARCHAR(500) NULL,
    expires_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    read_at TIMESTAMP NULL,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE
);

-- إنشاء فهارس لتحسين الأداء
CREATE INDEX idx_customers_code ON customers(customer_code);
CREATE INDEX idx_customers_phone ON customers(phone);
CREATE INDEX idx_suppliers_code ON suppliers(supplier_code);
CREATE INDEX idx_agents_code ON agents(agent_code);
CREATE INDEX idx_bookings_number ON bookings(booking_number);
CREATE INDEX idx_bookings_date ON bookings(booking_date);
CREATE INDEX idx_journal_entries_date ON journal_entries(entry_date);
CREATE INDEX idx_payments_date ON payments(payment_date);

-- فهارس الأمان والصلاحيات
CREATE INDEX idx_users_username ON users(username);
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_subscription ON users(subscription_start_date, subscription_end_date);
CREATE INDEX idx_user_permissions_user ON user_permissions(user_id);
CREATE INDEX idx_user_permissions_active ON user_permissions(is_active, start_date, end_date);
CREATE INDEX idx_user_sessions_token ON user_sessions(session_token);
CREATE INDEX idx_user_sessions_active ON user_sessions(is_active, expires_at);
CREATE INDEX idx_user_activity_user ON user_activity_log(user_id);
CREATE INDEX idx_user_activity_time ON user_activity_log(created_at);
CREATE INDEX idx_suspicious_attempts_ip ON suspicious_login_attempts(ip_address);
CREATE INDEX idx_suspicious_attempts_time ON suspicious_login_attempts(attempt_time);
CREATE INDEX idx_notifications_user ON notifications(user_id, is_read);
