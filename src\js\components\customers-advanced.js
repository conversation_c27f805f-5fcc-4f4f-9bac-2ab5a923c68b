/**
 * ===================================
 * إدارة العملاء المتقدمة - Advanced Customers Management
 * قمة الوعد للسفريات
 * ===================================
 */

window.AdvancedCustomers = {
    // إعدادات النظام
    config: {
        itemsPerPage: 15,
        searchDelay: 300,
        exportFormats: ['excel', 'pdf', 'csv'],
        allowedImageTypes: ['image/jpeg', 'image/png', 'image/gif'],
        maxImageSize: 2 * 1024 * 1024 // 2MB
    },

    // حالة النظام
    state: {
        customers: [],
        filteredCustomers: [],
        currentPage: 1,
        totalPages: 1,
        isLoading: false,
        selectedCustomers: [],
        sortBy: 'created_at',
        sortOrder: 'desc',
        filters: {
            search: '',
            status: '',
            type: '',
            dateFrom: '',
            dateTo: ''
        }
    },

    /**
     * تهيئة نظام إدارة العملاء
     */
    init: function() {
        console.log('🔄 تهيئة نظام إدارة العملاء المتقدم');
        
        try {
            // تحميل البيانات
            this.loadCustomers();
            
            // تهيئة الأحداث
            this.initEvents();
            
            console.log('✅ تم تهيئة نظام إدارة العملاء بنجاح');
            
        } catch (error) {
            console.error('❌ خطأ في تهيئة نظام إدارة العملاء:', error);
        }
    },

    /**
     * عرض صفحة إدارة العملاء
     */
    show: function() {
        const content = `
            <div class="customers-container fade-in">
                <!-- Header Section -->
                <div class="page-header mb-4">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h1 class="page-title">
                                <i class="fas fa-users me-3"></i>
                                إدارة العملاء
                            </h1>
                            <p class="page-subtitle text-muted">
                                إدارة شاملة لبيانات العملاء والتفاعل معهم
                            </p>
                        </div>
                        <div class="col-md-6 text-md-end">
                            <div class="page-actions">
                                <button class="btn btn-success-enhanced me-2" onclick="AdvancedCustomers.showAddCustomerModal()">
                                    <i class="fas fa-user-plus me-2"></i>إضافة عميل جديد
                                </button>
                                <div class="btn-group">
                                    <button class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                                        <i class="fas fa-download me-2"></i>تصدير
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" onclick="AdvancedCustomers.exportData('excel')">
                                            <i class="fas fa-file-excel me-2"></i>Excel
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="AdvancedCustomers.exportData('pdf')">
                                            <i class="fas fa-file-pdf me-2"></i>PDF
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="AdvancedCustomers.exportData('csv')">
                                            <i class="fas fa-file-csv me-2"></i>CSV
                                        </a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Filters and Search -->
                <div class="dashboard-card mb-4">
                    <div class="card-header-enhanced">
                        <h5>
                            <i class="fas fa-filter"></i>
                            البحث والتصفية
                        </h5>
                        <button class="btn btn-sm btn-outline-secondary" onclick="AdvancedCustomers.clearFilters()">
                            <i class="fas fa-times me-1"></i>مسح الفلاتر
                        </button>
                    </div>
                    <div class="card-body-enhanced">
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label class="form-label">البحث</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                    <input type="text" class="form-control" id="searchInput" 
                                           placeholder="البحث بالاسم، الهاتف، أو البريد الإلكتروني">
                                </div>
                            </div>
                            <div class="col-md-2 mb-3">
                                <label class="form-label">الحالة</label>
                                <select class="form-select" id="statusFilter">
                                    <option value="">جميع الحالات</option>
                                    <option value="active">نشط</option>
                                    <option value="inactive">غير نشط</option>
                                    <option value="vip">عميل مميز</option>
                                </select>
                            </div>
                            <div class="col-md-2 mb-3">
                                <label class="form-label">النوع</label>
                                <select class="form-select" id="typeFilter">
                                    <option value="">جميع الأنواع</option>
                                    <option value="individual">فردي</option>
                                    <option value="corporate">شركة</option>
                                    <option value="agent">وكيل</option>
                                </select>
                            </div>
                            <div class="col-md-2 mb-3">
                                <label class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="dateFromFilter">
                            </div>
                            <div class="col-md-2 mb-3">
                                <label class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="dateToFilter">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <div class="col-xl-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon primary">
                                <i class="fas fa-users"></i>
                            </div>
                            <div class="stat-number" id="totalCustomers">0</div>
                            <div class="stat-label">إجمالي العملاء</div>
                        </div>
                    </div>
                    <div class="col-xl-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon success">
                                <i class="fas fa-user-check"></i>
                            </div>
                            <div class="stat-number" id="activeCustomers">0</div>
                            <div class="stat-label">العملاء النشطون</div>
                        </div>
                    </div>
                    <div class="col-xl-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon warning">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="stat-number" id="vipCustomers">0</div>
                            <div class="stat-label">العملاء المميزون</div>
                        </div>
                    </div>
                    <div class="col-xl-3 col-md-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon info">
                                <i class="fas fa-user-plus"></i>
                            </div>
                            <div class="stat-number" id="newCustomers">0</div>
                            <div class="stat-label">عملاء جدد هذا الشهر</div>
                        </div>
                    </div>
                </div>

                <!-- Customers Table -->
                <div class="dashboard-card">
                    <div class="card-header-enhanced">
                        <h5>
                            <i class="fas fa-list"></i>
                            قائمة العملاء
                        </h5>
                        <div class="d-flex align-items-center">
                            <span class="text-muted me-3" id="customersCount">عرض 0 من 0 عميل</span>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-primary" onclick="AdvancedCustomers.toggleView('table')" id="tableViewBtn">
                                    <i class="fas fa-table"></i>
                                </button>
                                <button class="btn btn-outline-primary" onclick="AdvancedCustomers.toggleView('cards')" id="cardsViewBtn">
                                    <i class="fas fa-th-large"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body-enhanced p-0">
                        <!-- Table View -->
                        <div id="tableView" class="table-enhanced">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th width="40">
                                                <input type="checkbox" class="form-check-input" id="selectAll">
                                            </th>
                                            <th>العميل</th>
                                            <th>معلومات التواصل</th>
                                            <th>النوع</th>
                                            <th>الحالة</th>
                                            <th>آخر نشاط</th>
                                            <th>إجمالي الحجوزات</th>
                                            <th width="120">الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="customersTableBody">
                                        <!-- سيتم ملؤها ديناميكياً -->
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Cards View -->
                        <div id="cardsView" class="d-none p-3">
                            <div class="row" id="customersCardsContainer">
                                <!-- سيتم ملؤها ديناميكياً -->
                            </div>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-between align-items-center p-3 border-top">
                            <div class="pagination-info">
                                <span class="text-muted" id="paginationInfo">عرض 1-15 من 150 عميل</span>
                            </div>
                            <nav>
                                <ul class="pagination pagination-sm mb-0" id="paginationNav">
                                    <!-- سيتم ملؤها ديناميكياً -->
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Customer Modal -->
            ${this.renderCustomerModal()}
        `;

        // عرض المحتوى
        if (window.UI && window.UI.showPage) {
            window.UI.showPage(content);
        } else {
            document.getElementById('main-content').innerHTML = content;
        }

        // تحميل البيانات
        this.loadCustomers();
        
        // تهيئة الأحداث
        this.initEvents();
    },

    /**
     * تحميل بيانات العملاء
     */
    loadCustomers: function() {
        this.state.isLoading = true;
        
        try {
            // محاكاة تحميل البيانات من قاعدة البيانات
            setTimeout(() => {
                // بيانات وهمية للعملاء
                this.state.customers = this.generateSampleCustomers();
                
                // تطبيق الفلاتر
                this.applyFilters();
                
                // تحديث الإحصائيات
                this.updateStatistics();
                
                // عرض البيانات
                this.renderCustomers();
                
                this.state.isLoading = false;
                
            }, 1000);
            
        } catch (error) {
            console.error('خطأ في تحميل بيانات العملاء:', error);
            this.state.isLoading = false;
        }
    },

    /**
     * توليد بيانات عملاء وهمية
     */
    generateSampleCustomers: function() {
        const customers = [];
        const names = ['أحمد محمد', 'فاطمة أحمد', 'محمد علي', 'سارة محمد', 'علي حسن', 'نورا عبدالله'];
        const types = ['individual', 'corporate', 'agent'];
        const statuses = ['active', 'inactive', 'vip'];
        
        for (let i = 1; i <= 150; i++) {
            customers.push({
                id: i,
                name: names[Math.floor(Math.random() * names.length)] + ' ' + i,
                email: `customer${i}@example.com`,
                phone: `+966${Math.floor(Math.random() * 900000000) + 100000000}`,
                type: types[Math.floor(Math.random() * types.length)],
                status: statuses[Math.floor(Math.random() * statuses.length)],
                totalBookings: Math.floor(Math.random() * 50),
                totalSpent: Math.floor(Math.random() * 100000),
                lastActivity: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
                createdAt: new Date(Date.now() - Math.random() * 365 * 24 * 60 * 60 * 1000),
                avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(names[Math.floor(Math.random() * names.length)])}&background=2c5aa0&color=fff`
            });
        }
        
        return customers;
    },

    /**
     * تطبيق الفلاتر
     */
    applyFilters: function() {
        let filtered = [...this.state.customers];

        // فلتر البحث
        if (this.state.filters.search) {
            const search = this.state.filters.search.toLowerCase();
            filtered = filtered.filter(customer =>
                customer.name.toLowerCase().includes(search) ||
                customer.email.toLowerCase().includes(search) ||
                customer.phone.includes(search)
            );
        }

        // فلتر الحالة
        if (this.state.filters.status) {
            filtered = filtered.filter(customer => customer.status === this.state.filters.status);
        }

        // فلتر النوع
        if (this.state.filters.type) {
            filtered = filtered.filter(customer => customer.type === this.state.filters.type);
        }

        // فلتر التاريخ
        if (this.state.filters.dateFrom) {
            const fromDate = new Date(this.state.filters.dateFrom);
            filtered = filtered.filter(customer => customer.createdAt >= fromDate);
        }

        if (this.state.filters.dateTo) {
            const toDate = new Date(this.state.filters.dateTo);
            filtered = filtered.filter(customer => customer.createdAt <= toDate);
        }

        // ترتيب النتائج
        filtered.sort((a, b) => {
            const aValue = a[this.state.sortBy];
            const bValue = b[this.state.sortBy];

            if (this.state.sortOrder === 'asc') {
                return aValue > bValue ? 1 : -1;
            } else {
                return aValue < bValue ? 1 : -1;
            }
        });

        this.state.filteredCustomers = filtered;
        this.state.totalPages = Math.ceil(filtered.length / this.config.itemsPerPage);
        this.state.currentPage = 1;
    },

    /**
     * تحديث الإحصائيات
     */
    updateStatistics: function() {
        const total = this.state.customers.length;
        const active = this.state.customers.filter(c => c.status === 'active').length;
        const vip = this.state.customers.filter(c => c.status === 'vip').length;
        const newThisMonth = this.state.customers.filter(c => {
            const monthAgo = new Date();
            monthAgo.setMonth(monthAgo.getMonth() - 1);
            return c.createdAt >= monthAgo;
        }).length;

        // تحديث الأرقام مع تأثير العد التدريجي
        this.animateNumber('totalCustomers', total);
        this.animateNumber('activeCustomers', active);
        this.animateNumber('vipCustomers', vip);
        this.animateNumber('newCustomers', newThisMonth);
    },

    /**
     * عرض العملاء
     */
    renderCustomers: function() {
        const startIndex = (this.state.currentPage - 1) * this.config.itemsPerPage;
        const endIndex = startIndex + this.config.itemsPerPage;
        const pageCustomers = this.state.filteredCustomers.slice(startIndex, endIndex);

        // عرض في الجدول
        this.renderCustomersTable(pageCustomers);

        // عرض في البطاقات
        this.renderCustomersCards(pageCustomers);

        // تحديث معلومات الصفحة
        this.updatePagination();
    },

    /**
     * عرض العملاء في الجدول
     */
    renderCustomersTable: function(customers) {
        const tbody = document.getElementById('customersTableBody');
        if (!tbody) return;

        tbody.innerHTML = customers.map(customer => `
            <tr>
                <td>
                    <input type="checkbox" class="form-check-input customer-checkbox" value="${customer.id}">
                </td>
                <td>
                    <div class="d-flex align-items-center">
                        <img src="${customer.avatar}" alt="${customer.name}"
                             class="rounded-circle me-3" width="40" height="40">
                        <div>
                            <h6 class="mb-0">${customer.name}</h6>
                            <small class="text-muted">ID: ${customer.id}</small>
                        </div>
                    </div>
                </td>
                <td>
                    <div>
                        <div><i class="fas fa-envelope text-muted me-2"></i>${customer.email}</div>
                        <div><i class="fas fa-phone text-muted me-2"></i>${customer.phone}</div>
                    </div>
                </td>
                <td>
                    <span class="badge bg-${this.getTypeBadgeColor(customer.type)}">
                        ${this.getTypeDisplayName(customer.type)}
                    </span>
                </td>
                <td>
                    <span class="badge bg-${this.getStatusBadgeColor(customer.status)}">
                        ${this.getStatusDisplayName(customer.status)}
                    </span>
                </td>
                <td>
                    <small class="text-muted">${this.formatDate(customer.lastActivity)}</small>
                </td>
                <td>
                    <div>
                        <strong>${customer.totalBookings}</strong> حجز<br>
                        <small class="text-muted">${customer.totalSpent.toLocaleString('ar-SA')} ر.س</small>
                    </div>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="AdvancedCustomers.viewCustomer(${customer.id})" title="عرض">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-success" onclick="AdvancedCustomers.editCustomer(${customer.id})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="AdvancedCustomers.deleteCustomer(${customer.id})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    },

    /**
     * عرض العملاء في البطاقات
     */
    renderCustomersCards: function(customers) {
        const container = document.getElementById('customersCardsContainer');
        if (!container) return;

        container.innerHTML = customers.map(customer => `
            <div class="col-xl-4 col-lg-6 mb-4">
                <div class="dashboard-card h-100">
                    <div class="card-body-enhanced text-center">
                        <img src="${customer.avatar}" alt="${customer.name}"
                             class="rounded-circle mb-3" width="80" height="80">
                        <h5 class="card-title">${customer.name}</h5>
                        <p class="text-muted mb-2">${customer.email}</p>
                        <p class="text-muted mb-3">${customer.phone}</p>

                        <div class="row text-center mb-3">
                            <div class="col-6">
                                <h6 class="text-primary mb-0">${customer.totalBookings}</h6>
                                <small class="text-muted">حجز</small>
                            </div>
                            <div class="col-6">
                                <h6 class="text-success mb-0">${customer.totalSpent.toLocaleString('ar-SA')}</h6>
                                <small class="text-muted">ر.س</small>
                            </div>
                        </div>

                        <div class="mb-3">
                            <span class="badge bg-${this.getStatusBadgeColor(customer.status)} me-2">
                                ${this.getStatusDisplayName(customer.status)}
                            </span>
                            <span class="badge bg-${this.getTypeBadgeColor(customer.type)}">
                                ${this.getTypeDisplayName(customer.type)}
                            </span>
                        </div>

                        <div class="btn-group w-100">
                            <button class="btn btn-outline-primary btn-sm" onclick="AdvancedCustomers.viewCustomer(${customer.id})">
                                <i class="fas fa-eye me-1"></i>عرض
                            </button>
                            <button class="btn btn-outline-success btn-sm" onclick="AdvancedCustomers.editCustomer(${customer.id})">
                                <i class="fas fa-edit me-1"></i>تعديل
                            </button>
                            <button class="btn btn-outline-danger btn-sm" onclick="AdvancedCustomers.deleteCustomer(${customer.id})">
                                <i class="fas fa-trash me-1"></i>حذف
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    },

    /**
     * تحديث الترقيم
     */
    updatePagination: function() {
        const paginationNav = document.getElementById('paginationNav');
        const paginationInfo = document.getElementById('paginationInfo');
        const customersCount = document.getElementById('customersCount');

        if (!paginationNav) return;

        // تحديث معلومات الصفحة
        const startIndex = (this.state.currentPage - 1) * this.config.itemsPerPage + 1;
        const endIndex = Math.min(this.state.currentPage * this.config.itemsPerPage, this.state.filteredCustomers.length);

        if (paginationInfo) {
            paginationInfo.textContent = `عرض ${startIndex}-${endIndex} من ${this.state.filteredCustomers.length} عميل`;
        }

        if (customersCount) {
            customersCount.textContent = `عرض ${this.state.filteredCustomers.length} من ${this.state.customers.length} عميل`;
        }

        // إنشاء أزرار الترقيم
        let paginationHTML = '';

        // زر السابق
        paginationHTML += `
            <li class="page-item ${this.state.currentPage === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="AdvancedCustomers.goToPage(${this.state.currentPage - 1})">السابق</a>
            </li>
        `;

        // أرقام الصفحات
        const maxVisiblePages = 5;
        let startPage = Math.max(1, this.state.currentPage - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(this.state.totalPages, startPage + maxVisiblePages - 1);

        if (endPage - startPage + 1 < maxVisiblePages) {
            startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }

        for (let i = startPage; i <= endPage; i++) {
            paginationHTML += `
                <li class="page-item ${i === this.state.currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="AdvancedCustomers.goToPage(${i})">${i}</a>
                </li>
            `;
        }

        // زر التالي
        paginationHTML += `
            <li class="page-item ${this.state.currentPage === this.state.totalPages ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="AdvancedCustomers.goToPage(${this.state.currentPage + 1})">التالي</a>
            </li>
        `;

        paginationNav.innerHTML = paginationHTML;
    }
};
