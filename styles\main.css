/* ملف CSS الرئيسي لنظام إدارة وكالة السفر المحسن */

:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    --border-radius: 15px;
    --box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    --transition: all 0.3s ease;
}

* {
    font-family: 'Cairo', sans-serif;
}

html, body {
    height: 100%;
    margin: 0;
    padding: 0;
    overflow-x: hidden;
}

body {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    direction: rtl;
    min-height: 100vh;
    width: 100%;
}

/* شريط التنقل المحسن */
.navbar {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color)) !important;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    padding: 0.5rem 0;
    width: 100%;
    position: sticky;
    top: 0;
    z-index: 1030;
    font-size: 0.9rem;
}

.navbar-expand-xl .navbar-nav {
    flex-direction: row;
}

.navbar-nav .nav-item {
    margin: 0 2px;
}

.navbar-nav .nav-link {
    padding: 0.5rem 0.8rem !important;
    border-radius: 6px;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.navbar-nav .nav-link i {
    font-size: 1rem;
}

.navbar-nav .nav-link .nav-text {
    font-size: 0.85rem;
    font-weight: 500;
}

.navbar-nav .nav-link:hover {
    background: rgba(255, 255, 255, 0.15);
    transform: translateY(-1px);
}

.navbar-nav .nav-link.active {
    background: rgba(255, 255, 255, 0.2);
    font-weight: 600;
}

.user-menu {
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
}

.navbar-brand {
    font-weight: 700;
    font-size: 1.8rem;
    color: white !important;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.navbar-nav .nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
    padding: 0.7rem 1rem !important;
    border-radius: 8px;
    margin: 0 2px;
    transition: var(--transition);
}

.navbar-nav .nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white !important;
    transform: translateY(-1px);
}

.dropdown-menu {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
}

.dropdown-item {
    padding: 0.7rem 1.5rem;
    font-weight: 500;
    transition: var(--transition);
    border-radius: 8px;
    margin: 2px 8px;
}

.dropdown-item:hover {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: white;
    transform: translateX(5px);
}

/* البطاقات المحسنة */
.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: var(--transition);
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.card-header {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    padding: 1.2rem 1.5rem;
    font-weight: 600;
}

.card-body {
    padding: 1.5rem;
}

.card-hover:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* بطاقات الإحصائيات */
.card.bg-primary,
.card.bg-success,
.card.bg-warning,
.card.bg-info {
    background: linear-gradient(135deg, var(--bs-primary) 0%, var(--bs-primary-dark, #0a58ca) 100%);
}

.card.bg-success {
    background: linear-gradient(135deg, var(--bs-success) 0%, #157347 100%);
}

.card.bg-warning {
    background: linear-gradient(135deg, var(--bs-warning) 0%, #e08e0b 100%);
}

.card.bg-info {
    background: linear-gradient(135deg, var(--bs-info) 0%, #087990 100%);
}

/* الأزرار المحسنة */
.btn {
    border-radius: 12px;
    font-weight: 600;
    padding: 0.7rem 1.5rem;
    transition: var(--transition);
    border: none;
    position: relative;
    overflow: hidden;
}

.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn:hover::before {
    left: 100%;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.btn-primary {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
}

.btn-success {
    background: linear-gradient(45deg, #28a745, #20c997);
}

.btn-danger {
    background: linear-gradient(45deg, #dc3545, #e83e8c);
}

.btn-warning {
    background: linear-gradient(45deg, #ffc107, #fd7e14);
}

.btn-info {
    background: linear-gradient(45deg, #17a2b8, #6f42c1);
}

.btn-group .btn {
    margin: 0 2px;
}

/* الجداول المحسنة */
.table {
    border-radius: var(--border-radius);
    overflow: hidden;
    background: white;
    box-shadow: var(--box-shadow);
}

.table thead th {
    background: linear-gradient(45deg, var(--primary-color), var(--secondary-color));
    color: white;
    border: none;
    font-weight: 600;
    padding: 1rem;
    text-align: center;
}

.table tbody tr {
    transition: var(--transition);
}

.table tbody tr:hover {
    background: rgba(102, 126, 234, 0.05);
    transform: scale(1.01);
}

.table tbody td {
    padding: 1rem;
    vertical-align: middle;
    border-color: rgba(0, 0, 0, 0.05);
}

/* النماذج المحسنة */
.form-control, .form-select {
    border-radius: 12px;
    border: 2px solid #e9ecef;
    padding: 0.8rem 1rem;
    transition: var(--transition);
    background: rgba(255, 255, 255, 0.9);
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    background: white;
}

.form-label {
    font-weight: 600;
    color: var(--dark-color);
    margin-bottom: 0.7rem;
}

/* الشارات المحسنة */
.badge {
    font-size: 0.85rem;
    padding: 0.6em 1em;
    border-radius: 20px;
    font-weight: 500;
}

/* التنبيهات المحسنة */
.alert {
    border-radius: var(--border-radius);
    border: none;
    padding: 1.2rem 1.5rem;
    font-weight: 500;
    box-shadow: var(--box-shadow);
    position: relative;
    overflow: hidden;
}

.alert::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: currentColor;
    opacity: 0.8;
}

/* تحسين التنبيهات المؤقتة */
.alert.position-fixed {
    top: 20px;
    right: 20px;
    z-index: 9999;
    min-width: 300px;
    max-width: 500px;
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* تحسين أيقونات التنبيهات */
.alert i {
    margin-left: 0.5rem;
    font-size: 1.1em;
}

/* تحسين ألوان التنبيهات */
.alert-success {
    background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
    color: #155724;
    border-left: 4px solid #28a745;
}

.alert-danger {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
    border-left: 4px solid #dc3545;
}

.alert-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
    color: #856404;
    border-left: 4px solid #ffc107;
}

.alert-info {
    background: linear-gradient(135deg, #d1ecf1 0%, #bee5eb 100%);
    color: #0c5460;
    border-left: 4px solid #17a2b8;
}

/* الأقسام والرسوم المتحركة */
.content-section {
    display: none;
    padding: 2rem 0;
}

.content-section.active {
    display: block;
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* بطاقات الإحصائيات المحسنة */
.stats-card {
    background: linear-gradient(45deg, var(--success-color), #20c997);
    color: white;
    border-radius: var(--border-radius);
    padding: 2rem;
    text-align: center;
    position: relative;
    overflow: hidden;
}

.stats-card::before {
    content: '';
    position: absolute;
    top: -50%;
    right: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: pulse 3s infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); opacity: 0.7; }
    50% { transform: scale(1.1); opacity: 1; }
}

.stats-card h3 {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    position: relative;
    z-index: 2;
}

.stats-card p {
    font-size: 1.1rem;
    margin: 0;
    position: relative;
    z-index: 2;
}

/* تحسينات للطباعة */
@media print {
    .navbar, .btn, .no-print, .dropdown {
        display: none !important;
    }

    .card {
        box-shadow: none;
        border: 1px solid #ddd;
        break-inside: avoid;
    }

    body {
        background: white !important;
    }

    .table {
        font-size: 12px;
    }

    .table th, .table td {
        padding: 0.5rem !important;
    }

    h1, h2, h3, h4, h5, h6 {
        color: black !important;
    }

    .print-header {
        text-align: center;
        margin-bottom: 2rem;
        border-bottom: 2px solid #000;
        padding-bottom: 1rem;
    }

    .print-footer {
        text-align: center;
        margin-top: 2rem;
        border-top: 1px solid #000;
        padding-top: 1rem;
        font-size: 0.9rem;
    }
}

/* تحسينات للشاشات الصغيرة */
@media (max-width: 768px) {
    .container-fluid {
        padding-left: 10px;
        padding-right: 10px;
    }

    .main-content {
        padding: 15px 0;
    }

    /* تحسين الجداول للشاشات الصغيرة */
    .table-responsive {
        border-radius: var(--border-radius);
        box-shadow: var(--box-shadow);
    }

    .table {
        font-size: 0.85rem;
    }

    .table th, .table td {
        padding: 0.5rem 0.25rem;
        white-space: nowrap;
    }

    /* تحسين النماذج للشاشات الصغيرة */
    .form-control, .form-select {
        font-size: 16px; /* منع التكبير في iOS */
    }

    .navbar-expand-xl .navbar-nav {
        flex-direction: column;
        width: 100%;
    }

    .navbar-nav .nav-item {
        margin: 2px 0;
        width: 100%;
    }

    .navbar-nav .nav-link {
        padding: 0.75rem 1rem !important;
        justify-content: flex-start;
    }

    .navbar-nav .nav-link .nav-text {
        font-size: 0.9rem;
    }

    .page-header {
        padding: 1.5rem;
        text-align: center;
    }

    .page-header h1 {
        font-size: 2rem;
    }

    .module-card {
        margin-bottom: 1rem;
    }

    .module-icon {
        width: 60px;
        height: 60px;
    }

    .module-icon i {
        font-size: 1.5rem;
    }

    .quick-action-btn {
        height: 100px;
        font-size: 0.85rem;
    }

    .quick-action-btn i {
        font-size: 1.5rem;
    }

    .stat-item h3 {
        font-size: 1.5rem;
    }

    .activity-item {
        padding: 0.5rem 0;
    }

    .activity-item i {
        width: 35px;
        height: 35px;
        font-size: 1rem;
    }

    .card-body {
        padding: 1rem;
    }

    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }

    .table-responsive {
        font-size: 0.85rem;
    }

    .navbar-brand {
        font-size: 1.2rem;
    }

    .navbar {
        padding: 0.5rem 0;
    }
}

/* تحسينات للشاشات الكبيرة */
@media (min-width: 1200px) {
    .container-fluid {
        max-width: 100%;
        padding-left: 30px;
        padding-right: 30px;
    }

    .dashboard-cards .col-lg-3 {
        padding: 15px;
    }

    .card-body {
        padding: 2rem;
    }
}

/* تحسينات للشاشات المتوسطة */
@media (min-width: 768px) and (max-width: 1199px) {
    .navbar-nav .nav-link .nav-text {
        font-size: 0.8rem;
    }

    .navbar-nav .nav-link {
        padding: 0.4rem 0.6rem !important;
    }

    .module-icon {
        width: 70px;
        height: 70px;
    }

    .quick-action-btn {
        height: 110px;
    }

    .page-header h1 {
        font-size: 2.5rem;
    }
}

/* تحسينات للشاشات الكبيرة جداً */
@media (min-width: 1400px) {
    .container-fluid {
        max-width: 1400px;
        margin: 0 auto;
    }

    .navbar-nav .nav-link {
        padding: 0.6rem 1rem !important;
    }

    .navbar-nav .nav-link .nav-text {
        font-size: 0.9rem;
    }

    .module-icon {
        width: 90px;
        height: 90px;
    }

    .quick-action-btn {
        height: 130px;
    }

    .page-header {
        padding: 3rem;
    }

    .page-header h1 {
        font-size: 3.5rem;
    }
}

/* تأثيرات إضافية */
.loading {
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.btn-outline-primary:hover,
.btn-outline-success:hover,
.btn-outline-info:hover,
.btn-outline-warning:hover {
    transform: translateY(-1px);
}

/* الجداول */
.table {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.table thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
    padding: 1rem;
}

.table tbody td {
    padding: 1rem;
    vertical-align: middle;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* النماذج */
.form-control,
.form-select {
    border-radius: 8px;
    border: 1px solid #ced4da;
    padding: 0.75rem 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
}

.form-control:focus,
.form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

/* المودال */
.modal-content {
    border: none;
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.modal-header {
    border-bottom: 1px solid #e9ecef;
    border-radius: 12px 12px 0 0;
    padding: 1.5rem;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    border-radius: 0 0 12px 12px;
    padding: 1.5rem;
}

/* أقسام المحتوى */
.content-section {
    display: none;
}

.content-section.active {
    display: block;
    animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* العناوين */
h1, h2, h3, h4, h5, h6 {
    color: #2c3e50;
    font-weight: 700;
}

/* الأيقونات */
.fas, .far {
    margin-left: 0.5rem;
}

/* الشاشات الصغيرة */
@media (max-width: 768px) {
    .navbar-brand {
        font-size: 1.2rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .btn {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
    
    .table {
        font-size: 0.9rem;
    }
    
    .table thead th,
    .table tbody td {
        padding: 0.75rem 0.5rem;
    }
}

/* تحسينات إضافية */
.opacity-75 {
    opacity: 0.75;
}

.text-muted {
    color: #6c757d !important;
}

/* تأثيرات التحويم */
.card-hover:hover {
    transform: scale(1.02);
    transition: transform 0.2s ease;
}

/* ألوان مخصصة */
:root {
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --success-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --warning-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    --info-gradient: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

/* تحسين المظهر العام */
.container-fluid {
    max-width: 100%;
    padding-left: 15px;
    padding-right: 15px;
    margin: 0;
    width: 100%;
}

/* تحسين المحتوى الرئيسي */
.main-content {
    min-height: calc(100vh - 80px);
    padding: 20px 0;
    width: 100%;
}

/* تحسين الصفحة الرئيسية */
.dashboard-container {
    width: 100%;
    max-width: 100%;
    padding: 0;
}

/* تحسين بطاقات الصفحة الرئيسية */
.dashboard-cards {
    width: 100%;
    margin: 0;
    padding: 0 15px;
}

.dashboard-cards .row {
    margin: 0;
    width: 100%;
}

.dashboard-cards .col-md-4 {
    padding: 10px;
    width: 33.333333%;
    flex: 0 0 33.333333%;
    max-width: 33.333333%;
}

/* تحسين الرسوم البيانية */
.chart-container {
    width: 100%;
    height: 400px;
    position: relative;
}

/* تحسين الجداول */
.table-container {
    width: 100%;
    overflow-x: auto;
    margin-bottom: 1rem;
}

/* تحسين المودال */
.modal-dialog {
    max-width: 90%;
    margin: 1.75rem auto;
}

.modal-xl {
    max-width: 95%;
}

/* تحسين النماذج */
.form-container {
    width: 100%;
    max-width: 100%;
}

/* تحسين الأزرار */
.btn-container {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
}

/* تحسين البطاقات */
.card-container {
    width: 100%;
    margin-bottom: 1.5rem;
}

/* تحسين الشبكة */
.row {
    margin-left: -15px;
    margin-right: -15px;
}

.row > * {
    padding-left: 15px;
    padding-right: 15px;
}

/* تحسينات الجداول الكبيرة - إضافة جديدة */
.table-responsive {
    position: relative;
}

/* تحسين التمرير الأفقي */
.table-responsive::-webkit-scrollbar {
    height: 12px;
    width: 12px;
}

.table-responsive::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 6px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 6px;
    border: 2px solid #f1f1f1;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: #555;
}

/* تثبيت العمود الأول والأخير */
.table-fixed {
    table-layout: auto;
}

.table-fixed th:first-child,
.table-fixed td:first-child {
    position: sticky;
    left: 0;
    background: inherit;
    z-index: 10;
    box-shadow: 2px 0 5px rgba(0,0,0,0.1);
}

.table-fixed th:last-child,
.table-fixed td:last-child {
    position: sticky;
    right: 0;
    background: inherit;
    z-index: 10;
    box-shadow: -2px 0 5px rgba(0,0,0,0.1);
}

/* تحسين عرض الأعمدة */
.table th, .table td {
    white-space: nowrap;
    vertical-align: middle;
    padding: 0.75rem 0.5rem;
}

/* تحسين الخلايا النقدية */
.currency-cell {
    font-family: 'Courier New', monospace;
    text-align: left;
    direction: ltr;
}

/* تحسين شارات الحالة */
.transaction-status-badge {
    min-width: 120px;
    text-align: center;
}

.status-icon {
    margin-left: 0.25rem;
}

/* تحسين مؤشر التمرير */
.scroll-indicator {
    position: absolute;
    top: 10px;
    right: 10px;
    background: rgba(0,0,0,0.7);
    color: white;
    padding: 5px 10px;
    border-radius: 15px;
    font-size: 0.8rem;
    z-index: 20;
    pointer-events: none;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.table-responsive:hover .scroll-indicator {
    opacity: 1;
}

/* تحسينات إضافية للجداول الكبيرة */
@media (max-width: 768px) {
    /* إخفاء بعض الأعمدة في الشاشات الصغيرة */
    .table th:nth-child(n+10):not(:last-child),
    .table td:nth-child(n+10):not(:last-child) {
        display: none;
    }

    .table th:last-child,
    .table td:last-child {
        display: table-cell !important;
    }

    .table-responsive {
        max-height: 60vh;
    }
}

/* تحسين المحتوى */
.content-wrapper {
    width: 100%;
    min-height: calc(100vh - 80px);
    padding: 0;
}

/* تحسين الهيدر */
.page-header {
    width: 100%;
    margin-bottom: 2rem;
    padding: 1rem 0;
    border-bottom: 1px solid #e9ecef;
}

/* تحسين الفوتر */
.page-footer {
    width: 100%;
    margin-top: 2rem;
    padding: 1rem 0;
    border-top: 1px solid #e9ecef;
}

/* تنسيق خاص للنصوص العربية */
.arabic-text {
    font-family: 'Cairo', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.8;
}

/* تحسين شكل الإشعارات */
.alert {
    border: none;
    border-radius: 8px;
    font-weight: 500;
}

/* تحسين شكل الشارات */
.badge {
    font-weight: 600;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
}

/* تحسين المساحات */
.section-spacing {
    margin-bottom: 2rem;
}

.card-spacing {
    margin-bottom: 1.5rem;
}

/* تحسينات خاصة بإدارة العملاء */
.customers-table {
    font-size: 0.9rem;
}

.customers-table th {
    background-color: #343a40 !important;
    color: white !important;
    font-weight: 600;
    text-align: center;
    vertical-align: middle;
    white-space: nowrap;
}

.customers-table td {
    vertical-align: middle;
    white-space: nowrap;
}

.customers-table .btn-group {
    display: flex;
    gap: 2px;
}

.customers-table .btn-sm {
    padding: 0.25rem 0.5rem;
    font-size: 0.8rem;
}

/* تحسين المودال */
.modal-xl {
    max-width: 95%;
}

.modal-header.bg-primary {
    background: linear-gradient(135deg, #0d6efd 0%, #0a58ca 100%) !important;
}

/* تحسين البطاقات في الصفحة الرئيسية */
.card-hover {
    transition: all 0.3s ease;
    cursor: pointer;
}

.card-hover:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.card-hover .fa-4x {
    transition: all 0.3s ease;
}

.card-hover:hover .fa-4x {
    transform: scale(1.1);
}

/* تحسين الجداول */
.table-responsive {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.table-dark th {
    border: none;
    font-size: 0.85rem;
    padding: 1rem 0.75rem;
}

.table-striped tbody tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

/* تحسين الشارات */
.badge {
    font-size: 0.75rem;
    padding: 0.4rem 0.6rem;
}

/* تحسين النماذج */
.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.form-control:focus,
.form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* تحسين الأزرار */
.btn-group .btn {
    border-radius: 4px !important;
    margin: 0 1px;
}

/* تحسين الإحصائيات */
.stats-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
}

.stats-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stats-card h3 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stats-card small {
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* تحسين التنقل */
.breadcrumb {
    background-color: transparent;
    padding: 0;
    margin-bottom: 1rem;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "←";
    color: #6c757d;
}

/* تحسين الرسائل */
.alert {
    border: none;
    border-radius: 8px;
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* تحسين التمرير */
.table-responsive::-webkit-scrollbar {
    height: 8px;
}

.table-responsive::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* تحسينات التقارير */
.report-filters {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.report-stats .card {
    transition: all 0.3s ease;
}

.report-stats .card:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.progress {
    border-radius: 10px;
    overflow: hidden;
}

.progress-bar {
    transition: width 0.6s ease;
}

/* تحسين جداول التقارير */
.report-table {
    font-size: 0.9rem;
}

.report-table th {
    background: linear-gradient(135deg, #343a40 0%, #495057 100%) !important;
    color: white !important;
    font-weight: 600;
    text-align: center;
    border: none;
}

.report-table td {
    vertical-align: middle;
    border-color: #e9ecef;
}

.report-table .badge {
    font-size: 0.8rem;
    padding: 0.4rem 0.6rem;
}

/* تحسين الفلاتر */
.filter-section {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border: 1px solid #e9ecef;
}

.filter-section .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.filter-section .form-control,
.filter-section .form-select {
    border: 1px solid #ced4da;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.filter-section .form-control:focus,
.filter-section .form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* تحسين بطاقات الإحصائيات */
.stats-card-report {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 1.5rem;
    text-align: center;
    transition: all 0.3s ease;
    height: 100%;
}

.stats-card-report:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stats-card-report h3 {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.stats-card-report small {
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    color: #6c757d;
}

/* تحسين أزرار التقارير */
.report-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.report-actions .btn {
    border-radius: 6px;
    font-weight: 500;
    padding: 0.5rem 1rem;
    transition: all 0.3s ease;
}

.report-actions .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* تحسين المودال للتقارير */
.report-modal .modal-content {
    border-radius: 12px;
    border: none;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.2);
}

.report-modal .modal-header {
    background: linear-gradient(135deg, #0d6efd 0%, #0a58ca 100%);
    color: white;
    border-radius: 12px 12px 0 0;
    border-bottom: none;
}

/* تحسين الرسوم البيانية */
.chart-container {
    position: relative;
    height: 300px;
    margin: 1rem 0;
}

/* تنسيق المرفقات */
.attachment-item {
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
    background-color: #fff;
}

.attachment-item:hover {
    border-color: #007bff;
    box-shadow: 0 2px 10px rgba(0,123,255,0.1);
    transform: translateY(-2px);
}

.attachment-card {
    border: 1px solid #e9ecef;
    transition: all 0.3s ease;
    background-color: #fff;
}

.attachment-card:hover {
    border-color: #007bff;
    box-shadow: 0 4px 15px rgba(0,123,255,0.15);
    transform: translateY(-3px);
}

.attachments-preview {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #e9ecef;
    border-radius: 0.5rem;
    padding: 1rem;
    background-color: #f8f9fa;
}

/* تحسين مظهر input الملفات */
.form-control[type="file"] {
    border: 2px dashed #007bff;
    background-color: #f8f9ff;
    padding: 1rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.form-control[type="file"]:hover {
    border-color: #0056b3;
    background-color: #e6f3ff;
}

.form-control[type="file"]:focus {
    border-color: #0056b3;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,0.25);
}

/* أيقونات الملفات */
.file-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

/* تحسين مظهر المودال للمرفقات */
.modal-lg {
    max-width: 900px;
}

.modal-header.bg-primary {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
}

/* تحسين عرض الصور في المعاينة */
.modal-body img {
    max-width: 100%;
    height: auto;
    border-radius: 0.5rem;
    box-shadow: 0 4px 15px rgba(0,0,0,0.1);
}

/* تحسين أزرار المرفقات */
.attachment-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.attachment-actions .btn {
    border-radius: 4px;
    font-size: 0.8rem;
    padding: 0.25rem 0.5rem;
    transition: all 0.3s ease;
}

.attachment-actions .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* تحسين عرض معلومات الملف */
.file-info {
    font-size: 0.8rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

.file-size {
    font-weight: 500;
}

.file-date {
    font-style: italic;
}

/* تحسين شكل منطقة السحب والإفلات */
.drag-drop-area {
    border: 2px dashed #007bff;
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    background-color: #f8f9ff;
    transition: all 0.3s ease;
    cursor: pointer;
}

.drag-drop-area:hover {
    border-color: #0056b3;
    background-color: #e6f3ff;
}

.drag-drop-area.dragover {
    border-color: #28a745;
    background-color: #f0fff4;
}

/* تحسين عرض قائمة المرفقات */
.attachments-list {
    max-height: 400px;
    overflow-y: auto;
}

.attachments-list::-webkit-scrollbar {
    width: 6px;
}

.attachments-list::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 3px;
}

.attachments-list::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 3px;
}

.attachments-list::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* تحسين عرض أنواع الملفات */
.file-type-info {
    background-color: #f8f9fa;
    border-radius: 6px;
    padding: 1rem;
    margin-top: 1rem;
}

.file-type-info ul {
    margin-bottom: 0;
    padding-left: 1rem;
}

.file-type-info li {
    margin-bottom: 0.25rem;
    font-size: 0.85rem;
}

/* تحسين رسائل التحقق */
.file-validation-message {
    font-size: 0.8rem;
    margin-top: 0.5rem;
    padding: 0.5rem;
    border-radius: 4px;
}

.file-validation-message.success {
    background-color: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.file-validation-message.error {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

/* تحسين عداد الملفات */
.file-counter {
    background-color: #007bff;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    font-size: 0.7rem;
    font-weight: bold;
    margin-left: 0.25rem;
}

/* تحسين التنسيق للطباعة */
@media print {
    .no-print {
        display: none !important;
    }

    .card {
        border: 1px solid #ddd !important;
        box-shadow: none !important;
        break-inside: avoid;
    }

    .table {
        font-size: 11px;
        break-inside: avoid;
    }

    .btn {
        display: none;
    }

    .report-stats {
        break-inside: avoid;
    }

    .page-break {
        page-break-before: always;
    }

    .report-header {
        text-align: center;
        margin-bottom: 2rem;
        border-bottom: 2px solid #333;
        padding-bottom: 1rem;
    }

    .report-title {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 0.5rem;
    }

    .report-subtitle {
        font-size: 16px;
        color: #666;
    }
}

/* تحسينات المنتجات والخدمات */
.products-filters {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px;
    border: 1px solid #dee2e6;
    margin-bottom: 1.5rem;
}

.products-filters .form-text {
    font-size: 0.75rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

.products-filters .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.products-filters .form-select,
.products-filters .form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.products-filters .form-select:focus,
.products-filters .form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* بطاقات فئات المنتجات */
.category-card {
    transition: all 0.3s ease;
    border: 1px solid #e9ecef;
    border-radius: 8px;
}

.category-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    border-color: #007bff;
}

.category-card .card-body {
    padding: 1.5rem;
}

.category-card i {
    transition: all 0.3s ease;
}

.category-card:hover i {
    transform: scale(1.1);
}

/* نموذج إضافة المنتج */
.product-form .card {
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    margin-bottom: 1.5rem;
}

.product-form .card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    border-radius: 8px 8px 0 0;
}

.product-form .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.product-form .form-control,
.product-form .form-select {
    border-radius: 6px;
    border: 1px solid #ced4da;
    transition: all 0.3s ease;
}

.product-form .form-control:focus,
.product-form .form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.product-form .input-group .btn {
    border-radius: 0 6px 6px 0;
}

/* بطاقات الحسابات */
.profit-card {
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    border: 1px solid #e9ecef;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.profit-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.profit-card .card-body {
    padding: 1.5rem;
}

.profit-card h4 {
    font-weight: bold;
    margin-bottom: 0;
}

/* مفاتيح التبديل */
.form-check-input:checked {
    background-color: #198754;
    border-color: #198754;
}

.form-check-input:focus {
    border-color: #86b7fe;
    outline: 0;
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.form-check-label {
    font-weight: 500;
    color: #495057;
}

/* شارات المنتجات */
.product-badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
}

.category-badge {
    font-size: 0.8rem;
    padding: 0.4rem 0.6rem;
    border-radius: 6px;
    font-weight: 500;
}

.availability-badge {
    font-size: 0.75rem;
    padding: 0.3rem 0.5rem;
    border-radius: 4px;
    font-weight: 600;
}

/* جدول المنتجات */
.products-table {
    font-size: 0.9rem;
}

.products-table th {
    background-color: #495057;
    color: white;
    font-weight: 600;
    text-align: center;
    padding: 0.75rem 0.5rem;
    border: none;
}

.products-table td {
    padding: 0.75rem 0.5rem;
    vertical-align: middle;
    border-color: #e9ecef;
}

.products-table .btn-group {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border-radius: 6px;
    overflow: hidden;
}

.products-table .btn {
    border: none;
    padding: 0.375rem 0.5rem;
    transition: all 0.3s ease;
}

.products-table .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* ألوان مخصصة للفئات */
.text-purple {
    color: #6f42c1 !important;
}

.bg-purple {
    background-color: #6f42c1 !important;
}

/* تحسينات العملات */
.currency-input-group {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.currency-input-group .form-control {
    border-right: none;
    border-radius: 8px 0 0 8px;
}

.currency-input-group .currency-select {
    border-left: none;
    border-radius: 0 8px 8px 0;
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
    min-width: 100px;
}

.currency-input-group .currency-select:focus {
    background-color: #e9ecef;
    border-color: #0d6efd;
}

.currency-input-group:focus-within {
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

/* محول العملات */
.currency-converter-modal .modal-content {
    border-radius: 12px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.currency-converter-modal .modal-header {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    border-radius: 12px 12px 0 0;
}

.currency-converter-modal .exchange-rates {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px;
    padding: 1rem;
}

.currency-converter-modal .exchange-rate-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #dee2e6;
}

.currency-converter-modal .exchange-rate-item:last-child {
    border-bottom: none;
}

/* معلومات التحويل */
.conversion-info {
    font-size: 0.75rem;
    color: #6c757d;
    font-style: italic;
}

/* شارات العملة */
.currency-badge {
    font-size: 0.8rem;
    padding: 0.3rem 0.6rem;
    border-radius: 6px;
    font-weight: 600;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #495057;
    border: 1px solid #dee2e6;
}

.currency-badge.yer {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.currency-badge.sar {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    color: white;
}

.currency-badge.usd {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    color: #212529;
}

/* تحسينات الجدول للعملات */
.products-table .currency-cell {
    font-family: 'Courier New', monospace;
    font-weight: 600;
}

.products-table .currency-symbol {
    font-size: 0.9em;
    color: #6c757d;
    margin-left: 0.25rem;
}

/* شارات حالة المعاملة المحسنة */
.transaction-status-badge {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
    border-radius: 6px;
    font-weight: 600;
    text-align: center;
    white-space: nowrap;
}

/* ألوان مخصصة لحالات المعاملة */
.badge.bg-processing {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%) !important;
    color: white;
}

.badge.bg-office-delivered {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%) !important;
    color: white;
}

.badge.bg-embassy-pending {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%) !important;
    color: #212529;
}

.badge.bg-embassy-stamped {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
    color: white;
}

.badge.bg-office-stamped {
    background: linear-gradient(135deg, #28a745 0%, #1e7e34 100%) !important;
    color: white;
}

.badge.bg-delivered-stamped {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
    color: white;
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

.badge.bg-delivered-unstamped {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
    color: white;
    box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
}

/* تأثيرات تفاعلية للشارات */
.transaction-status-badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    transition: all 0.3s ease;
}

/* أيقونات للحالات */
.status-icon {
    margin-right: 0.5rem;
    font-size: 0.9em;
}

/* تحسينات للقوائم المنسدلة */
.form-select option[value="قيد المعاملة"] {
    background-color: #f8f9fa;
    color: #6c757d;
}

.form-select option[value="مسلم في المكتب"] {
    background-color: #e3f2fd;
    color: #1976d2;
}

.form-select option[value="قيد الترحيل للسفارة"] {
    background-color: #fff3e0;
    color: #f57c00;
}

.form-select option[value="مؤشر في السفارة"] {
    background-color: #e0f2f1;
    color: #00796b;
}

.form-select option[value="مؤشر في المكتب"] {
    background-color: #e8f5e8;
    color: #2e7d32;
}

.form-select option[value="مسلم للعميل مؤشر"] {
    background-color: #e8f5e8;
    color: #1b5e20;
    font-weight: 600;
}

.form-select option[value="مسلم للعميل غير مؤشر"] {
    background-color: #ffebee;
    color: #c62828;
    font-weight: 600;
}

/* تحسينات فلاتر مخزون التأشيرات */
.visa-inventory-filters {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px;
    border: 1px solid #dee2e6;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.visa-inventory-filters .form-text {
    font-size: 0.75rem;
    color: #6c757d;
    margin-top: 0.25rem;
}

.visa-inventory-filters .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.visa-inventory-filters .form-select,
.visa-inventory-filters .form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.visa-inventory-filters .form-select:focus,
.visa-inventory-filters .form-control:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.visa-inventory-filters .input-group .btn {
    border-radius: 0 6px 6px 0;
}

/* شارات الإحصائيات المحدثة */
.filter-stats {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 8px;
    padding: 1rem;
    border: 1px solid #e9ecef;
}

.filter-stats .badge {
    font-size: 0.85rem;
    padding: 0.5rem 0.75rem;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
    border-radius: 6px;
    transition: all 0.3s ease;
}

.filter-stats .badge:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* تحسين أزرار الفلاتر */
.filter-buttons {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-buttons .btn {
    border-radius: 6px;
    font-weight: 500;
    transition: all 0.3s ease;
    font-size: 0.85rem;
}

.filter-buttons .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

/* تحسينات واجهة الاستيراد */
.import-instructions {
    background: linear-gradient(135deg, #e3f2fd 0%, #f3e5f5 100%);
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.import-instructions h6 {
    color: #1976d2;
    font-weight: 600;
    margin-bottom: 1rem;
}

.import-instructions ol,
.import-instructions ul {
    margin-bottom: 0;
}

.import-instructions li {
    margin-bottom: 0.5rem;
    line-height: 1.6;
}

/* تحسين منطقة رفع الملف */
.file-upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    background-color: #f8f9fa;
}

.file-upload-area:hover {
    border-color: #0d6efd;
    background-color: #e7f3ff;
}

.file-upload-area.dragover {
    border-color: #0d6efd;
    background-color: #e7f3ff;
    transform: scale(1.02);
}

/* تحسين شريط التقدم */
.progress {
    height: 25px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.progress-bar {
    transition: width 0.6s ease;
    font-weight: 600;
    display: flex;
    align-items: center;
    justify-content: center;
}

.progress-bar-animated {
    animation: progress-bar-stripes 1s linear infinite;
}

/* تحسين جدول المعاينة */
.preview-table {
    font-size: 0.9rem;
}

.preview-table .table-danger {
    background-color: rgba(220, 53, 69, 0.1);
}

.preview-table .table-warning {
    background-color: rgba(255, 193, 7, 0.1);
}

.preview-table .sticky-top {
    position: sticky;
    top: 0;
    z-index: 10;
}

/* تحسين أيقونات الحالة */
.status-icon {
    font-size: 1.2rem;
    margin-right: 0.5rem;
}

.status-valid {
    color: #198754;
}

.status-warning {
    color: #ffc107;
}

.status-error {
    color: #dc3545;
}

/* تحسين بطاقات الإحصائيات */
.import-stats {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
}

.import-stats .badge {
    font-size: 0.9rem;
    padding: 0.5rem 0.75rem;
    margin-right: 0.5rem;
}

/* تحسين الأزرار */
.import-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
    justify-content: center;
}

.import-actions .btn {
    border-radius: 6px;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    transition: all 0.3s ease;
}

.import-actions .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.import-actions .btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

/* تحسين التنبيهات */
.import-alert {
    border-radius: 8px;
    border: none;
    font-weight: 500;
    padding: 1rem 1.5rem;
}

.import-alert .alert-heading {
    font-weight: 600;
    margin-bottom: 0.5rem;
}

/* تحسين التولتيب */
[title] {
    cursor: help;
}

/* تحسين التمرير */
.preview-container {
    max-height: 500px;
    overflow-y: auto;
}

.preview-container::-webkit-scrollbar {
    width: 8px;
}

.preview-container::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.preview-container::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

.preview-container::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* تحسينات قسم عملاء الوكيل المحدد */
.selected-agent-customers-section {
    border-top: 3px solid #17a2b8;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.selected-agent-customers-section .card-header {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
}

.selected-agent-customers-section .table-responsive {
    border-radius: 8px;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
}

.selected-agent-customers-section .sticky-top {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* تحسين شارات الإحصائيات */
.stats-badges {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 8px;
    padding: 1rem;
}

.stats-badges .badge {
    font-size: 0.9rem;
    padding: 0.5rem 0.75rem;
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
    border-radius: 6px;
}

/* تحسين أزرار الفرز والتصفية */
.filter-controls {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 8px;
    border: 1px solid #e9ecef;
}

.filter-controls .form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
}

.filter-controls .form-select,
.filter-controls .form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
    transition: all 0.3s ease;
}

.filter-controls .form-select:focus,
.filter-controls .form-control:focus {
    border-color: #17a2b8;
    box-shadow: 0 0 0 0.2rem rgba(23, 162, 184, 0.25);
}

/* تحسين الجدول المتداخل */
.nested-table {
    font-size: 0.85rem;
}

.nested-table th {
    background-color: #495057;
    color: white;
    font-weight: 600;
    text-align: center;
    padding: 0.75rem 0.5rem;
}

.nested-table td {
    padding: 0.5rem;
    vertical-align: middle;
}

.nested-table .badge {
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

/* تحسين أزرار الإجراءات */
.action-buttons .btn-group {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    border-radius: 6px;
    overflow: hidden;
}

.action-buttons .btn {
    border: none;
    padding: 0.375rem 0.5rem;
    transition: all 0.3s ease;
}

.action-buttons .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* تحسين التمرير */
.custom-scrollbar {
    scrollbar-width: thin;
    scrollbar-color: #17a2b8 #f1f1f1;
}

.custom-scrollbar::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

.custom-scrollbar::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb {
    background: #17a2b8;
    border-radius: 4px;
}

.custom-scrollbar::-webkit-scrollbar-thumb:hover {
    background: #138496;
}

/* تحسين الانتقالات */
.smooth-transition {
    transition: all 0.3s ease;
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* تحسين الاستجابة للشاشات الصغيرة */
@media (max-width: 768px) {
    .report-actions {
        flex-direction: column;
    }

    .report-actions .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .filter-section {
        padding: 1rem;
    }

    .stats-card-report h3 {
        font-size: 2rem;
    }

    .table-responsive {
        font-size: 0.8rem;
    }

    .import-instructions {
        padding: 1rem;
    }

    .import-actions {
        flex-direction: column;
    }

    .import-actions .btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .file-upload-area {
        padding: 1.5rem 1rem;
    }

    .filter-controls {
        padding: 1rem;
    }

    .filter-controls .row {
        margin: 0;
    }

    .filter-controls .col-md-3 {
        padding: 0.5rem;
    }

    .stats-badges {
        text-align: center;
    }

    .stats-badges .badge {
        display: block;
        margin: 0.25rem auto;
        width: fit-content;
    }

    .nested-table {
        font-size: 0.75rem;
    }

    .action-buttons .btn-group {
        flex-direction: column;
    }

    .action-buttons .btn {
        margin-bottom: 0.25rem;
        border-radius: 4px !important;
    }
}

/* ألوان إضافية */
.bg-purple {
    background-color: #6f42c1 !important;
    color: white !important;
}

.text-purple {
    color: #6f42c1 !important;
}

.badge.bg-purple {
    background-color: #6f42c1 !important;
}

.btn-purple {
    background-color: #6f42c1;
    border-color: #6f42c1;
    color: white;
}

.btn-purple:hover {
    background-color: #5a359a;
    border-color: #5a359a;
    color: white;
}

/* تحسينات نهائية للتخطيط */
* {
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
}

body {
    overflow-x: hidden;
    font-size: 14px;
    line-height: 1.6;
}

/* تحسين الحاويات */
.container,
.container-fluid,
.container-sm,
.container-md,
.container-lg,
.container-xl {
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
    margin-right: auto;
    margin-left: auto;
}

/* تحسين الشبكة */
.row {
    display: flex;
    flex-wrap: wrap;
    margin-right: -15px;
    margin-left: -15px;
}

.col,
.col-1, .col-2, .col-3, .col-4, .col-5, .col-6,
.col-7, .col-8, .col-9, .col-10, .col-11, .col-12,
.col-auto,
.col-sm, .col-sm-1, .col-sm-2, .col-sm-3, .col-sm-4, .col-sm-5, .col-sm-6,
.col-sm-7, .col-sm-8, .col-sm-9, .col-sm-10, .col-sm-11, .col-sm-12,
.col-sm-auto,
.col-md, .col-md-1, .col-md-2, .col-md-3, .col-md-4, .col-md-5, .col-md-6,
.col-md-7, .col-md-8, .col-md-9, .col-md-10, .col-md-11, .col-md-12,
.col-md-auto,
.col-lg, .col-lg-1, .col-lg-2, .col-lg-3, .col-lg-4, .col-lg-5, .col-lg-6,
.col-lg-7, .col-lg-8, .col-lg-9, .col-lg-10, .col-lg-11, .col-lg-12,
.col-lg-auto,
.col-xl, .col-xl-1, .col-xl-2, .col-xl-3, .col-xl-4, .col-xl-5, .col-xl-6,
.col-xl-7, .col-xl-8, .col-xl-9, .col-xl-10, .col-xl-11, .col-xl-12,
.col-xl-auto {
    position: relative;
    width: 100%;
    padding-right: 15px;
    padding-left: 15px;
}

/* تحسين المساحات */
.p-0 { padding: 0 !important; }
.m-0 { margin: 0 !important; }
.w-100 { width: 100% !important; }
.h-100 { height: 100% !important; }

/* تحسين الفليكس */
.d-flex {
    display: flex !important;
}

.flex-wrap {
    flex-wrap: wrap !important;
}

.justify-content-between {
    justify-content: space-between !important;
}

.align-items-center {
    align-items: center !important;
}

/* تحسين النصوص */
.text-center {
    text-align: center !important;
}

.text-right {
    text-align: right !important;
}

.text-left {
    text-align: left !important;
}

/* تحسين الظلال */
.shadow-sm {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.shadow {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important;
}

.shadow-lg {
    box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important;
}

/* تحسين الحدود */
.border {
    border: 1px solid #dee2e6 !important;
}

.border-0 {
    border: 0 !important;
}

.rounded {
    border-radius: 0.25rem !important;
}

.rounded-lg {
    border-radius: 0.5rem !important;
}

/* تحسين الخلفيات */
.bg-white {
    background-color: #fff !important;
}

.bg-light {
    background-color: #f8f9fa !important;
}

.bg-transparent {
    background-color: transparent !important;
}

/* تنسيقات الوحدات الرئيسية */
.module-card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    cursor: pointer;
    overflow: hidden;
    position: relative;
}

.module-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.module-card:hover::before {
    opacity: 1;
}

.module-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.module-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto;
    color: white;
    position: relative;
    overflow: hidden;
}

.module-icon::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    animation: pulse 3s infinite;
}

.module-icon i {
    position: relative;
    z-index: 2;
}

.module-stats {
    margin-top: 1rem;
}

.module-stats .badge {
    font-size: 0.8rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
}

/* الإجراءات السريعة */
.quick-action-btn {
    height: 120px;
    border-radius: 12px;
    border: 2px solid;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-weight: 600;
    position: relative;
    overflow: hidden;
}

.quick-action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.quick-action-btn:hover::before {
    left: 100%;
}

.quick-action-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.quick-action-btn i {
    transition: transform 0.3s ease;
}

.quick-action-btn:hover i {
    transform: scale(1.1);
}

/* بطاقة الإحصائيات */
.stat-item {
    padding: 1rem;
    border-radius: 8px;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    transition: all 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stat-item h3 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.stat-item small {
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* قائمة النشاط */
.activity-list {
    max-height: 300px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: center;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f1f1f1;
    transition: all 0.3s ease;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-item:hover {
    background: #f8f9fa;
    border-radius: 6px;
    padding-left: 0.5rem;
    padding-right: 0.5rem;
}

.activity-item i {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 1rem;
    font-size: 1.1rem;
}

.activity-item .fas.fa-user-plus {
    background: rgba(13, 110, 253, 0.1);
}

.activity-item .fas.fa-calendar-check {
    background: rgba(25, 135, 84, 0.1);
}

.activity-item .fas.fa-passport {
    background: rgba(111, 66, 193, 0.1);
}

.activity-item .fas.fa-money-bill {
    background: rgba(255, 193, 7, 0.1);
}

/* تحسين الهيدر */
.page-header {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 249, 250, 0.9) 100%);
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
}

.page-header h1 {
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.page-header .lead {
    font-size: 1.1rem;
    margin-bottom: 0;
}

/* تحسين الكارد هيدر */
.card-header.bg-gradient {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%) !important;
    border-radius: 15px 15px 0 0;
}

/* ألوان إضافية للأزرار */
.btn-outline-purple {
    color: #6f42c1;
    border-color: #6f42c1;
}

.btn-outline-purple:hover {
    color: #fff;
    background-color: #6f42c1;
    border-color: #6f42c1;
}

.bg-purple {
    background-color: #6f42c1 !important;
}

/* التصميم العمودي الجديد */
.vertical-modules-container {
    width: 100%;
    max-width: 1200px;
    margin: 2rem auto;
    padding: 2rem;
}

/* العنوان الرئيسي */
.page-title-section {
    text-align: center;
    margin-bottom: 3rem;
    padding: 2rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 20px;
    color: white;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.title-icon {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    margin: 0 auto 1rem;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.title-content h1 {
    font-size: 2.5rem;
    font-weight: 800;
    margin: 0 0 0.5rem 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.title-content p {
    font-size: 1.2rem;
    margin: 0;
    opacity: 0.9;
}

/* الشبكة ثلاثية الأعمدة */
.vertical-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

.vertical-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    border: 2px solid transparent;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    backdrop-filter: blur(15px);
    position: relative;
    overflow: hidden;
    min-height: 280px;
}

.vertical-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.4s ease;
}

.vertical-card:hover::before {
    opacity: 1;
}

.vertical-card:hover {
    transform: translateY(-10px) scale(1.02);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.2);
}

.card-icon {
    width: 80px;
    height: 80px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    margin-bottom: 1.5rem;
    position: relative;
    z-index: 2;
    border: 3px solid rgba(255, 255, 255, 0.2);
}

.card-icon::after {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    border-radius: 23px;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.3), transparent, rgba(255, 255, 255, 0.3));
    z-index: -1;
}

.card-content {
    position: relative;
    z-index: 2;
}

.card-content h3 {
    font-size: 1.4rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0 0 0.8rem 0;
    line-height: 1.3;
}

.card-content p {
    font-size: 0.95rem;
    color: #6c757d;
    margin: 0 0 1.5rem 0;
    line-height: 1.5;
}

.card-stats {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.3rem;
    background: rgba(102, 126, 234, 0.1);
    padding: 1rem 1.5rem;
    border-radius: 15px;
    border: 2px solid rgba(102, 126, 234, 0.2);
}

.stat-number {
    font-size: 2rem;
    font-weight: 800;
    color: #2c3e50;
    font-family: 'Arial', sans-serif;
}

.stat-label {
    font-size: 0.85rem;
    color: #667eea;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* ألوان البطاقات العمودية */
.vertical-card.primary .card-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.vertical-card.success .card-icon {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
}

.vertical-card.info .card-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.vertical-card.warning .card-icon {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.vertical-card.purple .card-icon {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.vertical-card.secondary .card-icon {
    background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
}

.vertical-card.dark .card-icon {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

.vertical-card.danger .card-icon {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}

.vertical-card.indigo .card-icon {
    background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
}

.vertical-card.teal .card-icon {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

.vertical-card.orange .card-icon {
    background: linear-gradient(135deg, #ff8a80 0%, #ffb74d 100%);
}

.vertical-card.gray .card-icon {
    background: linear-gradient(135deg, #bdc3c7 0%, #2c3e50 100%);
}

.main-header-section::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
    animation: headerPulse 6s infinite;
}

.company-logo {
    width: 80px;
    height: 80px;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2.5rem;
    color: white;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    position: relative;
    z-index: 2;
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.company-info {
    flex: 1;
    position: relative;
    z-index: 2;
}

.company-info h2 {
    font-size: 2.5rem;
    font-weight: 800;
    margin: 0 0 0.5rem 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    letter-spacing: 1px;
}

.company-info p {
    font-size: 1.2rem;
    margin: 0;
    opacity: 0.9;
    font-weight: 500;
}

/* شبكة الوحدات */
.sidebar-modules-grid {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.modules-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

/* الوحدات الجانبية */
.sidebar-module {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 1.5rem;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
    border: 2px solid transparent;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(15px);
    min-height: 180px;
    display: flex;
    flex-direction: column;
}

.sidebar-module::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.4s ease;
}

.sidebar-module:hover::before {
    opacity: 1;
}

.sidebar-module:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
    border-color: rgba(102, 126, 234, 0.3);
}

/* هيدر الوحدة */
.module-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    position: relative;
    z-index: 2;
}

.module-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.8rem;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
    border: 3px solid rgba(255, 255, 255, 0.2);
}

.module-icon::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    animation: iconShine 4s infinite;
}

.module-badge {
    background: rgba(255, 255, 255, 0.9);
    color: #2c3e50;
    font-size: 0.9rem;
    font-weight: 800;
    padding: 0.4rem 0.8rem;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 2px solid rgba(102, 126, 234, 0.1);
}

/* محتوى الوحدة */
.module-content {
    flex: 1;
    position: relative;
    z-index: 2;
}

.module-content h4 {
    font-size: 1.3rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0 0 0.5rem 0;
    letter-spacing: 0.5px;
}

.module-content p {
    font-size: 0.9rem;
    color: #6c757d;
    margin: 0 0 1rem 0;
    line-height: 1.5;
}

.module-stats {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.module-stats span {
    display: flex;
    align-items: center;
    gap: 0.3rem;
    font-size: 0.8rem;
    color: #495057;
    background: rgba(102, 126, 234, 0.1);
    padding: 0.3rem 0.6rem;
    border-radius: 8px;
    font-weight: 600;
}

.module-stats span i {
    font-size: 0.7rem;
}

/* ألوان الوحدات */
.sidebar-module.primary .module-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.sidebar-module.success .module-icon {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
}

.sidebar-module.info .module-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.sidebar-module.warning .module-icon {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.sidebar-module.purple .module-icon {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.sidebar-module.secondary .module-icon {
    background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
}

.sidebar-module.dark .module-icon {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

.sidebar-module.danger .module-icon {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}

.sidebar-module.indigo .module-icon {
    background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
}

.sidebar-module.teal .module-icon {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

.sidebar-module.orange .module-icon {
    background: linear-gradient(135deg, #ff8a80 0%, #ffb74d 100%);
}

.sidebar-module.gray .module-icon {
    background: linear-gradient(135deg, #bdc3c7 0%, #2c3e50 100%);
}

/* تأثيرات الهوفر للوحدات */
.sidebar-module.primary:hover {
    border-color: rgba(102, 126, 234, 0.4);
    box-shadow: 0 20px 50px rgba(102, 126, 234, 0.2);
}

.sidebar-module.success:hover {
    border-color: rgba(86, 171, 47, 0.4);
    box-shadow: 0 20px 50px rgba(86, 171, 47, 0.2);
}

.sidebar-module.info:hover {
    border-color: rgba(102, 126, 234, 0.4);
    box-shadow: 0 20px 50px rgba(102, 126, 234, 0.2);
}

.sidebar-module.warning:hover {
    border-color: rgba(240, 147, 251, 0.4);
    box-shadow: 0 20px 50px rgba(240, 147, 251, 0.2);
}

.sidebar-module.purple:hover {
    border-color: rgba(168, 237, 234, 0.4);
    box-shadow: 0 20px 50px rgba(168, 237, 234, 0.2);
}

.sidebar-module.secondary:hover {
    border-color: rgba(210, 153, 194, 0.4);
    box-shadow: 0 20px 50px rgba(210, 153, 194, 0.2);
}

.sidebar-module.dark:hover {
    border-color: rgba(44, 62, 80, 0.4);
    box-shadow: 0 20px 50px rgba(44, 62, 80, 0.2);
}

.sidebar-module.danger:hover {
    border-color: rgba(255, 154, 158, 0.4);
    box-shadow: 0 20px 50px rgba(255, 154, 158, 0.2);
}

.sidebar-module.indigo:hover {
    border-color: rgba(161, 140, 209, 0.4);
    box-shadow: 0 20px 50px rgba(161, 140, 209, 0.2);
}

.sidebar-module.teal:hover {
    border-color: rgba(255, 236, 210, 0.4);
    box-shadow: 0 20px 50px rgba(255, 236, 210, 0.2);
}

.sidebar-module.orange:hover {
    border-color: rgba(255, 138, 128, 0.4);
    box-shadow: 0 20px 50px rgba(255, 138, 128, 0.2);
}

.sidebar-module.gray:hover {
    border-color: rgba(189, 195, 199, 0.4);
    box-shadow: 0 20px 50px rgba(189, 195, 199, 0.2);
}

/* الرسوم المتحركة للمربعات الجانبية */
@keyframes headerPulse {
    0%, 100% {
        transform: scale(1) rotate(0deg);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.1) rotate(180deg);
        opacity: 1;
    }
}

@keyframes moduleSlideIn {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* تطبيق الرسوم المتحركة */
.sidebar-module {
    animation: moduleSlideIn 0.6s ease-out;
}

.modules-row:nth-child(1) .sidebar-module {
    animation-delay: 0.1s;
}

.modules-row:nth-child(2) .sidebar-module {
    animation-delay: 0.2s;
}

.modules-row:nth-child(3) .sidebar-module {
    animation-delay: 0.3s;
}

.modules-row:nth-child(4) .sidebar-module {
    animation-delay: 0.4s;
}

.sidebar-module:nth-child(1) { animation-delay: 0.1s; }
.sidebar-module:nth-child(2) { animation-delay: 0.2s; }
.sidebar-module:nth-child(3) { animation-delay: 0.3s; }

/* تحسينات للاستجابة */
@media (max-width: 1200px) {
    .sidebar-modules-container {
        max-width: 1200px;
        padding: 1.5rem;
    }

    .main-header-section {
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .company-logo {
        width: 70px;
        height: 70px;
        font-size: 2rem;
    }

    .company-info h2 {
        font-size: 2rem;
    }

    .company-info p {
        font-size: 1rem;
    }

    .modules-row {
        grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
        gap: 1.5rem;
    }

    .sidebar-module {
        min-height: 160px;
        padding: 1.2rem;
    }

    .module-icon {
        width: 55px;
        height: 55px;
        font-size: 1.6rem;
    }

    .module-content h4 {
        font-size: 1.2rem;
    }
}

@media (max-width: 992px) {
    .sidebar-modules-container {
        padding: 1rem;
        margin: 1rem auto;
    }

    .main-header-section {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
        padding: 1.2rem;
    }

    .company-info h2 {
        font-size: 1.8rem;
    }

    .modules-row {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
        gap: 1rem;
    }

    .sidebar-module {
        min-height: 140px;
        padding: 1rem;
    }

    .module-icon {
        width: 50px;
        height: 50px;
        font-size: 1.4rem;
    }

    .module-content h4 {
        font-size: 1.1rem;
    }

    .module-content p {
        font-size: 0.85rem;
    }

    .module-stats {
        gap: 0.5rem;
    }

    .module-stats span {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }
}

@media (max-width: 768px) {
    .sidebar-modules-container {
        padding: 0.8rem;
        border-radius: 15px;
    }

    .main-header-section {
        padding: 1rem;
        border-radius: 15px;
    }

    .company-logo {
        width: 60px;
        height: 60px;
        font-size: 1.8rem;
        border-radius: 15px;
    }

    .company-info h2 {
        font-size: 1.5rem;
    }

    .company-info p {
        font-size: 0.9rem;
    }

    .modules-row {
        grid-template-columns: 1fr;
        gap: 0.8rem;
    }

    .sidebar-module {
        min-height: 120px;
        padding: 0.8rem;
        border-radius: 15px;
    }

    .module-header {
        margin-bottom: 0.8rem;
    }

    .module-icon {
        width: 45px;
        height: 45px;
        font-size: 1.2rem;
        border-radius: 12px;
    }

    .module-badge {
        font-size: 0.8rem;
        padding: 0.3rem 0.6rem;
        border-radius: 10px;
    }

    .module-content h4 {
        font-size: 1rem;
    }

    .module-content p {
        font-size: 0.8rem;
        margin-bottom: 0.8rem;
    }

    .module-stats {
        flex-direction: column;
        gap: 0.3rem;
    }

    .module-stats span {
        font-size: 0.7rem;
        padding: 0.2rem 0.4rem;
        border-radius: 6px;
    }
}

@media (max-width: 576px) {
    .sidebar-modules-container {
        margin: 0.5rem auto;
        padding: 0.6rem;
    }

    .main-header-section {
        padding: 0.8rem;
        margin-bottom: 1rem;
    }

    .company-logo {
        width: 50px;
        height: 50px;
        font-size: 1.5rem;
    }

    .company-info h2 {
        font-size: 1.3rem;
    }

    .company-info p {
        font-size: 0.8rem;
    }

    .sidebar-module {
        min-height: 100px;
        padding: 0.6rem;
    }

    .module-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
        border-radius: 10px;
    }

    .module-badge {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
    }

    .module-content h4 {
        font-size: 0.9rem;
    }

    .module-content p {
        font-size: 0.75rem;
        margin-bottom: 0.6rem;
    }

    .module-stats span {
        font-size: 0.65rem;
        padding: 0.15rem 0.3rem;
    }
}

/* التدرجات اللونية */
.bg-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.bg-gradient-success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
}

.bg-gradient-info {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.bg-gradient-warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.bg-gradient-purple {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.bg-gradient-secondary {
    background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
}

.bg-gradient-dark {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

.bg-gradient-danger {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}

.bg-gradient-indigo {
    background: linear-gradient(135deg, #a18cd1 0%, #fbc2eb 100%);
}

.bg-gradient-teal {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

.bg-gradient-orange {
    background: linear-gradient(135deg, #ff8a80 0%, #ffb74d 100%);
}

.bg-gradient-gray {
    background: linear-gradient(135deg, #bdc3c7 0%, #2c3e50 100%);
}

/* تحديد مواقع الوحدات الدائرية المحسنة */
.circular-module[data-angle="0"] {
    top: 50%;
    right: 30px;
    transform: translateY(-50%);
    animation-delay: 0s;
}

.circular-module[data-angle="30"] {
    top: 20%;
    right: 12%;
    animation-delay: 0.3s;
}

.circular-module[data-angle="60"] {
    top: 5%;
    right: 30%;
    animation-delay: 0.6s;
}

.circular-module[data-angle="90"] {
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    animation-delay: 0.9s;
}

.circular-module[data-angle="120"] {
    top: 5%;
    left: 30%;
    animation-delay: 1.2s;
}

.circular-module[data-angle="150"] {
    top: 20%;
    left: 12%;
    animation-delay: 1.5s;
}

.circular-module[data-angle="180"] {
    top: 50%;
    left: 30px;
    transform: translateY(-50%);
    animation-delay: 1.8s;
}

.circular-module[data-angle="210"] {
    bottom: 20%;
    left: 12%;
    animation-delay: 2.1s;
}

.circular-module[data-angle="240"] {
    bottom: 5%;
    left: 30%;
    animation-delay: 2.4s;
}

.circular-module[data-angle="270"] {
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    animation-delay: 2.7s;
}

.circular-module[data-angle="300"] {
    bottom: 5%;
    right: 30%;
    animation-delay: 3s;
}

.circular-module[data-angle="330"] {
    bottom: 20%;
    right: 12%;
    animation-delay: 3.3s;
}

/* الرسوم المتحركة */
@keyframes centralPulse {
    0%, 100% {
        transform: translate(-50%, -50%) scale(1);
        box-shadow: 0 15px 40px rgba(102, 126, 234, 0.4);
    }
    50% {
        transform: translate(-50%, -50%) scale(1.05);
        box-shadow: 0 20px 50px rgba(102, 126, 234, 0.6);
    }
}

@keyframes iconRotate {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes moduleFloat {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

@keyframes moduleSlideIn {
    from {
        opacity: 0;
        transform: scale(0.5);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* تطبيق الرسوم المتحركة على الوحدات */
.circular-module {
    animation: moduleSlideIn 0.8s ease-out, moduleFloat 6s ease-in-out infinite;
}

/* تحسينات للاستجابة المحسنة */
@media (max-width: 1200px) {
    .circular-modules-container {
        height: 600px;
        max-width: 800px;
    }

    .central-circle {
        width: 190px;
        height: 190px;
    }

    .central-icon {
        font-size: 3.5rem;
    }

    .central-text h3 {
        font-size: 1.6rem;
    }

    .circular-module {
        width: 120px;
        height: 120px;
    }

    .circular-icon {
        width: 65px;
        height: 65px;
        font-size: 1.8rem;
    }

    .module-name {
        font-size: 0.8rem;
    }

    .module-count {
        font-size: 0.7rem;
        padding: 0.25rem 0.7rem;
    }

    .circular-tool {
        min-width: 280px;
        padding: 1rem 1.8rem;
    }

    .circular-tool-icon {
        width: 55px;
        height: 55px;
        font-size: 1.4rem;
    }
}

@media (max-width: 992px) {
    .circular-modules-container {
        height: 450px;
    }

    .central-circle {
        width: 130px;
        height: 130px;
    }

    .central-icon {
        font-size: 2rem;
    }

    .central-text h3 {
        font-size: 1.1rem;
    }

    .central-text p {
        font-size: 0.9rem;
    }

    .circular-module {
        width: 90px;
        height: 90px;
    }

    .circular-icon {
        width: 45px;
        height: 45px;
        font-size: 1.2rem;
    }
}

@media (max-width: 768px) {
    .circular-modules-container {
        height: 400px;
        margin: 1rem auto;
    }

    .central-circle {
        width: 110px;
        height: 110px;
    }

    .central-icon {
        font-size: 1.8rem;
        margin-bottom: 0.3rem;
    }

    .central-text h3 {
        font-size: 1rem;
    }

    .central-text p {
        font-size: 0.8rem;
    }

    .circular-module {
        width: 80px;
        height: 80px;
    }

    .circular-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
        margin-bottom: 0.3rem;
    }

    .module-name {
        font-size: 0.65rem;
        line-height: 1.1;
    }

    .module-count {
        font-size: 0.6rem;
        padding: 0.1rem 0.3rem;
    }
}

@media (max-width: 576px) {
    .circular-modules-container {
        height: 350px;
    }

    .central-circle {
        width: 90px;
        height: 90px;
    }

    .central-icon {
        font-size: 1.5rem;
    }

    .central-text h3 {
        font-size: 0.9rem;
    }

    .central-text p {
        font-size: 0.7rem;
    }

    .circular-module {
        width: 70px;
        height: 70px;
    }

    .circular-icon {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
        margin-bottom: 0.2rem;
    }

    .module-name {
        font-size: 0.6rem;
    }

    .module-count {
        font-size: 0.55rem;
    }
}

.modules-grid {
    display: flex;
    flex-wrap: wrap;
    gap: 1.5rem;
    justify-content: center;
    align-items: flex-start;
}

.module-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1.5rem 1rem;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 16px;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    min-width: 140px;
    max-width: 160px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.08);
}

.module-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.module-item:hover::before {
    opacity: 1;
}

.module-item:hover {
    transform: translateY(-8px) scale(1.05);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
    border-color: rgba(102, 126, 234, 0.3);
}

.module-icon-circle {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 1rem;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.module-icon-circle::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    animation: pulse 3s infinite;
}

.module-icon-circle i {
    font-size: 1.8rem;
    color: white;
    position: relative;
    z-index: 2;
    transition: transform 0.3s ease;
}

.module-item:hover .module-icon-circle i {
    transform: scale(1.2) rotate(5deg);
}

.module-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: #2c3e50;
    text-align: center;
    margin-bottom: 0.5rem;
    line-height: 1.3;
}

.module-badge {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    font-size: 0.75rem;
    font-weight: 600;
    padding: 0.3rem 0.8rem;
    border-radius: 20px;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
    transition: all 0.3s ease;
}

.module-item:hover .module-badge {
    transform: scale(1.1);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
}

/* ألوان إضافية للوحدات */
.bg-indigo {
    background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%) !important;
}

.bg-teal {
    background: linear-gradient(135deg, #14b8a6 0%, #06b6d4 100%) !important;
}

.bg-orange {
    background: linear-gradient(135deg, #f97316 0%, #ea580c 100%) !important;
}

.bg-gray {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%) !important;
}

/* تحسينات للاستجابة */
@media (max-width: 1200px) {
    .modules-grid {
        gap: 1.2rem;
    }

    .module-item {
        min-width: 130px;
        max-width: 150px;
        padding: 1.2rem 0.8rem;
    }

    .module-icon-circle {
        width: 60px;
        height: 60px;
    }

    .module-icon-circle i {
        font-size: 1.6rem;
    }
}

@media (max-width: 992px) {
    .modules-grid {
        gap: 1rem;
    }

    .module-item {
        min-width: 120px;
        max-width: 140px;
        padding: 1rem 0.6rem;
    }

    .module-icon-circle {
        width: 55px;
        height: 55px;
    }

    .module-icon-circle i {
        font-size: 1.4rem;
    }

    .module-title {
        font-size: 0.8rem;
    }
}

@media (max-width: 768px) {
    .modules-container {
        padding: 1.5rem;
    }

    .modules-grid {
        gap: 0.8rem;
        justify-content: space-around;
    }

    .module-item {
        min-width: 100px;
        max-width: 120px;
        padding: 0.8rem 0.4rem;
    }

    .module-icon-circle {
        width: 50px;
        height: 50px;
        margin-bottom: 0.8rem;
    }

    .module-icon-circle i {
        font-size: 1.2rem;
    }

    .module-title {
        font-size: 0.75rem;
        margin-bottom: 0.4rem;
    }

    .module-badge {
        font-size: 0.7rem;
        padding: 0.2rem 0.6rem;
    }
}

@media (max-width: 576px) {
    .modules-grid {
        gap: 0.6rem;
    }

    .module-item {
        min-width: 90px;
        max-width: 110px;
        padding: 0.6rem 0.3rem;
    }

    .module-icon-circle {
        width: 45px;
        height: 45px;
        margin-bottom: 0.6rem;
    }

    .module-icon-circle i {
        font-size: 1rem;
    }

    .module-title {
        font-size: 0.7rem;
        line-height: 1.2;
    }

    .module-badge {
        font-size: 0.65rem;
        padding: 0.15rem 0.5rem;
    }
}

/* تأثيرات إضافية للوحدات */
.module-item {
    animation: moduleSlideIn 0.6s ease-out;
}

.module-item:nth-child(1) { animation-delay: 0.1s; }
.module-item:nth-child(2) { animation-delay: 0.2s; }
.module-item:nth-child(3) { animation-delay: 0.3s; }
.module-item:nth-child(4) { animation-delay: 0.4s; }
.module-item:nth-child(5) { animation-delay: 0.5s; }
.module-item:nth-child(6) { animation-delay: 0.6s; }
.module-item:nth-child(7) { animation-delay: 0.7s; }
.module-item:nth-child(8) { animation-delay: 0.8s; }
.module-item:nth-child(9) { animation-delay: 0.9s; }
.module-item:nth-child(10) { animation-delay: 1.0s; }
.module-item:nth-child(11) { animation-delay: 1.1s; }
.module-item:nth-child(12) { animation-delay: 1.2s; }

@keyframes moduleSlideIn {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.8);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

/* تحسين الخلفية العامة */
.modules-container::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(102, 126, 234, 0.05) 0%, transparent 70%);
    animation: backgroundPulse 8s infinite;
    pointer-events: none;
}

@keyframes backgroundPulse {
    0%, 100% {
        transform: scale(1) rotate(0deg);
        opacity: 0.3;
    }
    50% {
        transform: scale(1.1) rotate(180deg);
        opacity: 0.6;
    }
}

/* تحسين التفاعل */
.module-item:active {
    transform: translateY(-5px) scale(1.02);
    transition: transform 0.1s ease;
}

.module-item:focus {
    outline: 3px solid rgba(102, 126, 234, 0.5);
    outline-offset: 2px;
}

/* تحسين الألوان المتدرجة */
.module-icon-circle.bg-primary {
    background: linear-gradient(135deg, #0d6efd 0%, #6610f2 100%);
}

.module-icon-circle.bg-success {
    background: linear-gradient(135deg, #198754 0%, #20c997 100%);
}

.module-icon-circle.bg-info {
    background: linear-gradient(135deg, #0dcaf0 0%, #6f42c1 100%);
}

.module-icon-circle.bg-warning {
    background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
}

.module-icon-circle.bg-danger {
    background: linear-gradient(135deg, #dc3545 0%, #e83e8c 100%);
}

.module-icon-circle.bg-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
}

.module-icon-circle.bg-dark {
    background: linear-gradient(135deg, #212529 0%, #343a40 100%);
}

/* تحسين الظلال حسب اللون */
.module-item:hover .module-icon-circle.bg-primary {
    box-shadow: 0 8px 25px rgba(13, 110, 253, 0.4);
}

.module-item:hover .module-icon-circle.bg-success {
    box-shadow: 0 8px 25px rgba(25, 135, 84, 0.4);
}

.module-item:hover .module-icon-circle.bg-info {
    box-shadow: 0 8px 25px rgba(13, 202, 240, 0.4);
}

.module-item:hover .module-icon-circle.bg-warning {
    box-shadow: 0 8px 25px rgba(255, 193, 7, 0.4);
}

.module-item:hover .module-icon-circle.bg-danger {
    box-shadow: 0 8px 25px rgba(220, 53, 69, 0.4);
}

.module-item:hover .module-icon-circle.bg-purple {
    box-shadow: 0 8px 25px rgba(111, 66, 193, 0.4);
}

.module-item:hover .module-icon-circle.bg-indigo {
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.4);
}

.module-item:hover .module-icon-circle.bg-teal {
    box-shadow: 0 8px 25px rgba(20, 184, 166, 0.4);
}

.module-item:hover .module-icon-circle.bg-orange {
    box-shadow: 0 8px 25px rgba(249, 115, 22, 0.4);
}

.module-item:hover .module-icon-circle.bg-gray {
    box-shadow: 0 8px 25px rgba(107, 114, 128, 0.4);
}

/* تصميم النوافذ العمودي */
.page-header-section {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 2rem;
}

.page-title {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.title-icon {
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.title-icon.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.title-text h2 {
    font-size: 1.8rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0 0 0.3rem 0;
}

.title-text p {
    font-size: 0.9rem;
    color: #6c757d;
    margin: 0;
}

.page-actions {
    display: flex;
    gap: 1rem;
}

.action-button {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.8rem 1.5rem;
    border: none;
    border-radius: 10px;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.action-button.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.action-button.secondary {
    background: #f8f9fa;
    color: #495057;
    border: 1px solid #dee2e6;
}

.action-button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.modern-window-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(circle at 20% 80%, rgba(102, 126, 234, 0.08) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(118, 75, 162, 0.08) 0%, transparent 50%);
    pointer-events: none;
}

.header-content {
    display: flex;
    align-items: center;
    gap: 2rem;
    position: relative;
    z-index: 2;
}

.header-icon {
    width: 80px;
    height: 80px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2.2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
    border: 3px solid rgba(255, 255, 255, 0.2);
    flex-shrink: 0;
}

.header-icon::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    animation: iconPulse 4s infinite;
}

.header-icon i {
    position: relative;
    z-index: 2;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.header-info {
    flex: 1;
}

.header-info h2 {
    font-size: 2.5rem;
    font-weight: 800;
    color: #2c3e50;
    margin: 0 0 0.5rem 0;
    background: linear-gradient(135deg, #2c3e50 0%, #667eea 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: 1px;
}

.header-info p {
    font-size: 1.1rem;
    color: #6c757d;
    margin: 0;
    font-weight: 500;
}

.header-actions {
    display: flex;
    gap: 1rem;
    flex-shrink: 0;
}

/* الأزرار الحديثة */
.modern-btn {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    padding: 1.2rem 2rem;
    border: none;
    border-radius: 15px;
    font-weight: 700;
    font-size: 0.95rem;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    min-width: 160px;
    justify-content: center;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    backdrop-filter: blur(10px);
}

.modern-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.6s;
}

.modern-btn:hover::before {
    left: 100%;
}

.modern-btn:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.2);
}

.modern-btn:active {
    transform: translateY(-1px) scale(0.98);
}

.modern-btn.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.modern-btn.success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.modern-btn.info {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.modern-btn.warning {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.modern-btn.danger {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.modern-btn.secondary {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #495057;
    border: 2px solid #dee2e6;
}

/* الأزرار المحددة */
.modern-btn.outline-success {
    background: rgba(86, 171, 47, 0.1);
    color: #56ab2f;
    border: 2px solid #56ab2f;
}

.modern-btn.outline-info {
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
    border: 2px solid #667eea;
}

.modern-btn.outline-success:hover {
    background: #56ab2f;
    color: white;
}

.modern-btn.outline-info:hover {
    background: #667eea;
    color: white;
}

/* قسم الفلاتر */
.filters-section {
    background: #ffffff;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
}

.filters-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
    align-items: end;
    max-width: 1000px;
    margin: 0 auto;
}

.filter-item {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-item label {
    font-size: 0.85rem;
    font-weight: 600;
    color: #495057;
}

.input-group {
    position: relative;
    display: flex;
    align-items: center;
}

.input-group i {
    position: absolute;
    right: 12px;
    color: #adb5bd;
    z-index: 2;
}

.input-group input,
.input-group select {
    width: 100%;
    padding: 0.8rem 2.5rem 0.8rem 1rem;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    background: #ffffff;
}

.input-group input:focus,
.input-group select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filter-actions {
    display: flex;
    gap: 0.5rem;
    grid-column: 1 / -1;
    justify-content: center;
    margin-top: 1rem;
}

.filter-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.8rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.filter-btn.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.filter-btn.secondary {
    background: #f8f9fa;
    color: #495057;
    border: 1px solid #dee2e6;
}

.filter-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.modern-toolbar::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(102, 126, 234, 0.05) 0%, transparent 70%);
    animation: toolbarPulse 8s infinite;
}

.toolbar-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    align-items: end;
    position: relative;
    z-index: 2;
}

.tool-card {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
    border: 2px solid transparent;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    gap: 1rem;
}

.tool-card:hover {
    transform: translateY(-5px);
    border-color: rgba(102, 126, 234, 0.3);
    box-shadow: 0 15px 40px rgba(102, 126, 234, 0.2);
}

.tool-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.3rem;
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.3);
    flex-shrink: 0;
    border: 2px solid rgba(255, 255, 255, 0.2);
}

.tool-content {
    flex: 1;
}

.tool-content label {
    display: block;
    font-size: 0.85rem;
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.modern-input,
.modern-select {
    width: 100%;
    border: none;
    outline: none;
    background: transparent;
    font-size: 1rem;
    font-weight: 600;
    color: #2c3e50;
    padding: 0.5rem 0;
    border-bottom: 2px solid rgba(102, 126, 234, 0.2);
    transition: all 0.3s ease;
}

.modern-input:focus,
.modern-select:focus {
    border-bottom-color: #667eea;
    transform: translateY(-2px);
}

.modern-input::placeholder {
    color: #adb5bd;
    font-style: italic;
    font-weight: 500;
}

.tool-actions {
    grid-column: 1 / -1;
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 1rem;
}

.circular-tool {
    display: flex;
    align-items: center;
    gap: 1.5rem;
    background: rgba(255, 255, 255, 0.95);
    border-radius: 60px;
    padding: 1.2rem 2rem;
    box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
    border: 3px solid transparent;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    min-width: 300px;
    backdrop-filter: blur(15px);
}

.circular-tool:hover {
    transform: translateY(-8px) scale(1.02);
    border-color: rgba(102, 126, 234, 0.4);
    box-shadow: 0 15px 45px rgba(102, 126, 234, 0.25);
}

.circular-tool-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.5rem;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
    flex-shrink: 0;
    border: 3px solid rgba(255, 255, 255, 0.3);
    position: relative;
    overflow: hidden;
}

.circular-tool-icon::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    animation: iconShine 3s infinite;
}

.circular-tool-content {
    flex: 1;
}

.circular-input,
.circular-select {
    width: 100%;
    border: none;
    outline: none;
    background: transparent;
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    padding: 0.8rem 0;
    border-bottom: 2px solid rgba(102, 126, 234, 0.2);
    transition: all 0.3s ease;
}

.circular-input:focus,
.circular-select:focus {
    border-bottom-color: #667eea;
    transform: translateY(-2px);
}

.circular-input::placeholder {
    color: #adb5bd;
    font-style: italic;
    font-weight: 500;
}

.circular-actions {
    display: flex;
    gap: 1.5rem;
}

.circular-action-btn {
    display: flex;
    align-items: center;
    gap: 0.8rem;
    padding: 1.2rem 2.5rem;
    border: none;
    border-radius: 60px;
    font-weight: 700;
    font-size: 1rem;
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;
    min-width: 180px;
    justify-content: center;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.circular-action-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.6s;
}

.circular-action-btn:hover::before {
    left: 100%;
}

.circular-action-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.circular-action-btn.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.circular-action-btn.secondary {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #495057;
    border: 2px solid #dee2e6;
}

/* قسم الإحصائيات */
.stats-section {
    margin: 2rem 0;
}

.stats-row {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 1.5rem;
    max-width: 1000px;
    margin: 0 auto;
}

.stat-box {
    background: #ffffff;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: all 0.3s ease;
}

.stat-box:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-box .stat-icon {
    width: 50px;
    height: 50px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.stat-box.primary .stat-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-box.success .stat-icon {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
}

.stat-box.warning .stat-icon {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-box.info .stat-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-box.danger .stat-icon {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}

.stat-box.teal .stat-icon {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

.stat-info {
    display: flex;
    flex-direction: column;
}

.stat-number {
    font-size: 1.8rem;
    font-weight: 800;
    color: #2c3e50;
    margin-bottom: 0.2rem;
}

.stat-label {
    font-size: 0.85rem;
    color: #6c757d;
    font-weight: 600;
}

.stat-card {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    border: 2px solid transparent;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    backdrop-filter: blur(15px);
    display: flex;
    align-items: center;
    gap: 1.5rem;
}

.stat-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.5) 0%, rgba(255, 255, 255, 0.1) 100%);
    opacity: 0;
    transition: opacity 0.4s ease;
}

.stat-card:hover::before {
    opacity: 1;
}

.stat-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
}

.stat-icon {
    width: 70px;
    height: 70px;
    border-radius: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.8rem;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    position: relative;
    overflow: hidden;
    border: 3px solid rgba(255, 255, 255, 0.2);
    flex-shrink: 0;
}

.stat-icon::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3) 0%, transparent 70%);
    animation: iconShine 4s infinite;
}

.stat-content {
    flex: 1;
}

.stat-content h3 {
    font-size: 2.2rem;
    font-weight: 800;
    color: #2c3e50;
    margin: 0 0 0.5rem 0;
    font-family: 'Arial', sans-serif;
    font-variant-numeric: tabular-nums;
}

.stat-content p {
    font-size: 1rem;
    color: #6c757d;
    margin: 0 0 0.8rem 0;
    font-weight: 600;
}

.stat-trend {
    display: inline-flex;
    align-items: center;
    gap: 0.3rem;
    font-size: 0.85rem;
    font-weight: 700;
    padding: 0.3rem 0.8rem;
    border-radius: 12px;
}

.stat-trend.up {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

.stat-trend.down {
    background: rgba(220, 53, 69, 0.1);
    color: #dc3545;
}

.stat-trend.neutral {
    background: rgba(108, 117, 125, 0.1);
    color: #6c757d;
}

/* ألوان بطاقات الإحصائيات */
.stat-card.primary .stat-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-card.success .stat-icon {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
}

.stat-card.info .stat-icon {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-card.warning .stat-icon {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-card.danger .stat-icon {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}

.stat-card.teal .stat-icon {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
}

/* تأثيرات الهوفر للبطاقات */
.stat-card.primary:hover {
    border-color: rgba(102, 126, 234, 0.4);
    box-shadow: 0 20px 50px rgba(102, 126, 234, 0.2);
}

.stat-card.success:hover {
    border-color: rgba(86, 171, 47, 0.4);
    box-shadow: 0 20px 50px rgba(86, 171, 47, 0.2);
}

.stat-card.info:hover {
    border-color: rgba(102, 126, 234, 0.4);
    box-shadow: 0 20px 50px rgba(102, 126, 234, 0.2);
}

.stat-card.warning:hover {
    border-color: rgba(240, 147, 251, 0.4);
    box-shadow: 0 20px 50px rgba(240, 147, 251, 0.2);
}

.stat-card.danger:hover {
    border-color: rgba(255, 154, 158, 0.4);
    box-shadow: 0 20px 50px rgba(255, 154, 158, 0.2);
}

.stat-card.teal:hover {
    border-color: rgba(255, 236, 210, 0.4);
    box-shadow: 0 20px 50px rgba(255, 236, 210, 0.2);
}

.circular-stat-circle {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: white;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25);
    margin-bottom: 0.8rem;
    position: relative;
    overflow: hidden;
    border: 3px solid rgba(255, 255, 255, 0.2);
}

.circular-stat-circle::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.4) 0%, transparent 70%);
    animation: statPulse 4s infinite;
}

.circular-stat-circle::after {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    border-radius: 50%;
    background: linear-gradient(45deg, rgba(255, 255, 255, 0.3), transparent, rgba(255, 255, 255, 0.3));
    z-index: -1;
}

.circular-stat-circle i {
    font-size: 1.5rem;
    margin-bottom: 0.3rem;
    position: relative;
    z-index: 2;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.circular-stat-number {
    font-size: 1rem;
    font-weight: 800;
    position: relative;
    z-index: 2;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.circular-stat-label {
    text-align: center;
    font-size: 0.8rem;
    font-weight: 700;
    color: #2c3e50;
    background: rgba(255, 255, 255, 0.95);
    padding: 0.4rem 1rem;
    border-radius: 25px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
    white-space: nowrap;
    border: 2px solid rgba(102, 126, 234, 0.1);
    backdrop-filter: blur(10px);
}

/* مواقع الإحصائيات الدائرية */
.circular-stat-item[data-stat="total"] {
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    animation-delay: 0s;
}

.circular-stat-item[data-stat="pending"] {
    top: 30%;
    right: 20px;
    animation-delay: 0.5s;
}

.circular-stat-item[data-stat="completed"] {
    bottom: 30%;
    right: 20px;
    animation-delay: 1s;
}

.circular-stat-item[data-stat="new"] {
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    animation-delay: 1.5s;
}

.circular-stat-item[data-stat="urgent"] {
    bottom: 30%;
    left: 20px;
    animation-delay: 2s;
}

.circular-stat-item[data-stat="delivered"] {
    top: 30%;
    left: 20px;
    animation-delay: 2.5s;
}

/* الرسوم المتحركة للإحصائيات */
@keyframes toolbarPulse {
    0%, 100% {
        transform: scale(1) rotate(0deg);
        opacity: 0.3;
    }
    50% {
        transform: scale(1.1) rotate(180deg);
        opacity: 0.6;
    }
}

@keyframes statFloat {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-8px);
    }
}

@keyframes statPulse {
    0%, 100% {
        transform: scale(1) rotate(0deg);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.3) rotate(180deg);
        opacity: 1;
    }
}

@keyframes iconShine {
    0%, 100% {
        transform: scale(1) rotate(0deg);
        opacity: 0.6;
    }
    50% {
        transform: scale(1.1) rotate(180deg);
        opacity: 1;
    }
}

@keyframes centerPulse {
    0%, 100% {
        transform: translate(-50%, -50%) scale(1);
        box-shadow: 0 15px 45px rgba(102, 126, 234, 0.5);
    }
    50% {
        transform: translate(-50%, -50%) scale(1.05);
        box-shadow: 0 20px 60px rgba(102, 126, 234, 0.7);
    }
}

/* تحسينات إضافية للتفاعل */
.circular-module:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
}

.circular-tool:active {
    transform: translateY(-5px) scale(0.98);
}

.circular-action-btn:active {
    transform: scale(0.95);
}

.circular-stat-item:active {
    transform: scale(0.95);
}

/* تأثيرات الإضاءة المتقدمة */
.circular-module-inner:hover {
    background: rgba(255, 255, 255, 1);
    box-shadow:
        0 20px 50px rgba(102, 126, 234, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.6);
}

.circular-tool:hover {
    background: rgba(255, 255, 255, 1);
    box-shadow:
        0 20px 60px rgba(102, 126, 234, 0.25),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.circular-stat-item:hover .circular-stat-circle {
    box-shadow:
        0 15px 40px rgba(0, 0, 0, 0.3),
        0 0 30px rgba(255, 255, 255, 0.5);
}

/* تحسين الخطوط والنصوص */
.module-name,
.circular-stat-label,
.central-text h3,
.circular-stats-center h3 {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    letter-spacing: 0.5px;
}

.module-count,
.circular-stat-number {
    font-family: 'Arial', sans-serif;
    font-variant-numeric: tabular-nums;
}

/* منطقة المحتوى الدائرية */
.circular-content-area {
    background: radial-gradient(circle at center, rgba(255, 255, 255, 0.95) 0%, rgba(248, 249, 250, 0.9) 100%);
    border-radius: 30px;
    padding: 2rem;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    border: 2px solid rgba(102, 126, 234, 0.1);
    position: relative;
    overflow: hidden;
}

.circular-content-area::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(102, 126, 234, 0.03) 0%, transparent 70%);
    animation: contentPulse 10s infinite;
    pointer-events: none;
}

.circular-data-container {
    position: relative;
    z-index: 2;
}

@keyframes contentPulse {
    0%, 100% {
        transform: scale(1) rotate(0deg);
        opacity: 0.5;
    }
    50% {
        transform: scale(1.05) rotate(180deg);
        opacity: 0.8;
    }
}

/* تحسينات للاستجابة - شبكة ثلاثية */
@media (max-width: 1200px) {
    .vertical-modules-container {
        max-width: 1000px;
        padding: 1.5rem;
    }

    .vertical-grid {
        max-width: 1000px;
        gap: 1.5rem;
    }

    .vertical-card {
        padding: 1.5rem;
        min-height: 250px;
    }

    .card-icon {
        width: 70px;
        height: 70px;
        font-size: 1.8rem;
        margin-bottom: 1.2rem;
    }

    .card-content h3 {
        font-size: 1.2rem;
    }

    .stat-number {
        font-size: 1.8rem;
    }

    .page-title-section {
        padding: 1.5rem;
    }

    .title-icon {
        width: 70px;
        height: 70px;
        font-size: 2.2rem;
    }

    .title-content h1 {
        font-size: 2.2rem;
    }

    .filters-grid {
        max-width: 900px;
        gap: 1.2rem;
    }

    .stats-row {
        max-width: 900px;
        gap: 1.2rem;
    }

    .stat-box {
        padding: 1.2rem;
    }

    .stat-box .stat-icon {
        width: 45px;
        height: 45px;
        font-size: 1.1rem;
    }
}

@media (max-width: 992px) {
    .vertical-modules-container {
        padding: 1rem;
        margin: 1rem auto;
    }

    .vertical-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 1.2rem;
        max-width: 700px;
    }

    .vertical-card {
        padding: 1.2rem;
        min-height: 220px;
    }

    .card-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
        margin-bottom: 1rem;
    }

    .card-content h3 {
        font-size: 1.1rem;
    }

    .card-content p {
        font-size: 0.85rem;
    }

    .stat-number {
        font-size: 1.6rem;
    }

    .page-title-section {
        padding: 1.2rem;
        margin-bottom: 2rem;
    }

    .title-content h1 {
        font-size: 2rem;
    }

    .title-content p {
        font-size: 1rem;
    }

    .page-header-section {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .page-actions {
        justify-content: center;
    }

    .filters-grid {
        grid-template-columns: 1fr;
        gap: 0.8rem;
        max-width: 500px;
    }

    .filter-actions {
        flex-direction: column;
        gap: 0.5rem;
    }

    .stats-row {
        grid-template-columns: repeat(2, 1fr);
        gap: 1rem;
        max-width: 600px;
    }

    .stat-box {
        padding: 1rem;
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }

    .table-header {
        flex-direction: column;
        gap: 1rem;
        text-align: center;
    }

    .table-controls {
        justify-content: center;
    }
}

@media (max-width: 768px) {
    .vertical-modules-container {
        padding: 0.8rem;
    }

    .vertical-grid {
        grid-template-columns: 1fr;
        gap: 1rem;
        max-width: 400px;
    }

    .vertical-card {
        padding: 1rem;
        border-radius: 15px;
        min-height: 200px;
    }

    .card-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
        border-radius: 15px;
        margin-bottom: 1rem;
    }

    .card-content h3 {
        font-size: 1.1rem;
    }

    .card-content p {
        font-size: 0.85rem;
        margin-bottom: 1rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .page-title-section {
        padding: 1rem;
        border-radius: 15px;
    }

    .title-icon {
        width: 60px;
        height: 60px;
        font-size: 2rem;
        border-radius: 15px;
    }

    .title-content h1 {
        font-size: 1.8rem;
    }

    .page-header-section {
        padding: 1rem;
        border-radius: 12px;
    }

    .title-icon {
        width: 50px;
        height: 50px;
        font-size: 1.3rem;
        border-radius: 12px;
    }

    .title-text h2 {
        font-size: 1.4rem;
    }

    .title-text p {
        font-size: 0.8rem;
    }

    .action-button {
        padding: 0.6rem 1rem;
        font-size: 0.8rem;
    }

    .filters-section {
        padding: 1rem;
        border-radius: 12px;
    }

    .filters-grid {
        max-width: 350px;
    }

    .stats-row {
        grid-template-columns: 1fr;
        gap: 0.8rem;
        max-width: 350px;
    }

    .stat-box {
        padding: 1rem;
        border-radius: 12px;
    }

    .table-section {
        padding: 1rem;
        border-radius: 12px;
    }

    .data-table thead th {
        padding: 0.8rem 0.5rem;
        font-size: 0.8rem;
    }

    .data-table tbody td {
        padding: 0.8rem 0.5rem;
        font-size: 0.8rem;
    }
}

@media (max-width: 576px) {
    .vertical-modules-container {
        margin: 0.5rem auto;
        padding: 0.6rem;
    }

    .vertical-grid {
        max-width: 300px;
        gap: 0.8rem;
    }

    .vertical-card {
        padding: 0.8rem;
        min-height: 180px;
        border-radius: 12px;
    }

    .card-icon {
        width: 50px;
        height: 50px;
        font-size: 1.3rem;
        border-radius: 12px;
        margin-bottom: 0.8rem;
    }

    .card-content h3 {
        font-size: 1rem;
    }

    .card-content p {
        font-size: 0.8rem;
        margin-bottom: 0.8rem;
    }

    .card-stats {
        padding: 0.8rem 1rem;
    }

    .stat-number {
        font-size: 1.4rem;
    }

    .stat-label {
        font-size: 0.75rem;
    }

    .page-title-section {
        padding: 0.8rem;
        margin-bottom: 1rem;
    }

    .title-icon {
        width: 50px;
        height: 50px;
        font-size: 1.8rem;
    }

    .title-content h1 {
        font-size: 1.5rem;
    }

    .title-content p {
        font-size: 0.9rem;
    }

    .page-header-section {
        padding: 0.8rem;
    }

    .title-text h2 {
        font-size: 1.2rem;
    }

    .action-button {
        padding: 0.5rem 0.8rem;
        font-size: 0.75rem;
    }

    .filters-grid {
        max-width: 280px;
    }

    .stats-row {
        max-width: 280px;
        gap: 0.6rem;
    }

    .stat-box {
        padding: 0.8rem;
    }

    .data-table thead th {
        padding: 0.6rem 0.3rem;
        font-size: 0.75rem;
    }

    .data-table tbody td {
        padding: 0.6rem 0.3rem;
        font-size: 0.75rem;
    }
}

@media (max-width: 992px) {
    .circular-toolbar {
        padding: 1.5rem;
    }

    .circular-tools-container {
        flex-direction: column;
        gap: 1rem;
    }

    .circular-tool {
        min-width: auto;
        width: 100%;
        max-width: 400px;
    }

    .circular-actions {
        width: 100%;
        justify-content: center;
    }

    .circular-action-btn {
        flex: 1;
        max-width: 180px;
    }

    .circular-stats-container {
        height: 350px;
    }

    .circular-stats-center {
        width: 120px;
        height: 120px;
    }

    .circular-stats-center h3 {
        font-size: 1rem;
    }

    .circular-stats-center p {
        font-size: 0.75rem;
    }

    .circular-stat-circle {
        width: 70px;
        height: 70px;
    }

    .circular-stat-circle i {
        font-size: 1rem;
    }

    .circular-stat-number {
        font-size: 0.8rem;
    }

    .circular-stat-label {
        font-size: 0.7rem;
        padding: 0.2rem 0.6rem;
    }
}

@media (max-width: 768px) {
    .circular-toolbar {
        padding: 1rem;
        border-radius: 25px;
    }

    .circular-tool {
        padding: 0.6rem 1rem;
        border-radius: 25px;
    }

    .circular-tool-icon {
        width: 40px;
        height: 40px;
        font-size: 1rem;
    }

    .circular-input,
    .circular-select {
        font-size: 0.9rem;
    }

    .circular-action-btn {
        padding: 0.6rem 1.2rem;
        font-size: 0.8rem;
        border-radius: 25px;
    }

    .circular-stats-container {
        height: 300px;
    }

    .circular-stats-center {
        width: 100px;
        height: 100px;
    }

    .circular-stats-center h3 {
        font-size: 0.9rem;
    }

    .circular-stats-center p {
        font-size: 0.7rem;
    }

    .circular-stat-circle {
        width: 60px;
        height: 60px;
    }

    .circular-stat-circle i {
        font-size: 0.9rem;
    }

    .circular-stat-number {
        font-size: 0.75rem;
    }

    .circular-stat-label {
        font-size: 0.65rem;
        padding: 0.15rem 0.5rem;
    }
}

@media (max-width: 576px) {
    .circular-toolbar {
        padding: 0.8rem;
        margin-bottom: 1rem;
    }

    .circular-tool {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
        padding: 0.8rem;
    }

    .circular-tool-icon {
        width: 35px;
        height: 35px;
        font-size: 0.9rem;
    }

    .circular-actions {
        flex-direction: column;
        gap: 0.5rem;
    }

    .circular-action-btn {
        width: 100%;
        max-width: none;
    }

    .circular-stats-container {
        height: 250px;
    }

    .circular-stats-center {
        width: 80px;
        height: 80px;
    }

    .circular-stats-center h3 {
        font-size: 0.8rem;
    }

    .circular-stats-center p {
        font-size: 0.6rem;
    }

    .circular-stat-circle {
        width: 50px;
        height: 50px;
    }

    .circular-stat-circle i {
        font-size: 0.8rem;
        margin-bottom: 0.1rem;
    }

    .circular-stat-number {
        font-size: 0.7rem;
    }

    .circular-stat-label {
        font-size: 0.6rem;
        padding: 0.1rem 0.4rem;
    }
}

.toolbar-section {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 2rem;
}

.tool-group {
    display: flex;
    gap: 1rem;
    flex: 1;
}

.tool-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
    min-width: 200px;
}

.tool-item:hover {
    border-color: #667eea;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.1);
}

.tool-item i {
    color: #667eea;
    font-size: 1.1rem;
}

.tool-input,
.tool-select {
    border: none;
    outline: none;
    background: transparent;
    flex: 1;
    font-weight: 500;
    color: #495057;
}

.tool-input::placeholder {
    color: #adb5bd;
}

.tool-actions {
    display: flex;
    gap: 0.5rem;
}

.tool-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 10px;
    font-weight: 600;
    font-size: 0.9rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.tool-btn.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.tool-btn.secondary {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    color: #495057;
    border: 2px solid #dee2e6;
}

.tool-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* منطقة المحتوى */
.content-area {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.data-container {
    width: 100%;
}

.data-header {
    margin-bottom: 2rem;
}

.data-stats {
    display: flex;
    gap: 2rem;
    justify-content: center;
    flex-wrap: wrap;
}

.stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 1.5rem;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 16px;
    border: 2px solid #e9ecef;
    transition: all 0.3s ease;
    min-width: 150px;
    position: relative;
    overflow: hidden;
}

.stat-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.stat-item:hover::before {
    opacity: 1;
}

.stat-item:hover {
    transform: translateY(-5px);
    border-color: #667eea;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.15);
}

.stat-item i {
    font-size: 2rem;
    margin-bottom: 0.5rem;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.9rem;
    color: #6c757d;
    font-weight: 500;
    text-align: center;
}

/* قسم الجداول */
.table-section {
    background: #ffffff;
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
    margin-top: 2rem;
}

.table-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.table-header h3 {
    font-size: 1.3rem;
    font-weight: 700;
    color: #2c3e50;
    margin: 0;
}

.table-controls {
    display: flex;
    gap: 0.5rem;
}

.control-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.6rem 1rem;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    font-size: 0.85rem;
    transition: all 0.3s ease;
    cursor: pointer;
}

.control-btn.success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
    color: white;
}

.control-btn.info {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
}

.control-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.15);
}

.table-wrapper {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    border: 1px solid #e9ecef;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    margin: 0;
}

.data-table thead {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
}

.data-table thead th {
    padding: 1rem 0.8rem;
    color: white;
    font-weight: 600;
    text-align: center;
    border: none;
    font-size: 0.85rem;
}

.data-table thead th i {
    margin-left: 0.3rem;
    opacity: 0.8;
    font-size: 0.75rem;
}

.data-table tbody td {
    padding: 1rem 0.8rem;
    text-align: center;
    border-bottom: 1px solid #f1f1f1;
    font-weight: 500;
    color: #495057;
    transition: all 0.3s ease;
    font-size: 0.85rem;
}

.data-table tbody tr {
    transition: all 0.3s ease;
}

.data-table tbody tr:hover {
    background: rgba(102, 126, 234, 0.05);
}

.data-table tbody tr:hover td {
    color: #2c3e50;
}

/* أزرار الإجراءات في الجداول */
.table-action-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 35px;
    height: 35px;
    border: none;
    border-radius: 8px;
    margin: 0 2px;
    transition: all 0.3s ease;
    cursor: pointer;
    font-size: 0.9rem;
}

.table-action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.table-action-btn.edit {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
}

.table-action-btn.delete {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
}

.table-action-btn.view {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

/* شارات الحالة المحسنة */
.status-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    text-align: center;
    white-space: nowrap;
    position: relative;
    overflow: hidden;
}

.status-badge::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.8s;
}

.status-badge:hover::before {
    left: 100%;
}

.status-badge.active {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.status-badge.inactive {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
    color: white;
}

.status-badge.pending {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    color: #212529;
}

.status-badge.processing {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
}

.status-badge.completed {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.status-badge.cancelled {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
}

/* الرسوم المتحركة */
@keyframes iconPulse {
    0%, 100% {
        transform: scale(1);
        opacity: 0.8;
    }
    50% {
        transform: scale(1.1);
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* تطبيق الرسوم المتحركة */
.window-header {
    animation: fadeInUp 0.6s ease-out;
}

.toolbar-container {
    animation: fadeInUp 0.8s ease-out;
}

.content-area {
    animation: fadeInUp 1s ease-out;
}

.stat-item {
    animation: slideInRight 0.6s ease-out;
}

.stat-item:nth-child(1) { animation-delay: 0.1s; }
.stat-item:nth-child(2) { animation-delay: 0.2s; }
.stat-item:nth-child(3) { animation-delay: 0.3s; }
.stat-item:nth-child(4) { animation-delay: 0.4s; }

/* تحسينات للاستجابة */
@media (max-width: 1200px) {
    .window-header-content {
        gap: 1.5rem;
    }

    .window-icon {
        width: 70px;
        height: 70px;
        font-size: 1.8rem;
    }

    .window-title {
        font-size: 2.2rem;
    }

    .toolbar-section {
        flex-direction: column;
        gap: 1rem;
    }

    .tool-group {
        flex-wrap: wrap;
        gap: 0.8rem;
    }

    .tool-item {
        min-width: 180px;
    }

    .data-stats {
        gap: 1.5rem;
    }
}

@media (max-width: 992px) {
    .window-header {
        padding: 1.5rem;
    }

    .window-header-content {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .window-title {
        font-size: 2rem;
    }

    .window-actions {
        justify-content: center;
    }

    .action-btn {
        min-width: 120px;
        padding: 0.8rem 1.2rem;
    }

    .toolbar-container {
        padding: 1.2rem;
    }

    .tool-item {
        min-width: 160px;
    }

    .content-area {
        padding: 1.5rem;
    }

    .data-stats {
        gap: 1rem;
    }

    .stat-item {
        min-width: 130px;
        padding: 1.2rem;
    }
}

@media (max-width: 768px) {
    .window-header {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .window-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .window-title {
        font-size: 1.8rem;
    }

    .window-subtitle {
        font-size: 1rem;
    }

    .window-actions {
        flex-direction: column;
        width: 100%;
    }

    .action-btn {
        width: 100%;
        justify-content: center;
    }

    .toolbar-container {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .tool-group {
        flex-direction: column;
        gap: 0.5rem;
    }

    .tool-item {
        min-width: auto;
        width: 100%;
    }

    .tool-actions {
        width: 100%;
        justify-content: center;
    }

    .tool-btn {
        flex: 1;
    }

    .content-area {
        padding: 1rem;
    }

    .data-stats {
        flex-direction: column;
        align-items: center;
        gap: 0.8rem;
    }

    .stat-item {
        width: 100%;
        max-width: 200px;
        padding: 1rem;
    }

    .stat-number {
        font-size: 1.8rem;
    }

    .modern-table thead th {
        padding: 1rem 0.5rem;
        font-size: 0.8rem;
    }

    .modern-table tbody td {
        padding: 0.8rem 0.5rem;
        font-size: 0.85rem;
    }

    .table-action-btn {
        width: 30px;
        height: 30px;
        font-size: 0.8rem;
    }
}

@media (max-width: 576px) {
    .window-title {
        font-size: 1.5rem;
    }

    .window-subtitle {
        font-size: 0.9rem;
    }

    .action-btn {
        padding: 0.6rem 1rem;
        font-size: 0.8rem;
    }

    .tool-btn {
        padding: 0.6rem 1rem;
        font-size: 0.8rem;
    }

    .stat-item {
        padding: 0.8rem;
    }

    .stat-item i {
        font-size: 1.5rem;
    }

    .stat-number {
        font-size: 1.5rem;
    }

    .stat-label {
        font-size: 0.8rem;
    }

    .modern-table {
        font-size: 0.8rem;
    }

    .modern-table thead th {
        padding: 0.8rem 0.3rem;
        font-size: 0.75rem;
    }

    .modern-table tbody td {
        padding: 0.6rem 0.3rem;
        font-size: 0.8rem;
    }

    .table-action-btn {
        width: 28px;
        height: 28px;
        font-size: 0.75rem;
        margin: 0 1px;
    }

    .status-badge {
        font-size: 0.7rem;
        padding: 0.3rem 0.6rem;
    }
}

/* النوافذ الجانبية */
.sidebar-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(5px);
    z-index: 1040;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.sidebar-overlay.show {
    opacity: 1;
    visibility: visible;
}

.sidebar {
    position: fixed;
    top: 0;
    right: -600px;
    width: 600px;
    height: 100%;
    background: #ffffff;
    box-shadow: -5px 0 25px rgba(0, 0, 0, 0.15);
    z-index: 1041;
    transition: right 0.3s ease;
    display: flex;
    flex-direction: column;
}

.sidebar.show {
    right: 0;
}

.sidebar-header {
    padding: 1.5rem;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
    color: white;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.sidebar-title {
    margin: 0;
    font-weight: 600;
    flex: 1;
}

.btn-close-sidebar {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    padding: 0.5rem;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.btn-close-sidebar:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: rotate(90deg);
}

.sidebar-content {
    flex: 1;
    padding: 1.5rem;
    overflow-y: auto;
    scrollbar-width: thin;
    scrollbar-color: #dee2e6 #f8f9fa;
}

.sidebar-content::-webkit-scrollbar {
    width: 6px;
}

.sidebar-content::-webkit-scrollbar-track {
    background: #f8f9fa;
}

.sidebar-content::-webkit-scrollbar-thumb {
    background: #dee2e6;
    border-radius: 3px;
}

.sidebar-content::-webkit-scrollbar-thumb:hover {
    background: #adb5bd;
}

.sidebar-footer {
    padding: 1.5rem;
    border-top: 1px solid #e9ecef;
    background: #f8f9fa;
}

/* أقسام النموذج */
.form-section {
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border-radius: 12px;
    border: 1px solid #e9ecef;
    position: relative;
    overflow: hidden;
}

.form-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
}

.form-section-title {
    color: var(--primary-color);
    font-weight: 600;
    margin-bottom: 1rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid #e9ecef;
    display: flex;
    align-items: center;
}

.form-section-title i {
    color: var(--secondary-color);
}

/* تحسين الحقول */
.form-label.required::after {
    content: ' *';
    color: #dc3545;
    font-weight: bold;
}

.sidebar .form-control,
.sidebar .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
    font-weight: 500;
}

.sidebar .form-control:focus,
.sidebar .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
    transform: translateY(-1px);
}

.sidebar .form-control:hover,
.sidebar .form-select:hover {
    border-color: #ced4da;
}

/* مجموعات الإدخال */
.sidebar .input-group {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.sidebar .input-group .form-control {
    border-radius: 0;
    border-right: none;
}

.sidebar .input-group .currency-select {
    border-radius: 0;
    border-left: none;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    font-weight: 600;
    color: var(--dark-color);
    min-width: 120px;
}

.sidebar .input-group .currency-select:focus {
    background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
}

/* منطقة رفع الملفات */
.file-upload-area {
    border: 2px dashed #dee2e6;
    border-radius: 12px;
    padding: 2rem;
    text-align: center;
    background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.file-upload-area::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle, rgba(102, 126, 234, 0.1) 0%, transparent 70%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.file-upload-area:hover::before {
    opacity: 1;
}

.file-upload-area:hover {
    border-color: var(--primary-color);
    background: linear-gradient(135deg, #ffffff 0%, #f0f4ff 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.file-upload-area.dragover {
    border-color: var(--success-color);
    background: linear-gradient(135deg, #f0fff4 0%, #ffffff 100%);
    transform: scale(1.02);
}

/* قائمة المرفقات */
.attachment-item {
    display: flex;
    align-items: center;
    padding: 0.75rem;
    background: #f8f9fa;
    border-radius: 8px;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.attachment-item:hover {
    background: #e9ecef;
    transform: translateX(5px);
}

.attachment-item i {
    font-size: 1.5rem;
    margin-left: 1rem;
    color: var(--primary-color);
}

.attachment-item .file-info {
    flex: 1;
}

.attachment-item .file-name {
    font-weight: 600;
    color: var(--dark-color);
}

.attachment-item .file-size {
    font-size: 0.8rem;
    color: #6c757d;
}

.attachment-item .btn-remove {
    background: none;
    border: none;
    color: #dc3545;
    padding: 0.25rem;
    border-radius: 4px;
    transition: all 0.3s ease;
}

.attachment-item .btn-remove:hover {
    background: #dc3545;
    color: white;
    transform: scale(1.1);
}

/* تحسينات للشاشات الصغيرة للنوافذ الجانبية */
@media (max-width: 768px) {
    .sidebar {
        width: 100%;
        right: -100%;
    }

    .sidebar-header {
        padding: 1rem;
    }

    .sidebar-content {
        padding: 1rem;
    }

    .sidebar-footer {
        padding: 1rem;
    }

    .form-section {
        padding: 1rem;
        margin-bottom: 1rem;
    }

    .form-section-title {
        font-size: 0.9rem;
    }

    .file-upload-area {
        padding: 1.5rem 1rem;
    }
}

/* تحسينات إضافية للنوافذ */
.content-section {
    display: none;
    animation: fadeIn 0.5s ease-in-out;
}

.content-section.active {
    display: block;
}

.page-header {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 249, 250, 0.95) 100%);
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    backdrop-filter: blur(10px);
}

.page-header h2 {
    margin-bottom: 0.25rem;
    font-weight: 700;
}

.page-header p {
    margin-bottom: 0;
    font-size: 1rem;
}

/* تحسين الجداول */
.table-responsive {
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.table {
    margin-bottom: 0;
}

.table thead th {
    background: linear-gradient(135deg, #343a40 0%, #495057 100%);
    color: white;
    font-weight: 600;
    text-align: center;
    border: none;
    padding: 1rem 0.75rem;
    font-size: 0.9rem;
}

.table tbody td {
    padding: 0.75rem;
    vertical-align: middle;
    border-color: #f1f1f1;
    font-size: 0.9rem;
}

.table tbody tr:hover {
    background: rgba(102, 126, 234, 0.05);
    transform: scale(1.001);
    transition: all 0.3s ease;
}

/* أزرار الإجراءات */
.btn-group .btn {
    border-radius: 6px !important;
    margin: 0 1px;
    font-size: 0.8rem;
    padding: 0.375rem 0.5rem;
    transition: all 0.3s ease;
}

.btn-group .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* شارات الحالة */
.status-badge {
    font-size: 0.75rem;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-weight: 600;
    text-align: center;
    white-space: nowrap;
}

.status-badge.processing {
    background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
    color: white;
}

.status-badge.delivered {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
}

.status-badge.pending {
    background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%);
    color: #212529;
}

.status-badge.stamped {
    background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
    color: white;
}

/* ================ أنماط الجدول المحسن لحجوزات العمرة ================ */

/* ملء الشاشة للجدول */
.table-fullscreen {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;
    width: 100vw !important;
    height: 100vh !important;
    z-index: 9999 !important;
    background: white;
    box-shadow: 0 0 50px rgba(0,0,0,0.5);
}

/* حاوي التمرير المحسن */
.table-scroll-container {
    position: relative;
}

.table-scroll-container .table-responsive {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
}

/* الأعمدة المثبتة */
.table th[style*="position: sticky"],
.table td[style*="position: sticky"] {
    border-right: 2px solid #dee2e6;
    box-shadow: 2px 0 5px rgba(0,0,0,0.1);
}

/* شريط التنقل */
.table-navigation-bar {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-top: 1px solid #dee2e6;
}

.table-navigation-bar .btn {
    border-radius: 50%;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

.table-navigation-bar .btn:hover {
    transform: scale(1.1);
    box-shadow: 0 2px 8px rgba(0,0,0,0.15);
}

/* تحسينات الجدول */
#umrahBookingsTable {
    border-collapse: separate;
    border-spacing: 0;
}

#umrahBookingsTable th {
    background: linear-gradient(135deg, #343a40 0%, #495057 100%);
    color: white;
    font-weight: 600;
    text-align: center;
    vertical-align: middle;
    border: 1px solid #495057;
    padding: 12px 8px;
    font-size: 13px;
}

#umrahBookingsTable td {
    vertical-align: middle;
    padding: 10px 8px;
    border: 1px solid #dee2e6;
    font-size: 12px;
    transition: background-color 0.2s ease;
}

#umrahBookingsTable tbody tr:hover {
    background-color: rgba(0,123,255,0.05);
    transform: scale(1.001);
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* تحسينات الشارات */
.badge.fs-6 {
    font-size: 0.75rem !important;
    padding: 0.35em 0.65em;
    font-weight: 500;
}

/* تحسينات الأيقونات */
#umrahBookingsTable .fas {
    font-size: 12px;
    opacity: 0.8;
}

/* تحسينات الأزرار */
#umrahBookingsTable .btn-group .btn {
    padding: 0.25rem 0.4rem;
    font-size: 11px;
    border-radius: 0.25rem;
    margin: 0 1px;
}

/* تحسينات النصوص */
.text-truncate {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* تحسينات الألوان المالية */
.text-success {
    color: #198754 !important;
    font-weight: 600;
}

.text-danger {
    color: #dc3545 !important;
    font-weight: 600;
}

.text-warning {
    color: #fd7e14 !important;
    font-weight: 600;
}

.text-primary {
    color: #0d6efd !important;
    font-weight: 600;
}

/* تحسينات الاستجابة */
@media (max-width: 768px) {
    #umrahBookingsTable {
        min-width: 1500px;
    }

    .table-navigation-bar {
        padding: 8px !important;
    }

    .table-navigation-bar .btn {
        width: 30px;
        height: 30px;
        font-size: 12px;
    }
}

/* تحسينات التمرير */
.table-responsive::-webkit-scrollbar {
    height: 8px;
    width: 8px;
}

.table-responsive::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
    border-radius: 4px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #0056b3 0%, #004085 100%);
}

/* تحسينات أدوات التحكم */
.table-controls {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 10px;
    margin-bottom: 15px;
}

.table-controls .btn {
    transition: all 0.2s ease;
}

.table-controls .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

/* تحسينات الإحصائيات */
.table-stats .badge {
    font-size: 0.8rem;
    padding: 0.5em 0.75em;
    margin: 0 0.25rem;
    transition: all 0.2s ease;
}

.table-stats .badge:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 8px rgba(0,0,0,0.2);
}
