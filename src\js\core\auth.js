/**
 * ===================================
 * نظام المصادقة - Authentication System
 * ===================================
 */

window.Auth = {
    // إعدادات المصادقة
    config: {
        sessionKey: 'qimat_alwaed_session',
        tokenKey: 'qimat_alwaed_token',
        userKey: 'qimat_alwaed_user',
        sessionTimeout: 8 * 60 * 60 * 1000, // 8 ساعات
        rememberMeDuration: 30 * 24 * 60 * 60 * 1000, // 30 يوم
        maxLoginAttempts: 5,
        lockoutDuration: 15 * 60 * 1000 // 15 دقيقة
    },

    // حالة المصادقة
    state: {
        isAuthenticated: false,
        currentUser: null,
        sessionStartTime: null,
        lastActivity: null,
        loginAttempts: 0,
        isLocked: false,
        lockoutEndTime: null
    },

    // المستخدمون الافتراضيون
    defaultUsers: [
        {
            id: 1,
            username: 'admin',
            email: '<EMAIL>',
            password: 'admin123', // في الواقع يجب تشفيرها
            full_name: 'مدير النظام',
            role: 'admin',
            permissions: ['*'],
            is_active: true,
            created_at: new Date().toISOString()
        },
        {
            id: 2,
            username: 'user',
            email: '<EMAIL>',
            password: 'user123',
            full_name: 'مستخدم عادي',
            role: 'user',
            permissions: ['read', 'write'],
            is_active: true,
            created_at: new Date().toISOString()
        }
    ],

    /**
     * تهيئة نظام المصادقة
     */
    init: function() {
        console.log('🔄 تهيئة نظام المصادقة');
        
        try {
            // إنشاء المستخدمين الافتراضيين
            this.createDefaultUsers();
            
            // استعادة الجلسة
            this.restoreSession();
            
            // بدء مراقبة النشاط
            this.startActivityMonitoring();
            
            console.log('✅ تم تهيئة نظام المصادقة بنجاح');
            
        } catch (error) {
            console.error('❌ خطأ في تهيئة نظام المصادقة:', error);
        }
    },

    /**
     * إنشاء المستخدمين الافتراضيين
     */
    createDefaultUsers: function() {
        if (window.Database) {
            const existingUsers = window.Database.findAll('users');
            
            if (existingUsers.length === 0) {
                this.defaultUsers.forEach(user => {
                    // تشفير كلمة المرور (محاكاة)
                    user.password_hash = this.hashPassword(user.password);
                    delete user.password;
                    
                    window.Database.insert('users', user);
                });
                
                console.log('👥 تم إنشاء المستخدمين الافتراضيين');
            }
        }
    },

    /**
     * تسجيل الدخول المحسن
     */
    login: function(credentials) {
        return new Promise((resolve, reject) => {
            try {
                // التحقق من القفل
                if (this.isAccountLocked()) {
                    reject(new Error('الحساب مقفل مؤقتاً. يرجى المحاولة لاحقاً.'));
                    return;
                }

                const { username, password, rememberMe = false, twoFactorCode = null } = credentials;

                // التحقق من صحة البيانات
                if (!username || !password) {
                    reject(new Error('يرجى إدخال اسم المستخدم وكلمة المرور'));
                    return;
                }

                // البحث عن المستخدم
                const user = this.findUser(username);
                if (!user) {
                    this.handleFailedLogin(username);
                    reject(new Error('اسم المستخدم أو كلمة المرور غير صحيحة'));
                    return;
                }

                // التحقق من كلمة المرور
                if (!this.verifyPassword(password, user.password_hash)) {
                    this.handleFailedLogin(username, user.id);
                    reject(new Error('اسم المستخدم أو كلمة المرور غير صحيحة'));
                    return;
                }

                // التحقق من حالة المستخدم
                if (!user.is_active) {
                    this.logActivity(user.id, 'login', 'error', 'محاولة دخول لحساب غير نشط');
                    reject(new Error('الحساب غير نشط. يرجى التواصل مع المدير'));
                    return;
                }

                // التحقق من صلاحية الاشتراك
                const subscriptionCheck = this.checkSubscriptionValidity(user);
                if (!subscriptionCheck.isValid) {
                    this.logActivity(user.id, 'login', 'error', 'محاولة دخول لحساب منتهي الصلاحية');
                    reject(new Error(subscriptionCheck.message));
                    return;
                }

                // التحقق من المصادقة الثنائية
                if (user.two_factor_enabled) {
                    if (!twoFactorCode) {
                        resolve({ requiresTwoFactor: true, userId: user.id });
                        return;
                    }

                    if (!this.verifyTwoFactorCode(user, twoFactorCode)) {
                        this.handleFailedLogin(username, user.id);
                        reject(new Error('رمز المصادقة الثنائية غير صحيح'));
                        return;
                    }
                }

                // التحقق من الجلسات المتزامنة
                const maxSessions = this.getSecuritySetting('max_concurrent_sessions', 3);
                if (this.getActiveSessionsCount(user.id) >= maxSessions) {
                    reject(new Error(`تم الوصول للحد الأقصى من الجلسات المتزامنة (${maxSessions})`));
                    return;
                }

                // إنشاء الجلسة
                const session = this.createSession(user, rememberMe);

                // إعادة تعيين محاولات تسجيل الدخول
                this.resetLoginAttempts();

                // تسجيل النشاط
                this.logActivity(user.id, 'login', 'success', 'تسجيل دخول ناجح', session.id);

                // تحديث آخر دخول
                this.updateLastLogin(user.id);

                console.log(`✅ تم تسجيل دخول المستخدم: ${user.username}`);
                resolve({ user, session });

            } catch (error) {
                console.error('❌ خطأ في تسجيل الدخول:', error);
                reject(error);
            }
        });
    },

    /**
     * تسجيل الخروج
     */
    logout: function() {
        try {
            // مسح الجلسة
            this.clearSession();
            
            // تحديث الحالة
            this.state.isAuthenticated = false;
            this.state.currentUser = null;
            this.state.sessionStartTime = null;
            this.state.lastActivity = null;

            // إرسال حدث تسجيل الخروج
            this.emitAuthEvent('logout');

            console.log('✅ تم تسجيل الخروج بنجاح');

            // إعادة توجيه لصفحة تسجيل الدخول
            if (window.Router) {
                window.Router.navigate('login');
            }

        } catch (error) {
            console.error('❌ خطأ في تسجيل الخروج:', error);
        }
    },

    /**
     * البحث عن مستخدم
     */
    findUser: function(username) {
        if (window.Database) {
            const users = window.Database.findAll('users');
            return users.find(user => 
                user.username === username || user.email === username
            );
        }
        return null;
    },

    /**
     * تشفير كلمة المرور (محاكاة)
     */
    hashPassword: function(password) {
        // في الواقع يجب استخدام خوارزمية تشفير قوية
        return btoa(password + 'salt_key_qimat_alwaed');
    },

    /**
     * التحقق من كلمة المرور
     */
    verifyPassword: function(password, hash) {
        return this.hashPassword(password) === hash;
    },

    /**
     * إنشاء جلسة
     */
    createSession: function(user, rememberMe = false) {
        const now = new Date();
        const sessionData = {
            userId: user.id,
            username: user.username,
            role: user.role,
            permissions: user.permissions,
            startTime: now.toISOString(),
            lastActivity: now.toISOString(),
            rememberMe: rememberMe,
            expiresAt: new Date(now.getTime() + 
                (rememberMe ? this.config.rememberMeDuration : this.config.sessionTimeout)
            ).toISOString()
        };

        // حفظ الجلسة
        localStorage.setItem(this.config.sessionKey, JSON.stringify(sessionData));
        
        // حفظ بيانات المستخدم
        const userData = { ...user };
        delete userData.password_hash; // عدم حفظ كلمة المرور
        localStorage.setItem(this.config.userKey, JSON.stringify(userData));

        // تحديث الحالة
        this.state.isAuthenticated = true;
        this.state.currentUser = userData;
        this.state.sessionStartTime = now;
        this.state.lastActivity = now;

        // إرسال حدث تسجيل الدخول
        this.emitAuthEvent('login', userData);
    },

    /**
     * استعادة الجلسة
     */
    restoreSession: function() {
        try {
            const sessionData = localStorage.getItem(this.config.sessionKey);
            const userData = localStorage.getItem(this.config.userKey);

            if (!sessionData || !userData) {
                return false;
            }

            const session = JSON.parse(sessionData);
            const user = JSON.parse(userData);

            // التحقق من انتهاء الجلسة
            const now = new Date();
            const expiresAt = new Date(session.expiresAt);

            if (now > expiresAt) {
                this.clearSession();
                return false;
            }

            // استعادة الحالة
            this.state.isAuthenticated = true;
            this.state.currentUser = user;
            this.state.sessionStartTime = new Date(session.startTime);
            this.state.lastActivity = new Date(session.lastActivity);

            // تحديث النشاط
            this.updateActivity();

            console.log(`✅ تم استعادة جلسة المستخدم: ${user.username}`);
            return true;

        } catch (error) {
            console.error('❌ خطأ في استعادة الجلسة:', error);
            this.clearSession();
            return false;
        }
    },

    /**
     * مسح الجلسة
     */
    clearSession: function() {
        localStorage.removeItem(this.config.sessionKey);
        localStorage.removeItem(this.config.userKey);
        localStorage.removeItem(this.config.tokenKey);
    },

    /**
     * تحديث النشاط
     */
    updateActivity: function() {
        if (this.state.isAuthenticated) {
            const now = new Date();
            this.state.lastActivity = now;

            // تحديث الجلسة المحفوظة
            const sessionData = localStorage.getItem(this.config.sessionKey);
            if (sessionData) {
                const session = JSON.parse(sessionData);
                session.lastActivity = now.toISOString();
                localStorage.setItem(this.config.sessionKey, JSON.stringify(session));
            }
        }
    },

    /**
     * بدء مراقبة النشاط
     */
    startActivityMonitoring: function() {
        // تحديث النشاط عند التفاعل
        const events = ['click', 'keypress', 'scroll', 'mousemove'];
        events.forEach(event => {
            document.addEventListener(event, () => {
                this.updateActivity();
            }, { passive: true });
        });

        // فحص انتهاء الجلسة كل دقيقة
        setInterval(() => {
            this.checkSessionExpiry();
        }, 60000);
    },

    /**
     * فحص انتهاء الجلسة
     */
    checkSessionExpiry: function() {
        if (!this.state.isAuthenticated) return;

        const sessionData = localStorage.getItem(this.config.sessionKey);
        if (!sessionData) {
            this.logout();
            return;
        }

        const session = JSON.parse(sessionData);
        const now = new Date();
        const expiresAt = new Date(session.expiresAt);

        if (now > expiresAt) {
            console.warn('⚠️ انتهت صلاحية الجلسة');
            this.logout();
            
            if (window.Notifications) {
                window.Notifications.warning('انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى.');
            }
        }
    },

    /**
     * معالجة فشل تسجيل الدخول
     */
    handleFailedLogin: function() {
        this.state.loginAttempts++;
        
        if (this.state.loginAttempts >= this.config.maxLoginAttempts) {
            this.lockAccount();
        }
    },

    /**
     * قفل الحساب
     */
    lockAccount: function() {
        this.state.isLocked = true;
        this.state.lockoutEndTime = new Date(Date.now() + this.config.lockoutDuration);
        
        console.warn('⚠️ تم قفل الحساب مؤقتاً');
        
        if (window.Notifications) {
            window.Notifications.error('تم قفل الحساب مؤقتاً بسبب محاولات تسجيل دخول فاشلة متعددة.');
        }
    },

    /**
     * التحقق من قفل الحساب
     */
    isAccountLocked: function() {
        if (!this.state.isLocked) return false;
        
        const now = new Date();
        if (now > this.state.lockoutEndTime) {
            this.resetLoginAttempts();
            return false;
        }
        
        return true;
    },

    /**
     * إعادة تعيين محاولات تسجيل الدخول
     */
    resetLoginAttempts: function() {
        this.state.loginAttempts = 0;
        this.state.isLocked = false;
        this.state.lockoutEndTime = null;
    },

    /**
     * إرسال حدث المصادقة
     */
    emitAuthEvent: function(type, data = null) {
        const event = new CustomEvent(`auth:${type}`, {
            detail: data
        });
        document.dispatchEvent(event);
    },

    /**
     * التحقق من المصادقة
     */
    isAuthenticated: function() {
        return this.state.isAuthenticated;
    },

    /**
     * الحصول على المستخدم الحالي
     */
    getCurrentUser: function() {
        return this.state.currentUser;
    },

    /**
     * التحقق من الصلاحية
     */
    hasPermission: function(permission) {
        if (!this.state.currentUser) return false;
        
        const permissions = this.state.currentUser.permissions || [];
        return permissions.includes('*') || permissions.includes(permission);
    },

    /**
     * التحقق من الدور
     */
    hasRole: function(role) {
        if (!this.state.currentUser) return false;
        return this.state.currentUser.role === role;
    },

    /**
     * التحقق من صلاحية الاشتراك
     */
    checkSubscriptionValidity: function(user) {
        const now = new Date();
        const startDate = new Date(user.subscription_start_date);
        const endDate = new Date(user.subscription_end_date);

        if (now < startDate) {
            return {
                isValid: false,
                message: 'الاشتراك لم يبدأ بعد'
            };
        }

        if (now > endDate) {
            return {
                isValid: false,
                message: 'انتهت صلاحية الاشتراك. يرجى التواصل مع المدير'
            };
        }

        // تحذير إذا كان الاشتراك ينتهي خلال 7 أيام
        const daysRemaining = Math.ceil((endDate - now) / (1000 * 60 * 60 * 24));
        if (daysRemaining <= 7) {
            return {
                isValid: true,
                warning: `ينتهي الاشتراك خلال ${daysRemaining} أيام`
            };
        }

        return { isValid: true };
    },

    /**
     * التحقق من المصادقة الثنائية
     */
    verifyTwoFactorCode: function(user, code) {
        // محاكاة التحقق من رمز المصادقة الثنائية
        // في التطبيق الحقيقي، يجب استخدام مكتبة مثل speakeasy
        const expectedCode = this.generateTwoFactorCode(user.two_factor_secret);
        return code === expectedCode;
    },

    /**
     * توليد رمز المصادقة الثنائية
     */
    generateTwoFactorCode: function(secret) {
        // محاكاة توليد الرمز - في التطبيق الحقيقي استخدم مكتبة مناسبة
        const timestamp = Math.floor(Date.now() / 30000);
        return ((timestamp + secret.length) % 1000000).toString().padStart(6, '0');
    },

    /**
     * معالجة فشل تسجيل الدخول
     */
    handleFailedLogin: function(username, userId = null) {
        this.state.loginAttempts++;

        // تسجيل المحاولة المشبوهة
        this.logSuspiciousAttempt(username, 'invalid_credentials');

        // قفل الحساب إذا تم الوصول للحد الأقصى
        if (this.state.loginAttempts >= this.config.maxLoginAttempts) {
            this.lockAccount();

            if (userId) {
                this.logActivity(userId, 'login', 'error', 'قفل الحساب بسبب محاولات فاشلة متعددة');
            }
        }
    },

    /**
     * تسجيل المحاولات المشبوهة
     */
    logSuspiciousAttempt: function(username, reason) {
        const attempt = {
            id: Date.now(),
            username: username,
            ip_address: this.getCurrentIP(),
            user_agent: navigator.userAgent,
            attempt_time: new Date().toISOString(),
            failure_reason: reason,
            is_blocked: false
        };

        // حفظ في التخزين المحلي (في التطبيق الحقيقي، احفظ في قاعدة البيانات)
        const attempts = JSON.parse(localStorage.getItem('suspicious_attempts') || '[]');
        attempts.push(attempt);
        localStorage.setItem('suspicious_attempts', JSON.stringify(attempts));
    },

    /**
     * تسجيل النشاط
     */
    logActivity: function(userId, actionType, status = 'success', description = '', sessionId = null) {
        const activity = {
            id: Date.now(),
            user_id: userId,
            session_id: sessionId,
            action_type: actionType,
            description: description,
            ip_address: this.getCurrentIP(),
            user_agent: navigator.userAgent,
            response_status: status,
            created_at: new Date().toISOString()
        };

        // حفظ في التخزين المحلي (في التطبيق الحقيقي، احفظ في قاعدة البيانات)
        const activities = JSON.parse(localStorage.getItem('user_activities') || '[]');
        activities.push(activity);

        // الاحتفاظ بآخر 1000 نشاط فقط
        if (activities.length > 1000) {
            activities.splice(0, activities.length - 1000);
        }

        localStorage.setItem('user_activities', JSON.stringify(activities));
    },

    /**
     * الحصول على عدد الجلسات النشطة
     */
    getActiveSessionsCount: function(userId) {
        const sessions = JSON.parse(localStorage.getItem('user_sessions') || '[]');
        const now = new Date();

        return sessions.filter(session =>
            session.user_id === userId &&
            session.is_active &&
            new Date(session.expires_at) > now
        ).length;
    },

    /**
     * الحصول على إعداد الأمان
     */
    getSecuritySetting: function(settingName, defaultValue) {
        const settings = JSON.parse(localStorage.getItem('security_settings') || '{}');
        return settings[settingName] || defaultValue;
    },

    /**
     * تحديث آخر دخول
     */
    updateLastLogin: function(userId) {
        const users = JSON.parse(localStorage.getItem('users') || '[]');
        const userIndex = users.findIndex(u => u.id === userId);

        if (userIndex !== -1) {
            users[userIndex].last_login = new Date().toISOString();
            users[userIndex].ip_address = this.getCurrentIP();
            users[userIndex].user_agent = navigator.userAgent;
            localStorage.setItem('users', JSON.stringify(users));
        }
    },

    /**
     * الحصول على IP الحالي (محاكاة)
     */
    getCurrentIP: function() {
        // في التطبيق الحقيقي، احصل على IP من الخادم
        return '***********';
    }

    /**
     * تغيير كلمة المرور
     */
    changePassword: function(oldPassword, newPassword) {
        return new Promise((resolve, reject) => {
            try {
                if (!this.state.currentUser) {
                    reject(new Error('يجب تسجيل الدخول أولاً'));
                    return;
                }

                // البحث عن المستخدم
                const user = this.findUser(this.state.currentUser.username);
                if (!user) {
                    reject(new Error('المستخدم غير موجود'));
                    return;
                }

                // التحقق من كلمة المرور القديمة
                if (!this.verifyPassword(oldPassword, user.password_hash)) {
                    reject(new Error('كلمة المرور القديمة غير صحيحة'));
                    return;
                }

                // تحديث كلمة المرور
                const newPasswordHash = this.hashPassword(newPassword);
                
                if (window.Database) {
                    window.Database.update('users', user.id, {
                        password_hash: newPasswordHash,
                        password_changed_at: new Date().toISOString()
                    });
                }

                console.log('✅ تم تغيير كلمة المرور بنجاح');
                resolve();

            } catch (error) {
                console.error('❌ خطأ في تغيير كلمة المرور:', error);
                reject(error);
            }
        });
    },

    /**
     * التحقق من صحة مدخلات تسجيل الدخول
     */
    validateLoginInput: function(username, password) {
        // فحص الطول
        if (!username || username.length < 3 || username.length > 50) {
            return false;
        }

        if (!password || password.length < 6 || password.length > 100) {
            return false;
        }

        // فحص الأحرف المسموحة
        const usernameRegex = /^[a-zA-Z0-9_@.-]+$/;
        if (!usernameRegex.test(username)) {
            return false;
        }

        return true;
    },

    /**
     * إنشاء جلسة آمنة
     */
    createSecureSession: function(user, rememberMe = false) {
        const now = Date.now();
        const sessionDuration = rememberMe ? this.config.rememberMeDuration : this.config.sessionDuration;

        // إنشاء معرف جلسة آمن
        const sessionId = window.Security ? window.Security.generateSecureToken(48) : this.generateSessionId();

        // بيانات الجلسة
        const sessionData = {
            sessionId: sessionId,
            userId: user.id,
            username: user.username,
            role: user.role,
            createdAt: now,
            expiresAt: now + sessionDuration,
            rememberMe: rememberMe,
            ipAddress: this.getClientIP(),
            userAgent: navigator.userAgent
        };

        // حفظ الجلسة
        this.state.isAuthenticated = true;
        this.state.currentUser = user;
        this.state.sessionData = sessionData;
        this.state.sessionStartTime = now;
        this.state.lastActivity = now;

        // حفظ في التخزين
        const storage = rememberMe ? localStorage : sessionStorage;
        storage.setItem('auth_session', JSON.stringify(sessionData));
        storage.setItem('last_activity', now.toString());

        // إرسال حدث تسجيل الدخول
        this.emitAuthEvent('login', user);
    },

    /**
     * تحديث وقت آخر نشاط
     */
    updateLastActivity: function() {
        const now = Date.now();
        this.state.lastActivity = now;

        // تحديث في التخزين
        const storage = this.state.sessionData?.rememberMe ? localStorage : sessionStorage;
        storage.setItem('last_activity', now.toString());
    },

    /**
     * تسجيل النشاط
     */
    logActivity: function(action, details = {}) {
        const logEntry = {
            id: Date.now().toString(36) + Math.random().toString(36).substr(2),
            action: action,
            details: details,
            timestamp: new Date().toISOString(),
            userId: this.state.currentUser?.id || null,
            username: this.state.currentUser?.username || details.username || 'unknown',
            ipAddress: this.getClientIP(),
            userAgent: navigator.userAgent
        };

        // إضافة للسجل
        if (!this.state.activityLog) {
            this.state.activityLog = [];
        }

        this.state.activityLog.push(logEntry);

        // الاحتفاظ بآخر 100 نشاط فقط
        if (this.state.activityLog.length > 100) {
            this.state.activityLog = this.state.activityLog.slice(-100);
        }

        // حفظ السجل
        try {
            localStorage.setItem('auth_activity_log', JSON.stringify(this.state.activityLog));
        } catch (error) {
            console.warn('⚠️ لا يمكن حفظ سجل النشاط:', error);
        }

        // إرسال للنظام الأمني إن وجد
        if (window.Security && window.Security.logSecurityEvent) {
            window.Security.logSecurityEvent(`auth_${action}`, logEntry);
        }
    },

    /**
     * الحصول على IP العميل (محاكاة)
     */
    getClientIP: function() {
        // في بيئة حقيقية، سيتم الحصول على IP من الخادم
        return 'localhost';
    },

    /**
     * إنشاء معرف جلسة
     */
    generateSessionId: function() {
        return Date.now().toString(36) + Math.random().toString(36).substr(2);
    },

    /**
     * التحقق من قوة كلمة المرور
     */
    validatePasswordStrength: function(password) {
        const minLength = 8;
        const hasUpperCase = /[A-Z]/.test(password);
        const hasLowerCase = /[a-z]/.test(password);
        const hasNumbers = /\d/.test(password);
        const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password);

        const score = {
            length: password.length >= minLength,
            upperCase: hasUpperCase,
            lowerCase: hasLowerCase,
            numbers: hasNumbers,
            specialChar: hasSpecialChar
        };

        const strength = Object.values(score).filter(Boolean).length;

        return {
            isValid: strength >= 3,
            score: score,
            strength: strength,
            message: this.getPasswordStrengthMessage(strength)
        };
    },

    /**
     * الحصول على رسالة قوة كلمة المرور
     */
    getPasswordStrengthMessage: function(strength) {
        switch (strength) {
            case 0:
            case 1:
                return 'كلمة مرور ضعيفة جداً';
            case 2:
                return 'كلمة مرور ضعيفة';
            case 3:
                return 'كلمة مرور متوسطة';
            case 4:
                return 'كلمة مرور قوية';
            case 5:
                return 'كلمة مرور قوية جداً';
            default:
                return 'غير محدد';
        }
    },

    /**
     * الحصول على تقرير الأمان
     */
    getSecurityReport: function() {
        return {
            timestamp: new Date().toISOString(),
            currentUser: this.state.currentUser ? {
                username: this.state.currentUser.username,
                role: this.state.currentUser.role,
                lastLogin: this.state.sessionStartTime
            } : null,
            session: {
                isAuthenticated: this.state.isAuthenticated,
                sessionDuration: this.state.lastActivity - this.state.sessionStartTime,
                lastActivity: this.state.lastActivity
            },
            security: {
                loginAttempts: this.state.loginAttempts,
                isLocked: this.state.isLocked,
                lockoutEndTime: this.state.lockoutEndTime
            },
            recentActivity: this.state.activityLog ? this.state.activityLog.slice(-10) : []
        };
    }
};

// تصدير نظام المصادقة للاستخدام العام
if (typeof module !== 'undefined' && module.exports) {
    module.exports = Auth;
}
