<?php
/**
 * ===================================
 * API المصادقة - Authentication API
 * نظام قمة الوعد للسفريات
 * ===================================
 */

require_once '../config.php';
require_once '../includes/database.php';
require_once '../includes/security.php';
require_once '../includes/auth.php';

// إعداد headers
header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: ' . CORS_ALLOWED_ORIGINS);
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// معالجة طلبات OPTIONS
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// التحقق من طريقة الطلب
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['error' => 'طريقة الطلب غير مدعومة']);
    exit();
}

// قراءة البيانات
$input = json_decode(file_get_contents('php://input'), true);
$action = $input['action'] ?? '';

try {
    switch ($action) {
        case 'login':
            handleLogin($input);
            break;
            
        case 'logout':
            handleLogout($input);
            break;
            
        case 'refresh_token':
            handleRefreshToken($input);
            break;
            
        case 'verify_session':
            handleVerifySession($input);
            break;
            
        case 'change_password':
            handleChangePassword($input);
            break;
            
        case 'reset_password':
            handleResetPassword($input);
            break;
            
        case 'verify_two_factor':
            handleVerifyTwoFactor($input);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => 'إجراء غير صحيح']);
            break;
    }
} catch (Exception $e) {
    error_log('Auth API Error: ' . $e->getMessage());
    http_response_code(500);
    echo json_encode(['error' => 'خطأ في الخادم']);
}

/**
 * معالجة تسجيل الدخول
 */
function handleLogin($input) {
    $username = $input['username'] ?? '';
    $password = $input['password'] ?? '';
    $rememberMe = $input['rememberMe'] ?? false;
    $twoFactorCode = $input['twoFactorCode'] ?? '';
    $clientIP = $_SERVER['REMOTE_ADDR'] ?? '';
    $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    
    // التحقق من البيانات المطلوبة
    if (empty($username) || empty($password)) {
        http_response_code(400);
        echo json_encode(['error' => 'اسم المستخدم وكلمة المرور مطلوبان']);
        return;
    }
    
    // التحقق من محاولات تسجيل الدخول
    if (isAccountLocked($username, $clientIP)) {
        http_response_code(429);
        echo json_encode(['error' => 'تم قفل الحساب مؤقتاً بسبب محاولات فاشلة متعددة']);
        return;
    }
    
    // التحقق من Rate Limiting
    if (isRateLimited($clientIP)) {
        http_response_code(429);
        echo json_encode(['error' => 'تم تجاوز الحد المسموح من الطلبات']);
        return;
    }
    
    // البحث عن المستخدم
    $user = findUser($username);
    if (!$user) {
        recordFailedLogin($username, $clientIP, 'invalid_username');
        http_response_code(401);
        echo json_encode(['error' => 'اسم المستخدم أو كلمة المرور غير صحيحة']);
        return;
    }
    
    // التحقق من كلمة المرور
    if (!verifyPassword($password, $user['password_hash'])) {
        recordFailedLogin($username, $clientIP, 'invalid_password');
        http_response_code(401);
        echo json_encode(['error' => 'اسم المستخدم أو كلمة المرور غير صحيحة']);
        return;
    }
    
    // التحقق من حالة المستخدم
    if (!$user['is_active']) {
        recordFailedLogin($username, $clientIP, 'account_disabled');
        http_response_code(403);
        echo json_encode(['error' => 'الحساب غير نشط']);
        return;
    }
    
    // التحقق من صلاحية الاشتراك
    $subscriptionCheck = checkSubscriptionValidity($user);
    if (!$subscriptionCheck['valid']) {
        recordFailedLogin($username, $clientIP, 'subscription_expired');
        http_response_code(403);
        echo json_encode(['error' => $subscriptionCheck['message']]);
        return;
    }
    
    // التحقق من المصادقة الثنائية
    if ($user['two_factor_enabled']) {
        if (empty($twoFactorCode)) {
            echo json_encode([
                'requiresTwoFactor' => true,
                'userId' => $user['id']
            ]);
            return;
        }
        
        if (!verifyTwoFactorCode($user, $twoFactorCode)) {
            recordFailedLogin($username, $clientIP, 'invalid_2fa');
            http_response_code(401);
            echo json_encode(['error' => 'رمز المصادقة الثنائية غير صحيح']);
            return;
        }
    }
    
    // التحقق من الجلسات المتزامنة
    $maxSessions = getSecuritySetting('max_concurrent_sessions', 3);
    if (getActiveSessionsCount($user['id']) >= $maxSessions) {
        http_response_code(429);
        echo json_encode(['error' => "تم الوصول للحد الأقصى من الجلسات المتزامنة ($maxSessions)"]);
        return;
    }
    
    // إنشاء الجلسة
    $session = createUserSession($user, $rememberMe, $clientIP, $userAgent);
    
    // تسجيل النشاط
    logUserActivity($user['id'], 'login', 'success', 'تسجيل دخول ناجح', $session['id']);
    
    // تحديث آخر دخول
    updateLastLogin($user['id'], $clientIP, $userAgent);
    
    // إعادة تعيين محاولات تسجيل الدخول
    resetFailedLogins($username, $clientIP);
    
    // إرسال الاستجابة
    echo json_encode([
        'success' => true,
        'user' => [
            'id' => $user['id'],
            'username' => $user['username'],
            'email' => $user['email'],
            'full_name' => $user['full_name'],
            'role' => $user['role'],
            'permissions' => getUserPermissions($user['id']),
            'subscription_end_date' => $user['subscription_end_date']
        ],
        'session' => [
            'token' => $session['token'],
            'expires_at' => $session['expires_at']
        ],
        'subscription_warning' => $subscriptionCheck['warning'] ?? null
    ]);
}

/**
 * معالجة تسجيل الخروج
 */
function handleLogout($input) {
    $token = $input['token'] ?? '';
    
    if (empty($token)) {
        http_response_code(400);
        echo json_encode(['error' => 'رمز الجلسة مطلوب']);
        return;
    }
    
    // البحث عن الجلسة
    $session = findSessionByToken($token);
    if (!$session) {
        http_response_code(404);
        echo json_encode(['error' => 'جلسة غير صحيحة']);
        return;
    }
    
    // إنهاء الجلسة
    terminateSession($session['id'], 'manual');
    
    // تسجيل النشاط
    logUserActivity($session['user_id'], 'logout', 'success', 'تسجيل خروج', $session['id']);
    
    echo json_encode(['success' => true, 'message' => 'تم تسجيل الخروج بنجاح']);
}

/**
 * معالجة تجديد الرمز المميز
 */
function handleRefreshToken($input) {
    $token = $input['token'] ?? '';
    
    if (empty($token)) {
        http_response_code(400);
        echo json_encode(['error' => 'رمز الجلسة مطلوب']);
        return;
    }
    
    // البحث عن الجلسة
    $session = findSessionByToken($token);
    if (!$session || !$session['is_active']) {
        http_response_code(401);
        echo json_encode(['error' => 'جلسة غير صحيحة أو منتهية']);
        return;
    }
    
    // التحقق من انتهاء الجلسة
    if (strtotime($session['expires_at']) <= time()) {
        terminateSession($session['id'], 'expired');
        http_response_code(401);
        echo json_encode(['error' => 'انتهت صلاحية الجلسة']);
        return;
    }
    
    // تجديد الجلسة
    $newToken = refreshSession($session['id']);
    
    echo json_encode([
        'success' => true,
        'token' => $newToken,
        'expires_at' => date('Y-m-d H:i:s', time() + SESSION_TIMEOUT)
    ]);
}

/**
 * معالجة التحقق من الجلسة
 */
function handleVerifySession($input) {
    $token = $input['token'] ?? '';
    
    if (empty($token)) {
        http_response_code(400);
        echo json_encode(['error' => 'رمز الجلسة مطلوب']);
        return;
    }
    
    // التحقق من الجلسة
    $session = validateSession($token);
    if (!$session) {
        http_response_code(401);
        echo json_encode(['valid' => false, 'error' => 'جلسة غير صحيحة']);
        return;
    }
    
    // الحصول على بيانات المستخدم
    $user = findUserById($session['user_id']);
    if (!$user || !$user['is_active']) {
        http_response_code(401);
        echo json_encode(['valid' => false, 'error' => 'مستخدم غير صحيح']);
        return;
    }
    
    // التحقق من صلاحية الاشتراك
    $subscriptionCheck = checkSubscriptionValidity($user);
    if (!$subscriptionCheck['valid']) {
        http_response_code(403);
        echo json_encode(['valid' => false, 'error' => $subscriptionCheck['message']]);
        return;
    }
    
    echo json_encode([
        'valid' => true,
        'user' => [
            'id' => $user['id'],
            'username' => $user['username'],
            'email' => $user['email'],
            'full_name' => $user['full_name'],
            'role' => $user['role'],
            'permissions' => getUserPermissions($user['id'])
        ],
        'subscription_warning' => $subscriptionCheck['warning'] ?? null
    ]);
}

/**
 * معالجة تغيير كلمة المرور
 */
function handleChangePassword($input) {
    $token = $input['token'] ?? '';
    $currentPassword = $input['currentPassword'] ?? '';
    $newPassword = $input['newPassword'] ?? '';
    
    if (empty($token) || empty($currentPassword) || empty($newPassword)) {
        http_response_code(400);
        echo json_encode(['error' => 'جميع الحقول مطلوبة']);
        return;
    }
    
    // التحقق من الجلسة
    $session = validateSession($token);
    if (!$session) {
        http_response_code(401);
        echo json_encode(['error' => 'جلسة غير صحيحة']);
        return;
    }
    
    // الحصول على بيانات المستخدم
    $user = findUserById($session['user_id']);
    if (!$user) {
        http_response_code(404);
        echo json_encode(['error' => 'مستخدم غير موجود']);
        return;
    }
    
    // التحقق من كلمة المرور الحالية
    if (!verifyPassword($currentPassword, $user['password_hash'])) {
        http_response_code(400);
        echo json_encode(['error' => 'كلمة المرور الحالية غير صحيحة']);
        return;
    }
    
    // التحقق من قوة كلمة المرور الجديدة
    $passwordValidation = validatePassword($newPassword);
    if (!$passwordValidation['valid']) {
        http_response_code(400);
        echo json_encode(['error' => $passwordValidation['message']]);
        return;
    }
    
    // تحديث كلمة المرور
    $newPasswordHash = hashPassword($newPassword);
    updateUserPassword($user['id'], $newPasswordHash);
    
    // تسجيل النشاط
    logUserActivity($user['id'], 'password_change', 'success', 'تغيير كلمة المرور', $session['id']);
    
    echo json_encode(['success' => true, 'message' => 'تم تغيير كلمة المرور بنجاح']);
}
?>
