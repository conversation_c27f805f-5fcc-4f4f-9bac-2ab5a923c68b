<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قمة الوعد للسفريات - نظام إدارة متكامل</title>
    
    <!-- External CSS Libraries -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700;800&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="assets/css/variables.css">
    <link rel="stylesheet" href="assets/css/base.css">
    <link rel="stylesheet" href="assets/css/components.css">
    <link rel="stylesheet" href="assets/css/layout.css">
    <link rel="stylesheet" href="assets/css/pages.css">
    <link rel="stylesheet" href="assets/css/responsive.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="assets/images/favicon.ico">
</head>
<body>
    <!-- Loading Screen -->
    <div id="loading-screen" class="loading-screen">
        <div class="loading-content">
            <div class="loading-logo">
                <i class="fas fa-plane"></i>
            </div>
            <div class="loading-text">
                <h3>قمة الوعد للسفريات</h3>
                <p>جاري تحميل النظام...</p>
            </div>
            <div class="loading-spinner">
                <div class="spinner"></div>
            </div>
        </div>
    </div>

    <!-- Navigation Bar -->
    <nav class="navbar navbar-expand-xl navbar-dark" id="main-navbar">
        <div class="container-fluid">
            <!-- Brand -->
            <a class="navbar-brand" href="#" onclick="showDashboard()">
                <div class="brand-content">
                    <i class="fas fa-plane brand-icon"></i>
                    <div class="brand-text">
                        <span class="brand-name">قمة الوعد</span>
                        <span class="brand-subtitle">للسفريات</span>
                    </div>
                </div>
            </a>

            <!-- Mobile Toggle -->
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>

            <!-- Navigation Menu -->
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <!-- Dashboard -->
                    <li class="nav-item">
                        <a class="nav-link active" href="#" onclick="showDashboard()">
                            <i class="fas fa-home"></i>
                            <span class="nav-text">الرئيسية</span>
                        </a>
                    </li>

                    <!-- Customers -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-users"></i>
                            <span class="nav-text">العملاء</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="showCustomers()">
                                <i class="fas fa-list me-2"></i>قائمة العملاء
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="showAddCustomer()">
                                <i class="fas fa-user-plus me-2"></i>إضافة عميل جديد
                            </a></li>
                        </ul>
                    </li>

                    <!-- Suppliers -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-truck"></i>
                            <span class="nav-text">الموردين</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="showSuppliers()">
                                <i class="fas fa-list me-2"></i>قائمة الموردين
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="showAddSupplier()">
                                <i class="fas fa-plus me-2"></i>إضافة مورد جديد
                            </a></li>
                        </ul>
                    </li>

                    <!-- Agents -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-handshake"></i>
                            <span class="nav-text">الوكلاء</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="showAgents()">
                                <i class="fas fa-list me-2"></i>قائمة الوكلاء
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="showAddAgent()">
                                <i class="fas fa-user-plus me-2"></i>إضافة وكيل جديد
                            </a></li>
                        </ul>
                    </li>

                    <!-- Bookings -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-calendar-check"></i>
                            <span class="nav-text">الحجوزات</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="showBookings()">
                                <i class="fas fa-list me-2"></i>قائمة الحجوزات
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="showAddBooking()">
                                <i class="fas fa-plus me-2"></i>حجز جديد
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="showHajjUmrah()">
                                <i class="fas fa-kaaba me-2"></i>حج وعمرة
                            </a></li>
                        </ul>
                    </li>

                    <!-- Inventory -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-boxes"></i>
                            <span class="nav-text">المخزون</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="showVisaInventory()">
                                <i class="fas fa-passport me-2"></i>مخزون التأشيرات
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="showInventory()">
                                <i class="fas fa-warehouse me-2"></i>المخزون العام
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="showProducts()">
                                <i class="fas fa-tags me-2"></i>المنتجات والخدمات
                            </a></li>
                        </ul>
                    </li>

                    <!-- Accounting -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-calculator"></i>
                            <span class="nav-text">الحسابات</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="showAccounts()">
                                <i class="fas fa-book me-2"></i>دليل الحسابات
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="showJournalEntries()">
                                <i class="fas fa-edit me-2"></i>القيود المحاسبية
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="showAgentsAndSuppliersAccounts()">
                                <i class="fas fa-users-cog me-2"></i>حسابات الوكلاء والموردين
                            </a></li>
                        </ul>
                    </li>

                    <!-- Reports -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-chart-bar"></i>
                            <span class="nav-text">التقارير</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="showCustomerReports()">
                                <i class="fas fa-users me-2"></i>تقارير العملاء
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="showAgentReports()">
                                <i class="fas fa-handshake me-2"></i>تقارير الوكلاء
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="showTransactionReports()">
                                <i class="fas fa-exchange-alt me-2"></i>تقارير المعاملات
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="showFinancialReports()">
                                <i class="fas fa-chart-line me-2"></i>التقارير المالية
                            </a></li>
                        </ul>
                    </li>

                    <!-- Settings -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-cog"></i>
                            <span class="nav-text">الإعدادات</span>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="showUserManagement()">
                                <i class="fas fa-users-cog me-2"></i>إدارة المستخدمين
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="showBackupSettings()">
                                <i class="fas fa-database me-2"></i>النسخ الاحتياطية
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="showSystemSettings()">
                                <i class="fas fa-sliders-h me-2"></i>الإعدادات العامة
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="showTransportCompanies()">
                                <i class="fas fa-bus me-2"></i>شركات النقل
                            </a></li>
                        </ul>
                    </li>
                </ul>

                <!-- User Menu -->
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle user-menu" href="#" role="button" data-bs-toggle="dropdown">
                            <div class="user-info">
                                <i class="fas fa-user-circle me-2"></i>
                                <span id="current-user-name">مدير النظام</span>
                            </div>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="#" onclick="showUserProfile()">
                                <i class="fas fa-user me-2"></i>الملف الشخصي
                            </a></li>
                            <li><a class="dropdown-item" href="#" onclick="showSettings()">
                                <i class="fas fa-cog me-2"></i>الإعدادات
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item text-danger" href="#" onclick="logout()">
                                <i class="fas fa-sign-out-alt me-2"></i>تسجيل الخروج
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content Container -->
    <main class="main-content" id="main-content">
        <!-- Content will be loaded here dynamically -->
    </main>

    <!-- Footer -->
    <footer class="main-footer">
        <div class="container-fluid">
            <div class="footer-content">
                <div class="footer-left">
                    <p>&copy; 2024 قمة الوعد للسفريات. جميع الحقوق محفوظة.</p>
                </div>
                <div class="footer-right">
                    <p>الإصدار 2.0.0</p>
                </div>
            </div>
        </div>
    </footer>

    <!-- External JavaScript Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JavaScript -->
    <script src="js/core/database.js"></script>
    <script src="js/core/app.js"></script>
    <script src="js/core/router.js"></script>
    <script src="js/core/auth.js"></script>
    <script src="js/utils/helpers.js"></script>
    <script src="js/utils/validation.js"></script>
    <script src="js/utils/notifications.js"></script>
    <script src="js/components/dashboard.js"></script>
    <script src="js/components/customers.js"></script>
    <script src="js/components/suppliers.js"></script>
    <script src="js/components/agents.js"></script>
    <script src="js/components/bookings.js"></script>
    <script src="js/components/inventory.js"></script>
    <script src="js/components/accounting.js"></script>
    <script src="js/components/reports.js"></script>
    <script src="js/components/settings.js"></script>

    <!-- Advanced System Components -->
    <script src="js/utils/encryption.js"></script>
    <script src="js/utils/ui-advanced.js"></script>
    <script src="js/core/app-advanced.js"></script>
    <script src="js/components/dashboard-advanced.js"></script>
    <script src="js/components/customers-advanced.js"></script>
    <script src="js/components/bookings-advanced.js"></script>
    <script src="js/components/user-management.js"></script>
    <script src="js/system-update.js"></script>

    <!-- Initialize App -->
    <script>
        // Initialize the application when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚀 بدء تهيئة النظام...');

            // تهيئة النظام الأساسي
            if (window.App) {
                App.init();
                console.log('✅ تم تهيئة النظام الأساسي');
            }

            // تهيئة واجهة المستخدم المتقدمة
            if (window.AdvancedUI) {
                AdvancedUI.init();
                console.log('✅ تم تهيئة واجهة المستخدم المتقدمة');
            }

            // تهيئة التطبيق المتقدم
            if (window.AdvancedApp) {
                AdvancedApp.init();
                console.log('✅ تم تهيئة التطبيق المتقدم');
            }

            // تهيئة فحص النظام
            if (window.SystemUpdate) {
                setTimeout(() => {
                    SystemUpdate.checkAndFixSystem();
                    SystemUpdate.fixInputAndEditIssues();
                    console.log('✅ تم فحص النظام وإصلاح المشاكل');
                }, 500);
            }

            console.log('🎉 تم تهيئة النظام بنجاح');
        });

        // معالجة الأخطاء العامة
        window.addEventListener('error', function(e) {
            console.error('خطأ في النظام:', e.error);

            // إظهار رسالة خطأ للمستخدم
            if (window.AdvancedApp && window.AdvancedApp.showNotification) {
                AdvancedApp.showNotification('حدث خطأ في النظام. يرجى إعادة تحميل الصفحة.', 'error');
            } else if (window.App && window.App.showNotification) {
                App.showNotification('حدث خطأ في النظام. يرجى إعادة تحميل الصفحة.', 'error');
            }
        });

        // معالجة الوعود المرفوضة
        window.addEventListener('unhandledrejection', function(e) {
            console.error('وعد مرفوض:', e.reason);
            e.preventDefault();
        });

        // إضافة وظائف التنقل العامة
        window.showDashboard = function() {
            if (window.AdvancedApp && window.AdvancedApp.showDashboard) {
                window.AdvancedApp.showDashboard();
            } else if (window.AdvancedDashboard && window.AdvancedDashboard.show) {
                window.AdvancedDashboard.show();
            } else if (window.App && window.App.showDashboard) {
                window.App.showDashboard();
            } else {
                console.warn('وظيفة عرض لوحة التحكم غير متاحة');
            }
        };

        window.showCustomers = function() {
            if (window.AdvancedApp && window.AdvancedApp.showCustomers) {
                window.AdvancedApp.showCustomers();
            } else if (window.AdvancedCustomers && window.AdvancedCustomers.show) {
                window.AdvancedCustomers.show();
            } else if (window.App && window.App.showCustomers) {
                window.App.showCustomers();
            } else {
                console.warn('وظيفة عرض العملاء غير متاحة');
            }
        };

        window.showBookings = function() {
            if (window.AdvancedApp && window.AdvancedApp.showBookings) {
                window.AdvancedApp.showBookings();
            } else if (window.AdvancedBookings && window.AdvancedBookings.show) {
                window.AdvancedBookings.show();
            } else if (window.App && window.App.showBookings) {
                window.App.showBookings();
            } else {
                console.warn('وظيفة عرض الحجوزات غير متاحة');
            }
        };

        window.showUsers = function() {
            if (window.AdvancedApp && window.AdvancedApp.showUsers) {
                window.AdvancedApp.showUsers();
            } else if (window.UserManagement && window.UserManagement.show) {
                window.UserManagement.show();
            } else {
                console.warn('وظيفة عرض المستخدمين غير متاحة');
            }
        };
    </script>
</body>
</html>
