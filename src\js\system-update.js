/**
 * ===================================
 * تحديث النظام وإصلاح المشاكل
 * قمة الوعد للسفريات
 * ===================================
 */

window.SystemUpdate = {
    /**
     * فحص النظام بالكامل وإصلاح المشاكل
     */
    checkAndFixSystem: function() {
        console.log('🔍 بدء فحص النظام الشامل...');
        
        try {
            // 1. فحص الملفات المطلوبة
            this.checkRequiredFiles();
            
            // 2. فحص المكونات
            this.checkComponents();
            
            // 3. إصلاح الوظائف المفقودة
            this.fixMissingFunctions();
            
            // 4. تحديث الأحداث
            this.updateEvents();
            
            // 5. إصلاح النماذج
            this.fixForms();
            
            // 6. تحديث التنقل
            this.updateNavigation();
            
            console.log('✅ تم فحص النظام وإصلاح المشاكل بنجاح');
            
        } catch (error) {
            console.error('❌ خطأ في فحص النظام:', error);
        }
    },

    /**
     * فحص الملفات المطلوبة
     */
    checkRequiredFiles: function() {
        const requiredFiles = [
            'src/js/core/app-advanced.js',
            'src/js/components/dashboard-advanced.js',
            'src/js/components/customers-advanced.js',
            'src/js/components/bookings-advanced.js',
            'src/js/components/user-management.js',
            'src/js/utils/ui-advanced.js',
            'src/js/utils/encryption.js'
        ];

        requiredFiles.forEach(file => {
            if (!this.isFileLoaded(file)) {
                console.warn(`⚠️ الملف مفقود: ${file}`);
            }
        });
    },

    /**
     * التحقق من تحميل الملف
     */
    isFileLoaded: function(filename) {
        const scripts = document.querySelectorAll('script[src]');
        return Array.from(scripts).some(script => script.src.includes(filename));
    },

    /**
     * فحص المكونات
     */
    checkComponents: function() {
        const components = [
            'AdvancedApp',
            'AdvancedDashboard',
            'AdvancedCustomers',
            'AdvancedBookings',
            'UserManagement',
            'AdvancedUI',
            'Encryption'
        ];

        components.forEach(component => {
            if (!window[component]) {
                console.warn(`⚠️ المكون مفقود: ${component}`);
                this.createPlaceholderComponent(component);
            } else {
                console.log(`✅ المكون متاح: ${component}`);
            }
        });
    },

    /**
     * إنشاء مكون بديل
     */
    createPlaceholderComponent: function(componentName) {
        window[componentName] = {
            init: function() {
                console.log(`تهيئة المكون البديل: ${componentName}`);
            },
            show: function() {
                console.log(`عرض المكون البديل: ${componentName}`);
                const content = `
                    <div class="alert alert-warning text-center">
                        <h4>المكون غير متاح</h4>
                        <p>المكون ${componentName} غير محمل بشكل صحيح.</p>
                        <button class="btn btn-primary" onclick="location.reload()">
                            إعادة تحميل الصفحة
                        </button>
                    </div>
                `;
                
                const mainContent = document.getElementById('main-content');
                if (mainContent) {
                    mainContent.innerHTML = content;
                }
            }
        };
    },

    /**
     * إصلاح الوظائف المفقودة
     */
    fixMissingFunctions: function() {
        // إصلاح وظائف العملاء
        this.fixCustomersFunctions();
        
        // إصلاح وظائف الحجوزات
        this.fixBookingsFunctions();
        
        // إصلاح وظائف المستخدمين
        this.fixUsersFunctions();
        
        // إصلاح وظائف لوحة التحكم
        this.fixDashboardFunctions();
    },

    /**
     * إصلاح وظائف العملاء
     */
    fixCustomersFunctions: function() {
        if (window.AdvancedCustomers) {
            // إضافة الوظائف المفقودة
            if (!window.AdvancedCustomers.renderCustomerModal) {
                window.AdvancedCustomers.renderCustomerModal = function() {
                    return '';
                };
            }

            if (!window.AdvancedCustomers.bulkAction) {
                window.AdvancedCustomers.bulkAction = function(action) {
                    const selectedCustomers = this.state.selectedCustomers || [];
                    if (selectedCustomers.length === 0) {
                        alert('يرجى تحديد عملاء للتنفيذ');
                        return;
                    }
                    
                    if (confirm(`هل أنت متأكد من تنفيذ هذا الإجراء على ${selectedCustomers.length} عميل؟`)) {
                        console.log(`تنفيذ الإجراء ${action} على العملاء:`, selectedCustomers);
                        this.showNotification('تم تنفيذ الإجراء بنجاح', 'success');
                    }
                };
            }
        }
    },

    /**
     * إصلاح وظائف الحجوزات
     */
    fixBookingsFunctions: function() {
        if (window.AdvancedBookings) {
            // إضافة الوظائف المفقودة
            if (!window.AdvancedBookings.renderBookingModals) {
                window.AdvancedBookings.renderBookingModals = function() {
                    return '';
                };
            }

            if (!window.AdvancedBookings.clearFilters) {
                window.AdvancedBookings.clearFilters = function() {
                    this.state.filters = {
                        search: '',
                        type: '',
                        status: '',
                        paymentStatus: '',
                        dateFrom: '',
                        dateTo: '',
                        customer: '',
                        agent: ''
                    };

                    // مسح قيم الحقول
                    const inputs = ['searchInput', 'typeFilter', 'statusFilter', 'paymentStatusFilter', 'dateFromFilter', 'dateToFilter'];
                    inputs.forEach(id => {
                        const element = document.getElementById(id);
                        if (element) element.value = '';
                    });

                    this.applyFilters();
                    this.renderBookings();
                };
            }
        }
    },

    /**
     * إصلاح وظائف المستخدمين
     */
    fixUsersFunctions: function() {
        if (window.UserManagement) {
            // إضافة الوظائف المفقودة
            if (!window.UserManagement.renderUserModals) {
                window.UserManagement.renderUserModals = function() {
                    return '';
                };
            }

            if (!window.UserManagement.showUserModal) {
                window.UserManagement.showUserModal = function(user = null, mode = 'add') {
                    const modalId = 'userModal';
                    const isEdit = mode === 'edit';
                    const isView = mode === 'view';
                    
                    const modalHTML = `
                        <div class="modal fade" id="${modalId}" tabindex="-1">
                            <div class="modal-dialog modal-lg">
                                <div class="modal-content">
                                    <div class="modal-header">
                                        <h5 class="modal-title">
                                            <i class="fas fa-user me-2"></i>
                                            ${isView ? 'عرض المستخدم' : isEdit ? 'تعديل المستخدم' : 'إضافة مستخدم جديد'}
                                        </h5>
                                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                                    </div>
                                    <div class="modal-body">
                                        <form id="userForm">
                                            <div class="row">
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">الاسم الكامل *</label>
                                                    <input type="text" class="form-control" id="fullName" 
                                                           value="${user ? user.fullName : ''}" ${isView ? 'readonly' : ''} required>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">اسم المستخدم *</label>
                                                    <input type="text" class="form-control" id="username" 
                                                           value="${user ? user.username : ''}" ${isView ? 'readonly' : ''} required>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">البريد الإلكتروني *</label>
                                                    <input type="email" class="form-control" id="email" 
                                                           value="${user ? user.email : ''}" ${isView ? 'readonly' : ''} required>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">رقم الهاتف</label>
                                                    <input type="tel" class="form-control" id="phone" 
                                                           value="${user ? user.phone : ''}" ${isView ? 'readonly' : ''}>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">الدور *</label>
                                                    <select class="form-select" id="role" ${isView ? 'disabled' : ''} required>
                                                        ${Object.entries(this.config.roles).map(([key, value]) => 
                                                            `<option value="${key}" ${user && user.role === key ? 'selected' : ''}>${value}</option>`
                                                        ).join('')}
                                                    </select>
                                                </div>
                                                <div class="col-md-6 mb-3">
                                                    <label class="form-label">الحالة</label>
                                                    <select class="form-select" id="status" ${isView ? 'disabled' : ''}>
                                                        <option value="active" ${user && user.status === 'active' ? 'selected' : ''}>نشط</option>
                                                        <option value="inactive" ${user && user.status === 'inactive' ? 'selected' : ''}>غير نشط</option>
                                                        <option value="locked" ${user && user.status === 'locked' ? 'selected' : ''}>مقفل</option>
                                                    </select>
                                                </div>
                                                ${!isView && !isEdit ? `
                                                    <div class="col-md-6 mb-3">
                                                        <label class="form-label">كلمة المرور *</label>
                                                        <input type="password" class="form-control" id="password" required>
                                                    </div>
                                                    <div class="col-md-6 mb-3">
                                                        <label class="form-label">تأكيد كلمة المرور *</label>
                                                        <input type="password" class="form-control" id="confirmPassword" required>
                                                    </div>
                                                ` : ''}
                                            </div>
                                        </form>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                            ${isView ? 'إغلاق' : 'إلغاء'}
                                        </button>
                                        ${!isView ? `
                                            <button type="button" class="btn btn-primary" onclick="UserManagement.saveUser(${user ? user.id : 'null'})">
                                                <i class="fas fa-save me-2"></i>
                                                ${isEdit ? 'تحديث' : 'حفظ'}
                                            </button>
                                        ` : ''}
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;

                    // إزالة النافذة السابقة إن وجدت
                    const existingModal = document.getElementById(modalId);
                    if (existingModal) {
                        existingModal.remove();
                    }

                    // إضافة النافذة الجديدة
                    document.body.insertAdjacentHTML('beforeend', modalHTML);

                    // إظهار النافذة
                    const modal = new bootstrap.Modal(document.getElementById(modalId));
                    modal.show();

                    // إزالة النافذة عند الإغلاق
                    document.getElementById(modalId).addEventListener('hidden.bs.modal', function() {
                        this.remove();
                    });
                };
            }

            if (!window.UserManagement.saveUser) {
                window.UserManagement.saveUser = function(userId = null) {
                    const form = document.getElementById('userForm');
                    if (!form.checkValidity()) {
                        form.reportValidity();
                        return;
                    }

                    const userData = {
                        fullName: document.getElementById('fullName').value.trim(),
                        username: document.getElementById('username').value.trim(),
                        email: document.getElementById('email').value.trim(),
                        phone: document.getElementById('phone').value.trim(),
                        role: document.getElementById('role').value,
                        status: document.getElementById('status').value
                    };

                    if (userId) {
                        // تحديث مستخدم موجود
                        const userIndex = this.state.users.findIndex(u => u.id === userId);
                        if (userIndex !== -1) {
                            this.state.users[userIndex] = {
                                ...this.state.users[userIndex],
                                ...userData
                            };
                            this.showNotification('تم تحديث بيانات المستخدم بنجاح', 'success');
                        }
                    } else {
                        // إضافة مستخدم جديد
                        const newUser = {
                            id: Date.now(),
                            ...userData,
                            createdAt: new Date(),
                            lastActivity: new Date(),
                            loginAttempts: 0,
                            isOnline: false,
                            avatar: `https://ui-avatars.com/api/?name=${encodeURIComponent(userData.fullName)}&background=2c5aa0&color=fff`
                        };
                        this.state.users.push(newUser);
                        this.showNotification('تم إضافة المستخدم بنجاح', 'success');
                    }

                    // تحديث العرض
                    this.applyFilters();
                    this.updateStatistics();
                    this.renderUsers();

                    // إغلاق النافذة
                    const modal = bootstrap.Modal.getInstance(document.getElementById('userModal'));
                    modal.hide();
                };
            }
        }
    },

    /**
     * إصلاح وظائف لوحة التحكم
     */
    fixDashboardFunctions: function() {
        if (window.AdvancedDashboard) {
            // إضافة الوظائف المفقودة
            if (!window.AdvancedDashboard.charts) {
                window.AdvancedDashboard.charts = {};
            }

            if (!window.AdvancedDashboard.initCharts) {
                window.AdvancedDashboard.initCharts = function() {
                    console.log('تهيئة الرسوم البيانية...');
                    // يمكن إضافة تهيئة الرسوم البيانية هنا
                };
            }
        }
    },

    /**
     * تحديث الأحداث
     */
    updateEvents: function() {
        // إضافة أحداث عامة للنظام
        document.addEventListener('DOMContentLoaded', function() {
            // تهيئة التلميحات
            const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            tooltipTriggerList.map(function (tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // تهيئة القوائم المنسدلة
            const dropdownElementList = [].slice.call(document.querySelectorAll('.dropdown-toggle'));
            dropdownElementList.map(function (dropdownToggleEl) {
                return new bootstrap.Dropdown(dropdownToggleEl);
            });
        });
    },

    /**
     * إصلاح النماذج
     */
    fixForms: function() {
        // إضافة التحقق من صحة النماذج
        const forms = document.querySelectorAll('form');
        forms.forEach(form => {
            form.addEventListener('submit', function(e) {
                if (!form.checkValidity()) {
                    e.preventDefault();
                    e.stopPropagation();
                }
                form.classList.add('was-validated');
            });
        });
    },

    /**
     * تحديث التنقل
     */
    updateNavigation: function() {
        // إضافة وظائف التنقل العامة
        window.showDashboard = function() {
            if (window.AdvancedApp && window.AdvancedApp.showDashboard) {
                window.AdvancedApp.showDashboard();
            } else if (window.AdvancedDashboard && window.AdvancedDashboard.show) {
                window.AdvancedDashboard.show();
            } else {
                console.warn('وظيفة عرض لوحة التحكم غير متاحة');
            }
        };

        window.showCustomers = function() {
            if (window.AdvancedApp && window.AdvancedApp.showCustomers) {
                window.AdvancedApp.showCustomers();
            } else if (window.AdvancedCustomers && window.AdvancedCustomers.show) {
                window.AdvancedCustomers.show();
            } else {
                console.warn('وظيفة عرض العملاء غير متاحة');
            }
        };

        window.showBookings = function() {
            if (window.AdvancedApp && window.AdvancedApp.showBookings) {
                window.AdvancedApp.showBookings();
            } else if (window.AdvancedBookings && window.AdvancedBookings.show) {
                window.AdvancedBookings.show();
            } else {
                console.warn('وظيفة عرض الحجوزات غير متاحة');
            }
        };

        window.showUsers = function() {
            if (window.AdvancedApp && window.AdvancedApp.showUsers) {
                window.AdvancedApp.showUsers();
            } else if (window.UserManagement && window.UserManagement.show) {
                window.UserManagement.show();
            } else {
                console.warn('وظيفة عرض المستخدمين غير متاحة');
            }
        };
    },

    /**
     * إصلاح مشاكل الإدخال والتعديل
     */
    fixInputAndEditIssues: function() {
        console.log('🔧 إصلاح مشاكل الإدخال والتعديل...');
        
        // إصلاح مشاكل النماذج
        this.fixFormIssues();
        
        // إصلاح مشاكل التحقق
        this.fixValidationIssues();
        
        // إصلاح مشاكل الحفظ
        this.fixSaveIssues();
        
        console.log('✅ تم إصلاح مشاكل الإدخال والتعديل');
    },

    /**
     * إصلاح مشاكل النماذج
     */
    fixFormIssues: function() {
        // إضافة معالجات الأحداث للنماذج
        document.addEventListener('change', function(e) {
            if (e.target.matches('input, select, textarea')) {
                // إزالة رسائل الخطأ عند التغيير
                e.target.classList.remove('is-invalid');
                const feedback = e.target.parentNode.querySelector('.invalid-feedback');
                if (feedback) {
                    feedback.remove();
                }
            }
        });
    },

    /**
     * إصلاح مشاكل التحقق
     */
    fixValidationIssues: function() {
        // إضافة التحقق المخصص
        window.validateForm = function(formId) {
            const form = document.getElementById(formId);
            if (!form) return false;

            const inputs = form.querySelectorAll('input[required], select[required], textarea[required]');
            let isValid = true;

            inputs.forEach(input => {
                if (!input.value.trim()) {
                    input.classList.add('is-invalid');
                    isValid = false;
                } else {
                    input.classList.remove('is-invalid');
                }
            });

            return isValid;
        };
    },

    /**
     * إصلاح مشاكل الحفظ
     */
    fixSaveIssues: function() {
        // إضافة وظيفة حفظ عامة
        window.saveData = function(data, type) {
            try {
                localStorage.setItem(`${type}_data`, JSON.stringify(data));
                console.log(`تم حفظ بيانات ${type} بنجاح`);
                return true;
            } catch (error) {
                console.error(`خطأ في حفظ بيانات ${type}:`, error);
                return false;
            }
        };

        // إضافة وظيفة تحميل عامة
        window.loadData = function(type) {
            try {
                const data = localStorage.getItem(`${type}_data`);
                return data ? JSON.parse(data) : null;
            } catch (error) {
                console.error(`خطأ في تحميل بيانات ${type}:`, error);
                return null;
            }
        };
    }
};

// تشغيل فحص النظام عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    setTimeout(() => {
        window.SystemUpdate.checkAndFixSystem();
        window.SystemUpdate.fixInputAndEditIssues();
    }, 1000);
});
