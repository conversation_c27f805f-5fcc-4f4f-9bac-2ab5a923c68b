<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - قمة الوعد للسفريات</title>
    
    <!-- CSS Files -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <link href="src/css/dashboard-advanced.css" rel="stylesheet">
    
    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            max-width: 1000px;
            width: 100%;
            margin: 20px;
        }

        .login-left {
            background: linear-gradient(135deg, #2c5aa0 0%, #1e3c72 100%);
            color: white;
            padding: 3rem;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .login-left::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
            animation: float 20s infinite linear;
        }

        @keyframes float {
            0% { transform: translateX(0) translateY(0); }
            100% { transform: translateX(-50px) translateY(-50px); }
        }

        .login-logo {
            font-size: 3rem;
            margin-bottom: 1rem;
            position: relative;
            z-index: 2;
        }

        .login-title {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 1rem;
            position: relative;
            z-index: 2;
        }

        .login-subtitle {
            font-size: 1.1rem;
            opacity: 0.9;
            position: relative;
            z-index: 2;
        }

        .login-right {
            padding: 3rem;
        }

        .form-floating {
            margin-bottom: 1.5rem;
        }

        .form-control {
            border: 2px solid #e3e6f0;
            border-radius: 10px;
            padding: 1rem;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #2c5aa0;
            box-shadow: 0 0 0 0.2rem rgba(44, 90, 160, 0.25);
        }

        .btn-login {
            background: linear-gradient(135deg, #2c5aa0 0%, #1e3c72 100%);
            border: none;
            border-radius: 10px;
            padding: 1rem 2rem;
            font-size: 1.1rem;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-login::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-login:hover::before {
            left: 100%;
        }

        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(44, 90, 160, 0.3);
        }

        .login-options {
            text-align: center;
            margin-top: 2rem;
        }

        .divider {
            position: relative;
            text-align: center;
            margin: 2rem 0;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e3e6f0;
        }

        .divider span {
            background: white;
            padding: 0 1rem;
            color: #6c757d;
        }

        .social-login {
            display: flex;
            gap: 1rem;
            justify-content: center;
        }

        .social-btn {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            border: 2px solid #e3e6f0;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .social-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .alert-enhanced {
            border: none;
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 1.5rem;
        }

        .loading-spinner {
            display: none;
            margin-right: 0.5rem;
        }

        .password-toggle {
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #6c757d;
            cursor: pointer;
            z-index: 10;
        }

        .features-list {
            list-style: none;
            padding: 0;
            margin-top: 2rem;
        }

        .features-list li {
            padding: 0.5rem 0;
            position: relative;
            z-index: 2;
        }

        .features-list li::before {
            content: '✓';
            color: #28a745;
            font-weight: bold;
            margin-left: 0.5rem;
        }

        @media (max-width: 768px) {
            .login-container {
                margin: 10px;
            }
            
            .login-left {
                padding: 2rem;
            }
            
            .login-right {
                padding: 2rem;
            }
            
            .login-title {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="row g-0 h-100">
            <!-- Left Side - Branding -->
            <div class="col-lg-6 login-left">
                <div class="login-logo">
                    <i class="fas fa-plane"></i>
                </div>
                <h1 class="login-title">قمة الوعد للسفريات</h1>
                <p class="login-subtitle">نظام إدارة شامل ومتطور للسفريات والسياحة</p>
                
                <ul class="features-list">
                    <li>إدارة متقدمة للحجوزات والعملاء</li>
                    <li>نظام محاسبي شامل ومتكامل</li>
                    <li>تقارير تفصيلية وتحليلات ذكية</li>
                    <li>أمان عالي وحماية البيانات</li>
                    <li>واجهة سهلة الاستخدام</li>
                </ul>
            </div>
            
            <!-- Right Side - Login Form -->
            <div class="col-lg-6 login-right">
                <div class="text-center mb-4">
                    <h2>تسجيل الدخول</h2>
                    <p class="text-muted">أدخل بياناتك للوصول إلى النظام</p>
                </div>

                <!-- Alert Messages -->
                <div id="alertContainer"></div>

                <!-- Login Form -->
                <form id="loginForm">
                    <div class="form-floating">
                        <input type="text" class="form-control" id="username" placeholder="اسم المستخدم" required>
                        <label for="username">
                            <i class="fas fa-user me-2"></i>اسم المستخدم
                        </label>
                    </div>

                    <div class="form-floating position-relative">
                        <input type="password" class="form-control" id="password" placeholder="كلمة المرور" required>
                        <label for="password">
                            <i class="fas fa-lock me-2"></i>كلمة المرور
                        </label>
                        <button type="button" class="password-toggle" onclick="togglePassword()">
                            <i class="fas fa-eye" id="passwordToggleIcon"></i>
                        </button>
                    </div>

                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="rememberMe">
                            <label class="form-check-label" for="rememberMe">
                                تذكرني
                            </label>
                        </div>
                        <a href="#" class="text-decoration-none" onclick="showForgotPassword()">
                            نسيت كلمة المرور؟
                        </a>
                    </div>

                    <button type="submit" class="btn btn-login">
                        <span class="loading-spinner">
                            <i class="fas fa-spinner fa-spin"></i>
                        </span>
                        <span class="btn-text">تسجيل الدخول</span>
                    </button>
                </form>

                <!-- Divider -->
                <div class="divider">
                    <span>أو</span>
                </div>

                <!-- Quick Login Options -->
                <div class="login-options">
                    <p class="text-muted mb-3">تسجيل دخول سريع:</p>
                    <div class="row g-2">
                        <div class="col-6">
                            <button class="btn btn-outline-primary w-100" onclick="quickLogin('admin')">
                                <i class="fas fa-user-shield me-2"></i>مدير
                            </button>
                        </div>
                        <div class="col-6">
                            <button class="btn btn-outline-success w-100" onclick="quickLogin('employee')">
                                <i class="fas fa-user me-2"></i>موظف
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Footer -->
                <div class="text-center mt-4">
                    <small class="text-muted">
                        © 2024 قمة الوعد للسفريات. جميع الحقوق محفوظة.
                    </small>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript Files -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="src/js/core/database.js"></script>
    <script src="src/js/core/auth.js"></script>
    <script src="src/js/utils/encryption.js"></script>

    <script>
        // متغيرات النظام
        let loginAttempts = 0;
        const maxAttempts = 5;
        const lockoutTime = 15 * 60 * 1000; // 15 دقيقة

        // تهيئة الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            // التحقق من حالة القفل
            checkLockoutStatus();
            
            // تهيئة النظام
            initializeSystem();
            
            // تهيئة الأحداث
            initializeEvents();
        });

        /**
         * تهيئة النظام
         */
        function initializeSystem() {
            try {
                // تهيئة قاعدة البيانات
                if (window.Database) {
                    window.Database.init();
                }

                // تهيئة نظام المصادقة
                if (window.Auth) {
                    window.Auth.init();
                }

                // تهيئة نظام التشفير
                if (window.Encryption) {
                    window.Encryption.init();
                }

                console.log('✅ تم تهيئة النظام بنجاح');
            } catch (error) {
                console.error('❌ خطأ في تهيئة النظام:', error);
                showAlert('خطأ في تهيئة النظام', 'danger');
            }
        }

        /**
         * تهيئة الأحداث
         */
        function initializeEvents() {
            // حدث إرسال النموذج
            document.getElementById('loginForm').addEventListener('submit', handleLogin);

            // أحداث لوحة المفاتيح
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    handleLogin(e);
                }
            });

            // تنظيف الرسائل عند الكتابة
            document.getElementById('username').addEventListener('input', clearAlerts);
            document.getElementById('password').addEventListener('input', clearAlerts);
        }

        /**
         * معالجة تسجيل الدخول
         */
        async function handleLogin(e) {
            e.preventDefault();

            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value;
            const rememberMe = document.getElementById('rememberMe').checked;

            // التحقق من البيانات
            if (!username || !password) {
                showAlert('يرجى إدخال اسم المستخدم وكلمة المرور', 'warning');
                return;
            }

            // إظهار مؤشر التحميل
            showLoading(true);

            try {
                // محاولة تسجيل الدخول
                const result = await authenticateUser(username, password);

                if (result.success) {
                    // نجح تسجيل الدخول
                    showAlert('تم تسجيل الدخول بنجاح', 'success');
                    
                    // حفظ بيانات المستخدم
                    saveUserSession(result.user, rememberMe);
                    
                    // إعادة توجيه
                    setTimeout(() => {
                        window.location.href = 'index.html';
                    }, 1000);

                } else {
                    // فشل تسجيل الدخول
                    loginAttempts++;
                    
                    if (loginAttempts >= maxAttempts) {
                        lockAccount();
                    } else {
                        const remaining = maxAttempts - loginAttempts;
                        showAlert(`بيانات خاطئة. المحاولات المتبقية: ${remaining}`, 'danger');
                    }
                }

            } catch (error) {
                console.error('خطأ في تسجيل الدخول:', error);
                showAlert('حدث خطأ في النظام. يرجى المحاولة لاحقاً', 'danger');
            } finally {
                showLoading(false);
            }
        }

        /**
         * مصادقة المستخدم
         */
        async function authenticateUser(username, password) {
            // محاكاة عملية المصادقة
            return new Promise((resolve) => {
                setTimeout(() => {
                    // بيانات المستخدمين الافتراضية
                    const users = [
                        { username: 'admin', password: 'admin123', role: 'admin', fullName: 'مدير النظام' },
                        { username: 'employee', password: 'emp123', role: 'employee', fullName: 'موظف' },
                        { username: 'accountant', password: 'acc123', role: 'accountant', fullName: 'محاسب' }
                    ];

                    const user = users.find(u => u.username === username && u.password === password);
                    
                    if (user) {
                        resolve({
                            success: true,
                            user: {
                                id: Date.now(),
                                username: user.username,
                                fullName: user.fullName,
                                role: user.role,
                                loginTime: new Date().toISOString()
                            }
                        });
                    } else {
                        resolve({ success: false });
                    }
                }, 1500);
            });
        }

        /**
         * حفظ جلسة المستخدم
         */
        function saveUserSession(user, rememberMe) {
            localStorage.setItem('isLoggedIn', 'true');
            localStorage.setItem('currentUser', JSON.stringify(user));
            
            if (rememberMe) {
                localStorage.setItem('rememberUser', user.username);
            }
        }

        /**
         * تسجيل دخول سريع
         */
        function quickLogin(userType) {
            const credentials = {
                admin: { username: 'admin', password: 'admin123' },
                employee: { username: 'employee', password: 'emp123' }
            };

            if (credentials[userType]) {
                document.getElementById('username').value = credentials[userType].username;
                document.getElementById('password').value = credentials[userType].password;
                document.getElementById('loginForm').dispatchEvent(new Event('submit'));
            }
        }

        /**
         * تبديل إظهار كلمة المرور
         */
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleIcon = document.getElementById('passwordToggleIcon');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.className = 'fas fa-eye-slash';
            } else {
                passwordInput.type = 'password';
                toggleIcon.className = 'fas fa-eye';
            }
        }

        /**
         * إظهار/إخفاء مؤشر التحميل
         */
        function showLoading(show) {
            const spinner = document.querySelector('.loading-spinner');
            const btnText = document.querySelector('.btn-text');
            const submitBtn = document.querySelector('.btn-login');

            if (show) {
                spinner.style.display = 'inline-block';
                btnText.textContent = 'جاري تسجيل الدخول...';
                submitBtn.disabled = true;
            } else {
                spinner.style.display = 'none';
                btnText.textContent = 'تسجيل الدخول';
                submitBtn.disabled = false;
            }
        }

        /**
         * إظهار رسالة تنبيه
         */
        function showAlert(message, type) {
            const alertContainer = document.getElementById('alertContainer');
            const alertHTML = `
                <div class="alert alert-${type} alert-enhanced alert-dismissible fade show" role="alert">
                    <i class="fas fa-${getAlertIcon(type)} me-2"></i>
                    ${message}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            `;
            alertContainer.innerHTML = alertHTML;

            // إزالة التنبيه تلقائياً بعد 5 ثوان
            setTimeout(() => {
                const alert = alertContainer.querySelector('.alert');
                if (alert) {
                    alert.remove();
                }
            }, 5000);
        }

        /**
         * مسح التنبيهات
         */
        function clearAlerts() {
            const alertContainer = document.getElementById('alertContainer');
            alertContainer.innerHTML = '';
        }

        /**
         * الحصول على أيقونة التنبيه
         */
        function getAlertIcon(type) {
            const icons = {
                success: 'check-circle',
                danger: 'exclamation-circle',
                warning: 'exclamation-triangle',
                info: 'info-circle'
            };
            return icons[type] || 'info-circle';
        }

        /**
         * قفل الحساب
         */
        function lockAccount() {
            const lockTime = Date.now() + lockoutTime;
            localStorage.setItem('accountLocked', lockTime);
            
            showAlert(`تم قفل الحساب لمدة 15 دقيقة بسبب المحاولات الخاطئة المتكررة`, 'danger');
            
            // تعطيل النموذج
            document.getElementById('loginForm').style.pointerEvents = 'none';
            document.getElementById('loginForm').style.opacity = '0.5';
        }

        /**
         * التحقق من حالة القفل
         */
        function checkLockoutStatus() {
            const lockTime = localStorage.getItem('accountLocked');
            
            if (lockTime && Date.now() < parseInt(lockTime)) {
                const remainingTime = Math.ceil((parseInt(lockTime) - Date.now()) / 60000);
                showAlert(`الحساب مقفل. المدة المتبقية: ${remainingTime} دقيقة`, 'warning');
                
                // تعطيل النموذج
                document.getElementById('loginForm').style.pointerEvents = 'none';
                document.getElementById('loginForm').style.opacity = '0.5';
                
                // إعادة تفعيل النموذج عند انتهاء القفل
                setTimeout(() => {
                    localStorage.removeItem('accountLocked');
                    location.reload();
                }, parseInt(lockTime) - Date.now());
            }
        }

        /**
         * إظهار نافذة نسيان كلمة المرور
         */
        function showForgotPassword() {
            showAlert('يرجى التواصل مع مدير النظام لإعادة تعيين كلمة المرور', 'info');
        }
    </script>
</body>
</html>
