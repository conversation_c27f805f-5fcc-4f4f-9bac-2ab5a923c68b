<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قمة الوعد للسفريات - نظام إدارة شامل</title>
    <meta name="description" content="نظام إدارة شامل لوكالات السفر والسياحة مع نظام محاسبي متطور وإدارة المستخدمين">
    <meta name="keywords" content="نظام سفريات, نظام محاسبي, إدارة وكالة سفر, حجوزات, تذاكر طيران">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    <!-- AOS Animation -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2c5aa0;
            --secondary-color: #f8f9fa;
            --accent-color: #28a745;
            --text-dark: #2c3e50;
            --text-light: #6c757d;
        }

        * {
            font-family: 'Cairo', sans-serif;
        }

        body {
            overflow-x: hidden;
        }

        .hero-section {
            background: linear-gradient(135deg, var(--primary-color) 0%, #1e3c72 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="white" opacity="0.1"><polygon points="1000,100 1000,0 0,100"/></svg>');
            background-size: cover;
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .feature-card {
            background: white;
            border-radius: 15px;
            padding: 2rem;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
            height: 100%;
        }

        .feature-card:hover {
            transform: translateY(-10px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }

        .feature-icon {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1.5rem;
            color: white;
            font-size: 2rem;
        }

        .section-title {
            position: relative;
            display: inline-block;
            margin-bottom: 3rem;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 4px;
            background: var(--accent-color);
            border-radius: 2px;
        }

        .stats-section {
            background: var(--secondary-color);
            padding: 5rem 0;
        }

        .stat-item {
            text-align: center;
            padding: 2rem;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 900;
            color: var(--primary-color);
            display: block;
        }

        .pricing-card {
            background: white;
            border-radius: 20px;
            padding: 3rem 2rem;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .pricing-card.featured {
            transform: scale(1.05);
            border: 3px solid var(--accent-color);
        }

        .pricing-card.featured::before {
            content: 'الأكثر شعبية';
            position: absolute;
            top: 20px;
            right: -30px;
            background: var(--accent-color);
            color: white;
            padding: 5px 40px;
            transform: rotate(45deg);
            font-size: 0.8rem;
            font-weight: 600;
        }

        .price {
            font-size: 3rem;
            font-weight: 900;
            color: var(--primary-color);
        }

        .btn-primary-custom {
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            border: none;
            padding: 12px 30px;
            border-radius: 50px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }

        .navbar {
            background: rgba(255,255,255,0.95) !important;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 20px rgba(0,0,0,0.1);
        }

        .footer {
            background: var(--text-dark);
            color: white;
            padding: 3rem 0 1rem;
        }

        .demo-video {
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }

        @media (max-width: 768px) {
            .hero-section {
                min-height: 80vh;
            }
            
            .pricing-card.featured {
                transform: none;
                margin-top: 2rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container">
            <a class="navbar-brand text-primary" href="#">
                <i class="fas fa-plane me-2"></i>
                قمة الوعد للسفريات
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#features">المميزات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#demo">العرض التوضيحي</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#pricing">الأسعار</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#contact">تواصل معنا</a>
                    </li>
                </ul>
                <div class="d-flex gap-2">
                    <a href="login.html" class="btn btn-outline-primary">تسجيل الدخول</a>
                    <a href="#pricing" class="btn btn-primary-custom">ابدأ الآن</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section text-white">
        <div class="container">
            <div class="row align-items-center">
                <div class="col-lg-6 hero-content" data-aos="fade-right">
                    <h1 class="display-4 fw-bold mb-4">
                        نظام إدارة شامل لوكالات السفر والسياحة
                    </h1>
                    <p class="lead mb-4">
                        نظام متطور وآمن لإدارة جميع عمليات وكالة السفر مع نظام محاسبي متكامل 
                        وإدارة المستخدمين بصلاحيات محددة بالأيام
                    </p>
                    <div class="d-flex gap-3 flex-wrap">
                        <a href="#demo" class="btn btn-light btn-lg px-4">
                            <i class="fas fa-play me-2"></i>
                            شاهد العرض التوضيحي
                        </a>
                        <a href="login.html" class="btn btn-outline-light btn-lg px-4">
                            <i class="fas fa-rocket me-2"></i>
                            جرب النظام مجاناً
                        </a>
                    </div>
                </div>
                <div class="col-lg-6" data-aos="fade-left">
                    <div class="text-center">
                        <img src="https://via.placeholder.com/600x400/2c5aa0/ffffff?text=نظام+قمة+الوعد" 
                             alt="نظام قمة الوعد" class="img-fluid rounded shadow-lg">
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="stats-section">
        <div class="container">
            <div class="row">
                <div class="col-md-3 col-6" data-aos="fade-up" data-aos-delay="100">
                    <div class="stat-item">
                        <span class="stat-number" data-count="500">0</span>
                        <h5>وكالة سفر</h5>
                        <p class="text-muted">تستخدم النظام</p>
                    </div>
                </div>
                <div class="col-md-3 col-6" data-aos="fade-up" data-aos-delay="200">
                    <div class="stat-item">
                        <span class="stat-number" data-count="50000">0</span>
                        <h5>حجز شهرياً</h5>
                        <p class="text-muted">يتم معالجتها</p>
                    </div>
                </div>
                <div class="col-md-3 col-6" data-aos="fade-up" data-aos-delay="300">
                    <div class="stat-item">
                        <span class="stat-number" data-count="99">0</span>
                        <h5>% وقت التشغيل</h5>
                        <p class="text-muted">موثوقية عالية</p>
                    </div>
                </div>
                <div class="col-md-3 col-6" data-aos="fade-up" data-aos-delay="400">
                    <div class="stat-item">
                        <span class="stat-number" data-count="24">0</span>
                        <h5>ساعة دعم</h5>
                        <p class="text-muted">على مدار الساعة</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="section-title display-5 fw-bold text-dark" data-aos="fade-up">
                        مميزات النظام
                    </h2>
                    <p class="lead text-muted" data-aos="fade-up" data-aos-delay="100">
                        نظام شامل ومتطور لإدارة جميع عمليات وكالة السفر
                    </p>
                </div>
            </div>
            <div class="row g-4">
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="100">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-users-cog"></i>
                        </div>
                        <h4 class="text-center mb-3">إدارة المستخدمين المتقدمة</h4>
                        <p class="text-muted text-center">
                            نظام متطور لإدارة المستخدمين مع صلاحيات محددة بالأيام وحماية متعددة المستويات
                        </p>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>صلاحيات محددة بالأيام</li>
                            <li><i class="fas fa-check text-success me-2"></i>مصادقة ثنائية</li>
                            <li><i class="fas fa-check text-success me-2"></i>تتبع النشاط</li>
                            <li><i class="fas fa-check text-success me-2"></i>إدارة الجلسات</li>
                        </ul>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="200">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-calculator"></i>
                        </div>
                        <h4 class="text-center mb-3">نظام محاسبي متكامل</h4>
                        <p class="text-muted text-center">
                            نظام محاسبي شامل مع دليل حسابات وقيود محاسبية وتقارير مالية متقدمة
                        </p>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>دليل حسابات شامل</li>
                            <li><i class="fas fa-check text-success me-2"></i>قيود محاسبية تلقائية</li>
                            <li><i class="fas fa-check text-success me-2"></i>تقارير مالية</li>
                            <li><i class="fas fa-check text-success me-2"></i>متعدد العملات</li>
                        </ul>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="300">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-plane"></i>
                        </div>
                        <h4 class="text-center mb-3">إدارة الحجوزات</h4>
                        <p class="text-muted text-center">
                            نظام متطور لإدارة جميع أنواع الحجوزات من تذاكر طيران وفنادق وباقات سياحية
                        </p>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>حجوزات الطيران</li>
                            <li><i class="fas fa-check text-success me-2"></i>حجوزات الفنادق</li>
                            <li><i class="fas fa-check text-success me-2"></i>باقات الحج والعمرة</li>
                            <li><i class="fas fa-check text-success me-2"></i>تتبع الحالة</li>
                        </ul>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="400">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h4 class="text-center mb-3">أمان متقدم</h4>
                        <p class="text-muted text-center">
                            حماية متعددة المستويات مع تشفير البيانات وحماية من الهجمات السيبرانية
                        </p>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>تشفير البيانات</li>
                            <li><i class="fas fa-check text-success me-2"></i>حماية من الهجمات</li>
                            <li><i class="fas fa-check text-success me-2"></i>نسخ احتياطية</li>
                            <li><i class="fas fa-check text-success me-2"></i>مراقبة الأمان</li>
                        </ul>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="500">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h4 class="text-center mb-3">تقارير ذكية</h4>
                        <p class="text-muted text-center">
                            تقارير تفصيلية وتحليلات ذكية لمساعدتك في اتخاذ القرارات الصحيحة
                        </p>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>تقارير مالية</li>
                            <li><i class="fas fa-check text-success me-2"></i>تحليل الأداء</li>
                            <li><i class="fas fa-check text-success me-2"></i>إحصائيات العملاء</li>
                            <li><i class="fas fa-check text-success me-2"></i>تصدير متعدد</li>
                        </ul>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="600">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <h4 class="text-center mb-3">متجاوب مع الأجهزة</h4>
                        <p class="text-muted text-center">
                            يعمل بسلاسة على جميع الأجهزة من الكمبيوتر إلى الهاتف المحمول
                        </p>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>تصميم متجاوب</li>
                            <li><i class="fas fa-check text-success me-2"></i>سرعة عالية</li>
                            <li><i class="fas fa-check text-success me-2"></i>واجهة سهلة</li>
                            <li><i class="fas fa-check text-success me-2"></i>دعم اللغة العربية</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Demo Section -->
    <section id="demo" class="py-5 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="section-title display-5 fw-bold text-dark" data-aos="fade-up">
                        شاهد النظام في العمل
                    </h2>
                    <p class="lead text-muted" data-aos="fade-up" data-aos-delay="100">
                        جرب النظام بنفسك واكتشف سهولة الاستخدام والمميزات المتقدمة
                    </p>
                </div>
            </div>
            <div class="row align-items-center">
                <div class="col-lg-6" data-aos="fade-right">
                    <div class="demo-video">
                        <img src="https://via.placeholder.com/600x400/f8f9fa/2c5aa0?text=عرض+توضيحي+للنظام"
                             alt="عرض توضيحي" class="img-fluid rounded">
                        <div class="position-absolute top-50 start-50 translate-middle">
                            <button class="btn btn-primary btn-lg rounded-circle" style="width: 80px; height: 80px;">
                                <i class="fas fa-play fa-2x"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6" data-aos="fade-left">
                    <h3 class="mb-4">اكتشف قوة النظام</h3>
                    <div class="row g-3">
                        <div class="col-12">
                            <div class="d-flex align-items-center">
                                <div class="bg-primary text-white rounded-circle me-3 d-flex align-items-center justify-content-center"
                                     style="width: 50px; height: 50px;">
                                    <i class="fas fa-user-shield"></i>
                                </div>
                                <div>
                                    <h5 class="mb-1">تسجيل دخول آمن</h5>
                                    <p class="text-muted mb-0">مع مصادقة ثنائية وحماية متقدمة</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="d-flex align-items-center">
                                <div class="bg-success text-white rounded-circle me-3 d-flex align-items-center justify-content-center"
                                     style="width: 50px; height: 50px;">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div>
                                    <h5 class="mb-1">صلاحيات محددة بالوقت</h5>
                                    <p class="text-muted mb-0">تحكم كامل في مدة استخدام كل مستخدم</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="d-flex align-items-center">
                                <div class="bg-info text-white rounded-circle me-3 d-flex align-items-center justify-content-center"
                                     style="width: 50px; height: 50px;">
                                    <i class="fas fa-chart-bar"></i>
                                </div>
                                <div>
                                    <h5 class="mb-1">تقارير فورية</h5>
                                    <p class="text-muted mb-0">احصل على تقارير مفصلة في ثوانٍ</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mt-4">
                        <a href="login.html" class="btn btn-primary-custom btn-lg">
                            <i class="fas fa-rocket me-2"></i>
                            جرب النظام الآن
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
        // Initialize AOS
        AOS.init({
            duration: 1000,
            once: true
        });

        // Animate counters
        function animateCounters() {
            const counters = document.querySelectorAll('.stat-number');
            counters.forEach(counter => {
                const target = parseInt(counter.getAttribute('data-count'));
                const increment = target / 100;
                let current = 0;

                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }
                    counter.textContent = Math.floor(current);
                }, 20);
            });
        }

        // Trigger counter animation when stats section is visible
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateCounters();
                    observer.unobserve(entry.target);
                }
            });
        });

        observer.observe(document.querySelector('.stats-section'));
    </script>
</body>
</html>
