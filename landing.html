<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>قمة الوعد للسفريات - نظام إدارة شامل</title>
    <meta name="description" content="نظام إدارة شامل لوكالات السفر والسياحة مع نظام محاسبي متطور وإدارة المستخدمين">
    <meta name="keywords" content="نظام سفريات, نظام محاسبي, إدارة وكالة سفر, حجوزات, تذاكر طيران">
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700;900&display=swap" rel="stylesheet">
    <!-- AOS Animation -->
    <link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
    <!-- Advanced Landing Styles -->
    <link href="styles/landing-advanced.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-color: #2c5aa0;
            --primary-dark: #1e3c72;
            --secondary-color: #f8f9fa;
            --accent-color: #28a745;
            --accent-light: #34ce57;
            --text-dark: #2c3e50;
            --text-light: #6c757d;
            --white: #ffffff;
            --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%);
            --gradient-accent: linear-gradient(135deg, var(--accent-color) 0%, var(--accent-light) 100%);
            --shadow-light: 0 5px 15px rgba(0,0,0,0.08);
            --shadow-medium: 0 10px 30px rgba(0,0,0,0.12);
            --shadow-heavy: 0 20px 40px rgba(0,0,0,0.15);
            --border-radius: 15px;
            --transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        * {
            font-family: 'Cairo', sans-serif;
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            overflow-x: hidden;
            line-height: 1.6;
            color: var(--text-dark);
        }

        .hero-section {
            background: var(--gradient-primary);
            min-height: 100vh;
            display: flex;
            align-items: center;
            position: relative;
            overflow: hidden;
        }

        .hero-section::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="white" opacity="0.1"><polygon points="1000,100 1000,0 0,100"/></svg>');
            background-size: cover;
        }

        .hero-section::after {
            content: '';
            position: absolute;
            top: -50%;
            right: -20%;
            width: 100%;
            height: 200%;
            background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(5deg); }
        }

        .hero-content {
            position: relative;
            z-index: 2;
        }

        .hero-particles {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }

        .particle {
            position: absolute;
            background: rgba(255,255,255,0.1);
            border-radius: 50%;
            animation: particle-float 8s infinite linear;
        }

        @keyframes particle-float {
            0% { transform: translateY(100vh) rotate(0deg); opacity: 0; }
            10% { opacity: 1; }
            90% { opacity: 1; }
            100% { transform: translateY(-100px) rotate(360deg); opacity: 0; }
        }

        .feature-card {
            background: var(--white);
            border-radius: var(--border-radius);
            padding: 2.5rem;
            box-shadow: var(--shadow-light);
            transition: var(--transition);
            height: 100%;
            border: 1px solid rgba(0,0,0,0.05);
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 4px;
            background: var(--gradient-accent);
            transform: scaleX(0);
            transition: var(--transition);
        }

        .feature-card:hover {
            transform: translateY(-15px);
            box-shadow: var(--shadow-heavy);
        }

        .feature-card:hover::before {
            transform: scaleX(1);
        }

        .feature-icon {
            width: 90px;
            height: 90px;
            background: var(--gradient-primary);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 2rem;
            color: white;
            font-size: 2.2rem;
            position: relative;
            box-shadow: var(--shadow-medium);
        }

        .feature-icon::after {
            content: '';
            position: absolute;
            top: -2px;
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: var(--gradient-accent);
            border-radius: 50%;
            z-index: -1;
            opacity: 0;
            transition: var(--transition);
        }

        .feature-card:hover .feature-icon::after {
            opacity: 1;
        }

        .section-title {
            position: relative;
            display: inline-block;
            margin-bottom: 3rem;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 60px;
            height: 4px;
            background: var(--accent-color);
            border-radius: 2px;
        }

        .stats-section {
            background: var(--secondary-color);
            padding: 5rem 0;
        }

        .stat-item {
            text-align: center;
            padding: 2rem;
        }

        .stat-number {
            font-size: 3rem;
            font-weight: 900;
            color: var(--primary-color);
            display: block;
        }

        .pricing-card {
            background: white;
            border-radius: 20px;
            padding: 3rem 2rem;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .pricing-card.featured {
            transform: scale(1.05);
            border: 3px solid var(--accent-color);
        }

        .pricing-card.featured::before {
            content: 'الأكثر شعبية';
            position: absolute;
            top: 20px;
            right: -30px;
            background: var(--accent-color);
            color: white;
            padding: 5px 40px;
            transform: rotate(45deg);
            font-size: 0.8rem;
            font-weight: 600;
        }

        .price {
            font-size: 3rem;
            font-weight: 900;
            color: var(--primary-color);
        }

        .btn-primary-custom {
            background: linear-gradient(135deg, var(--primary-color), var(--accent-color));
            border: none;
            padding: 12px 30px;
            border-radius: 50px;
            font-weight: 600;
            transition: all 0.3s ease;
        }

        .btn-primary-custom:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(0,0,0,0.2);
        }

        .navbar-brand {
            font-weight: 700;
            font-size: 1.5rem;
        }

        .navbar {
            background: rgba(255,255,255,0.95) !important;
            backdrop-filter: blur(20px);
            box-shadow: var(--shadow-light);
            transition: var(--transition);
            padding: 1rem 0;
        }

        .navbar.scrolled {
            background: rgba(255,255,255,0.98) !important;
            box-shadow: var(--shadow-medium);
            padding: 0.5rem 0;
        }

        .navbar-nav .nav-link {
            font-weight: 500;
            color: var(--text-dark) !important;
            margin: 0 0.5rem;
            padding: 0.5rem 1rem !important;
            border-radius: 25px;
            transition: var(--transition);
        }

        .navbar-nav .nav-link:hover {
            background: var(--secondary-color);
            color: var(--primary-color) !important;
        }

        .footer {
            background: var(--text-dark);
            color: white;
            padding: 3rem 0 1rem;
        }

        .demo-video {
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.2);
        }

        /* Dashboard Preview Styles */
        .dashboard-preview {
            position: relative;
            z-index: 2;
        }

        .browser-mockup {
            background: var(--white);
            border-radius: 15px;
            box-shadow: var(--shadow-heavy);
            overflow: hidden;
            transform: perspective(1000px) rotateY(-5deg) rotateX(5deg);
            transition: var(--transition);
        }

        .browser-mockup:hover {
            transform: perspective(1000px) rotateY(0deg) rotateX(0deg);
        }

        .browser-header {
            background: #f1f3f4;
            padding: 12px 20px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .browser-buttons {
            display: flex;
            gap: 8px;
        }

        .btn-close-mockup,
        .btn-minimize-mockup,
        .btn-maximize-mockup {
            width: 12px;
            height: 12px;
            border-radius: 50%;
        }

        .btn-close-mockup { background: #ff5f57; }
        .btn-minimize-mockup { background: #ffbd2e; }
        .btn-maximize-mockup { background: #28ca42; }

        .browser-url {
            background: var(--white);
            padding: 6px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            color: var(--text-light);
            flex: 1;
        }

        .browser-content {
            background: var(--secondary-color);
            min-height: 300px;
        }

        .floating-element {
            position: absolute;
            animation: float-gentle 3s ease-in-out infinite;
        }

        @keyframes float-gentle {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
        }

        .floating-element:nth-child(2) { animation-delay: 1s; }
        .floating-element:nth-child(3) { animation-delay: 2s; }

        /* Enhanced Sections */
        .section-divider {
            height: 100px;
            background: linear-gradient(to right, transparent, var(--primary-color), transparent);
            margin: 0;
            opacity: 0.1;
        }

        .testimonial-card {
            background: var(--white);
            border-radius: var(--border-radius);
            padding: 2rem;
            box-shadow: var(--shadow-light);
            transition: var(--transition);
            text-align: center;
            position: relative;
        }

        .testimonial-card::before {
            content: '"';
            position: absolute;
            top: -20px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 4rem;
            color: var(--primary-color);
            opacity: 0.3;
        }

        .testimonial-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-medium);
        }

        .client-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin: 0 auto 1rem;
            background: var(--gradient-primary);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 1.5rem;
            font-weight: bold;
        }

        .pricing-table {
            background: var(--white);
            border-radius: var(--border-radius);
            padding: 2.5rem;
            box-shadow: var(--shadow-light);
            transition: var(--transition);
            position: relative;
            overflow: hidden;
        }

        .pricing-table.featured {
            transform: scale(1.05);
            border: 3px solid var(--accent-color);
            box-shadow: var(--shadow-heavy);
        }

        .pricing-table.featured::before {
            content: 'الأكثر شعبية';
            position: absolute;
            top: 20px;
            right: -30px;
            background: var(--accent-color);
            color: white;
            padding: 8px 40px;
            transform: rotate(45deg);
            font-size: 0.8rem;
            font-weight: 600;
        }

        .price-tag {
            font-size: 3.5rem;
            font-weight: 900;
            color: var(--primary-color);
            line-height: 1;
        }

        .price-currency {
            font-size: 1.2rem;
            vertical-align: top;
        }

        .price-period {
            font-size: 1rem;
            color: var(--text-light);
            font-weight: normal;
        }

        /* Contact Section */
        .contact-card {
            background: var(--white);
            border-radius: var(--border-radius);
            padding: 2rem;
            box-shadow: var(--shadow-light);
            transition: var(--transition);
            text-align: center;
        }

        .contact-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-medium);
        }

        .contact-icon {
            width: 70px;
            height: 70px;
            background: var(--gradient-accent);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 1rem;
            color: white;
            font-size: 1.8rem;
        }

        @media (max-width: 768px) {
            .hero-section {
                min-height: 80vh;
            }

            .pricing-table.featured {
                transform: none;
                margin-top: 2rem;
            }

            .browser-mockup {
                transform: none;
            }

            .floating-element {
                display: none;
            }

            .display-3 {
                font-size: 2.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-light fixed-top">
        <div class="container">
            <a class="navbar-brand text-primary" href="#">
                <i class="fas fa-plane me-2"></i>
                قمة الوعد للسفريات
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="#features">المميزات</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#demo">العرض التوضيحي</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#pricing">الأسعار</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#contact">تواصل معنا</a>
                    </li>
                </ul>
                <div class="d-flex gap-2">
                    <a href="login.html" class="btn btn-outline-primary">تسجيل الدخول</a>
                    <a href="#pricing" class="btn btn-primary-custom">ابدأ الآن</a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero-section text-white">
        <!-- Animated Particles -->
        <div class="hero-particles">
            <div class="particle" style="left: 10%; width: 4px; height: 4px; animation-delay: 0s;"></div>
            <div class="particle" style="left: 20%; width: 6px; height: 6px; animation-delay: 1s;"></div>
            <div class="particle" style="left: 30%; width: 3px; height: 3px; animation-delay: 2s;"></div>
            <div class="particle" style="left: 40%; width: 5px; height: 5px; animation-delay: 3s;"></div>
            <div class="particle" style="left: 50%; width: 4px; height: 4px; animation-delay: 4s;"></div>
            <div class="particle" style="left: 60%; width: 6px; height: 6px; animation-delay: 5s;"></div>
            <div class="particle" style="left: 70%; width: 3px; height: 3px; animation-delay: 6s;"></div>
            <div class="particle" style="left: 80%; width: 5px; height: 5px; animation-delay: 7s;"></div>
            <div class="particle" style="left: 90%; width: 4px; height: 4px; animation-delay: 8s;"></div>
        </div>

        <div class="container">
            <div class="row align-items-center min-vh-100">
                <div class="col-lg-6 hero-content" data-aos="fade-right">
                    <div class="badge bg-light text-primary mb-3 px-3 py-2">
                        <i class="fas fa-star me-2"></i>
                        النسخة المتقدمة 2.0
                    </div>
                    <h1 class="display-3 fw-bold mb-4 lh-1">
                        نظام إدارة شامل
                        <span class="text-warning">لوكالات السفر</span>
                        والسياحة
                    </h1>
                    <p class="lead mb-4 fs-5">
                        نظام متطور وآمن لإدارة جميع عمليات وكالة السفر مع نظام محاسبي متكامل
                        وإدارة المستخدمين بصلاحيات محددة بالأيام
                    </p>

                    <!-- مميزات سريعة -->
                    <div class="row mb-4">
                        <div class="col-6">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-check-circle text-warning me-2"></i>
                                <span>صلاحيات محددة بالأيام</span>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-check-circle text-warning me-2"></i>
                                <span>أمان متقدم</span>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-check-circle text-warning me-2"></i>
                                <span>نظام محاسبي متكامل</span>
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="d-flex align-items-center mb-2">
                                <i class="fas fa-check-circle text-warning me-2"></i>
                                <span>تقارير ذكية</span>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex gap-3 flex-wrap">
                        <a href="demo-interactive.html" class="btn btn-warning btn-lg px-4 py-3 rounded-pill">
                            <i class="fas fa-play me-2"></i>
                            شاهد العرض التوضيحي
                        </a>
                        <a href="login.html" class="btn btn-outline-light btn-lg px-4 py-3 rounded-pill">
                            <i class="fas fa-rocket me-2"></i>
                            جرب النظام مجاناً
                        </a>
                    </div>

                    <!-- إحصائيات سريعة -->
                    <div class="row mt-5">
                        <div class="col-4 text-center">
                            <h3 class="fw-bold text-warning mb-0">500+</h3>
                            <small class="text-light">وكالة سفر</small>
                        </div>
                        <div class="col-4 text-center">
                            <h3 class="fw-bold text-warning mb-0">50K+</h3>
                            <small class="text-light">حجز شهرياً</small>
                        </div>
                        <div class="col-4 text-center">
                            <h3 class="fw-bold text-warning mb-0">99.9%</h3>
                            <small class="text-light">وقت التشغيل</small>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6" data-aos="fade-left" data-aos-delay="200">
                    <div class="text-center position-relative">
                        <!-- Dashboard Preview -->
                        <div class="dashboard-preview">
                            <div class="browser-mockup">
                                <div class="browser-header">
                                    <div class="browser-buttons">
                                        <span class="btn-close-mockup"></span>
                                        <span class="btn-minimize-mockup"></span>
                                        <span class="btn-maximize-mockup"></span>
                                    </div>
                                    <div class="browser-url">qimat-alwaed.com</div>
                                </div>
                                <div class="browser-content">
                                    <img src="https://via.placeholder.com/600x400/f8f9fa/2c5aa0?text=لوحة+التحكم+الذكية"
                                         alt="نظام قمة الوعد" class="img-fluid">
                                </div>
                            </div>
                        </div>

                        <!-- Floating Elements -->
                        <div class="floating-element" style="top: 10%; right: 10%;" data-aos="fade-up" data-aos-delay="400">
                            <div class="bg-warning text-dark p-3 rounded-3 shadow">
                                <i class="fas fa-users fa-2x mb-2"></i>
                                <h6 class="mb-0">إدارة المستخدمين</h6>
                            </div>
                        </div>

                        <div class="floating-element" style="bottom: 20%; left: 10%;" data-aos="fade-up" data-aos-delay="600">
                            <div class="bg-light text-primary p-3 rounded-3 shadow">
                                <i class="fas fa-chart-line fa-2x mb-2"></i>
                                <h6 class="mb-0">تقارير ذكية</h6>
                            </div>
                        </div>

                        <div class="floating-element" style="top: 50%; right: 5%;" data-aos="fade-up" data-aos-delay="800">
                            <div class="bg-success text-white p-3 rounded-3 shadow">
                                <i class="fas fa-shield-alt fa-2x mb-2"></i>
                                <h6 class="mb-0">أمان متقدم</h6>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Stats Section -->
    <section class="stats-section">
        <div class="container">
            <div class="row">
                <div class="col-md-3 col-6" data-aos="fade-up" data-aos-delay="100">
                    <div class="stat-item">
                        <span class="stat-number" data-count="500">0</span>
                        <h5>وكالة سفر</h5>
                        <p class="text-muted">تستخدم النظام</p>
                    </div>
                </div>
                <div class="col-md-3 col-6" data-aos="fade-up" data-aos-delay="200">
                    <div class="stat-item">
                        <span class="stat-number" data-count="50000">0</span>
                        <h5>حجز شهرياً</h5>
                        <p class="text-muted">يتم معالجتها</p>
                    </div>
                </div>
                <div class="col-md-3 col-6" data-aos="fade-up" data-aos-delay="300">
                    <div class="stat-item">
                        <span class="stat-number" data-count="99">0</span>
                        <h5>% وقت التشغيل</h5>
                        <p class="text-muted">موثوقية عالية</p>
                    </div>
                </div>
                <div class="col-md-3 col-6" data-aos="fade-up" data-aos-delay="400">
                    <div class="stat-item">
                        <span class="stat-number" data-count="24">0</span>
                        <h5>ساعة دعم</h5>
                        <p class="text-muted">على مدار الساعة</p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="section-title display-5 fw-bold text-dark" data-aos="fade-up">
                        مميزات النظام
                    </h2>
                    <p class="lead text-muted" data-aos="fade-up" data-aos-delay="100">
                        نظام شامل ومتطور لإدارة جميع عمليات وكالة السفر
                    </p>
                </div>
            </div>
            <div class="row g-4">
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="100">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-users-cog"></i>
                        </div>
                        <h4 class="text-center mb-3">إدارة المستخدمين المتقدمة</h4>
                        <p class="text-muted text-center">
                            نظام متطور لإدارة المستخدمين مع صلاحيات محددة بالأيام وحماية متعددة المستويات
                        </p>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>صلاحيات محددة بالأيام</li>
                            <li><i class="fas fa-check text-success me-2"></i>مصادقة ثنائية</li>
                            <li><i class="fas fa-check text-success me-2"></i>تتبع النشاط</li>
                            <li><i class="fas fa-check text-success me-2"></i>إدارة الجلسات</li>
                        </ul>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="200">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-calculator"></i>
                        </div>
                        <h4 class="text-center mb-3">نظام محاسبي متكامل</h4>
                        <p class="text-muted text-center">
                            نظام محاسبي شامل مع دليل حسابات وقيود محاسبية وتقارير مالية متقدمة
                        </p>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>دليل حسابات شامل</li>
                            <li><i class="fas fa-check text-success me-2"></i>قيود محاسبية تلقائية</li>
                            <li><i class="fas fa-check text-success me-2"></i>تقارير مالية</li>
                            <li><i class="fas fa-check text-success me-2"></i>متعدد العملات</li>
                        </ul>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="300">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-plane"></i>
                        </div>
                        <h4 class="text-center mb-3">إدارة الحجوزات</h4>
                        <p class="text-muted text-center">
                            نظام متطور لإدارة جميع أنواع الحجوزات من تذاكر طيران وفنادق وباقات سياحية
                        </p>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>حجوزات الطيران</li>
                            <li><i class="fas fa-check text-success me-2"></i>حجوزات الفنادق</li>
                            <li><i class="fas fa-check text-success me-2"></i>باقات الحج والعمرة</li>
                            <li><i class="fas fa-check text-success me-2"></i>تتبع الحالة</li>
                        </ul>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="400">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h4 class="text-center mb-3">أمان متقدم</h4>
                        <p class="text-muted text-center">
                            حماية متعددة المستويات مع تشفير البيانات وحماية من الهجمات السيبرانية
                        </p>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>تشفير البيانات</li>
                            <li><i class="fas fa-check text-success me-2"></i>حماية من الهجمات</li>
                            <li><i class="fas fa-check text-success me-2"></i>نسخ احتياطية</li>
                            <li><i class="fas fa-check text-success me-2"></i>مراقبة الأمان</li>
                        </ul>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="500">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <h4 class="text-center mb-3">تقارير ذكية</h4>
                        <p class="text-muted text-center">
                            تقارير تفصيلية وتحليلات ذكية لمساعدتك في اتخاذ القرارات الصحيحة
                        </p>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>تقارير مالية</li>
                            <li><i class="fas fa-check text-success me-2"></i>تحليل الأداء</li>
                            <li><i class="fas fa-check text-success me-2"></i>إحصائيات العملاء</li>
                            <li><i class="fas fa-check text-success me-2"></i>تصدير متعدد</li>
                        </ul>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="600">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-mobile-alt"></i>
                        </div>
                        <h4 class="text-center mb-3">متجاوب مع الأجهزة</h4>
                        <p class="text-muted text-center">
                            يعمل بسلاسة على جميع الأجهزة من الكمبيوتر إلى الهاتف المحمول
                        </p>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>تصميم متجاوب</li>
                            <li><i class="fas fa-check text-success me-2"></i>سرعة عالية</li>
                            <li><i class="fas fa-check text-success me-2"></i>واجهة سهلة</li>
                            <li><i class="fas fa-check text-success me-2"></i>دعم اللغة العربية</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Demo Section -->
    <section id="demo" class="py-5 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="section-title display-5 fw-bold text-dark" data-aos="fade-up">
                        شاهد النظام في العمل
                    </h2>
                    <p class="lead text-muted" data-aos="fade-up" data-aos-delay="100">
                        جرب النظام بنفسك واكتشف سهولة الاستخدام والمميزات المتقدمة
                    </p>
                </div>
            </div>
            <div class="row align-items-center">
                <div class="col-lg-6" data-aos="fade-right">
                    <div class="demo-video">
                        <img src="https://via.placeholder.com/600x400/f8f9fa/2c5aa0?text=عرض+توضيحي+للنظام"
                             alt="عرض توضيحي" class="img-fluid rounded">
                        <div class="position-absolute top-50 start-50 translate-middle">
                            <button class="btn btn-primary btn-lg rounded-circle" style="width: 80px; height: 80px;">
                                <i class="fas fa-play fa-2x"></i>
                            </button>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6" data-aos="fade-left">
                    <h3 class="mb-4">اكتشف قوة النظام</h3>
                    <div class="row g-3">
                        <div class="col-12">
                            <div class="d-flex align-items-center">
                                <div class="bg-primary text-white rounded-circle me-3 d-flex align-items-center justify-content-center"
                                     style="width: 50px; height: 50px;">
                                    <i class="fas fa-user-shield"></i>
                                </div>
                                <div>
                                    <h5 class="mb-1">تسجيل دخول آمن</h5>
                                    <p class="text-muted mb-0">مع مصادقة ثنائية وحماية متقدمة</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="d-flex align-items-center">
                                <div class="bg-success text-white rounded-circle me-3 d-flex align-items-center justify-content-center"
                                     style="width: 50px; height: 50px;">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <div>
                                    <h5 class="mb-1">صلاحيات محددة بالوقت</h5>
                                    <p class="text-muted mb-0">تحكم كامل في مدة استخدام كل مستخدم</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-12">
                            <div class="d-flex align-items-center">
                                <div class="bg-info text-white rounded-circle me-3 d-flex align-items-center justify-content-center"
                                     style="width: 50px; height: 50px;">
                                    <i class="fas fa-chart-bar"></i>
                                </div>
                                <div>
                                    <h5 class="mb-1">تقارير فورية</h5>
                                    <p class="text-muted mb-0">احصل على تقارير مفصلة في ثوانٍ</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="mt-4">
                        <a href="login.html" class="btn btn-primary-custom btn-lg">
                            <i class="fas fa-rocket me-2"></i>
                            جرب النظام الآن
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section id="pricing" class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="section-title display-5 fw-bold text-dark" data-aos="fade-up">
                        خطط الأسعار
                    </h2>
                    <p class="lead text-muted" data-aos="fade-up" data-aos-delay="100">
                        اختر الخطة المناسبة لحجم وكالتك
                    </p>
                </div>
            </div>
            <div class="row g-4 justify-content-center">
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="100">
                    <div class="pricing-table text-center">
                        <h4 class="mb-3">الخطة الأساسية</h4>
                        <div class="price-tag mb-3">
                            <span class="price-currency">ر.س</span>299
                            <span class="price-period">/شهرياً</span>
                        </div>
                        <ul class="list-unstyled mb-4">
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>حتى 5 مستخدمين</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>1000 حجز شهرياً</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>تقارير أساسية</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>دعم فني</li>
                            <li class="mb-2"><i class="fas fa-times text-muted me-2"></i>مصادقة ثنائية</li>
                        </ul>
                        <button class="btn btn-outline-primary btn-lg w-100">ابدأ الآن</button>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="200">
                    <div class="pricing-table text-center featured">
                        <h4 class="mb-3">الخطة المتقدمة</h4>
                        <div class="price-tag mb-3">
                            <span class="price-currency">ر.س</span>599
                            <span class="price-period">/شهرياً</span>
                        </div>
                        <ul class="list-unstyled mb-4">
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>حتى 20 مستخدم</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>5000 حجز شهرياً</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>تقارير متقدمة</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>دعم فني 24/7</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>مصادقة ثنائية</li>
                        </ul>
                        <button class="btn btn-primary btn-lg w-100">ابدأ الآن</button>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="300">
                    <div class="pricing-table text-center">
                        <h4 class="mb-3">الخطة المؤسسية</h4>
                        <div class="price-tag mb-3">
                            <span class="price-currency">ر.س</span>999
                            <span class="price-period">/شهرياً</span>
                        </div>
                        <ul class="list-unstyled mb-4">
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>مستخدمين غير محدود</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>حجوزات غير محدودة</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>تقارير مخصصة</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>دعم مخصص</li>
                            <li class="mb-2"><i class="fas fa-check text-success me-2"></i>جميع المميزات</li>
                        </ul>
                        <button class="btn btn-outline-primary btn-lg w-100">تواصل معنا</button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Testimonials Section -->
    <section class="py-5 bg-light">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="section-title display-5 fw-bold text-dark" data-aos="fade-up">
                        آراء عملائنا
                    </h2>
                    <p class="lead text-muted" data-aos="fade-up" data-aos-delay="100">
                        ماذا يقول عملاؤنا عن النظام
                    </p>
                </div>
            </div>
            <div class="row g-4">
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="100">
                    <div class="testimonial-card">
                        <div class="client-avatar">أ.م</div>
                        <h5>أحمد محمد</h5>
                        <small class="text-muted">مدير وكالة الرحلات الذهبية</small>
                        <p class="mt-3 text-muted">
                            "النظام غير حياة وكالتنا بالكامل. الآن نستطيع إدارة جميع العمليات بسهولة
                            والتحكم في صلاحيات الموظفين بدقة."
                        </p>
                        <div class="text-warning">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="200">
                    <div class="testimonial-card">
                        <div class="client-avatar">س.ع</div>
                        <h5>سارة عبدالله</h5>
                        <small class="text-muted">مديرة وكالة السفر المتميز</small>
                        <p class="mt-3 text-muted">
                            "الأمان والحماية في هذا النظام لا مثيل لها. نشعر بالأمان التام
                            على بيانات عملائنا وحساباتنا المالية."
                        </p>
                        <div class="text-warning">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="300">
                    <div class="testimonial-card">
                        <div class="client-avatar">م.ح</div>
                        <h5>محمد حسن</h5>
                        <small class="text-muted">مالك مجموعة وكالات الأحلام</small>
                        <p class="mt-3 text-muted">
                            "التقارير والإحصائيات التي يوفرها النظام ساعدتنا في اتخاذ قرارات
                            أفضل وزيادة أرباحنا بنسبة 40%."
                        </p>
                        <div class="text-warning">
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                            <i class="fas fa-star"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Contact Section -->
    <section id="contact" class="py-5">
        <div class="container">
            <div class="row">
                <div class="col-12 text-center mb-5">
                    <h2 class="section-title display-5 fw-bold text-dark" data-aos="fade-up">
                        تواصل معنا
                    </h2>
                    <p class="lead text-muted" data-aos="fade-up" data-aos-delay="100">
                        نحن هنا لمساعدتك في بدء رحلتك مع النظام
                    </p>
                </div>
            </div>
            <div class="row g-4">
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="100">
                    <div class="contact-card">
                        <div class="contact-icon">
                            <i class="fas fa-phone"></i>
                        </div>
                        <h5>اتصل بنا</h5>
                        <p class="text-muted">+966 12 345 6789</p>
                        <p class="text-muted">+966 50 123 4567</p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="200">
                    <div class="contact-card">
                        <div class="contact-icon">
                            <i class="fas fa-envelope"></i>
                        </div>
                        <h5>راسلنا</h5>
                        <p class="text-muted"><EMAIL></p>
                        <p class="text-muted"><EMAIL></p>
                    </div>
                </div>
                <div class="col-lg-4 col-md-6" data-aos="fade-up" data-aos-delay="300">
                    <div class="contact-card">
                        <div class="contact-icon">
                            <i class="fas fa-map-marker-alt"></i>
                        </div>
                        <h5>زورنا</h5>
                        <p class="text-muted">الرياض، المملكة العربية السعودية</p>
                        <p class="text-muted">حي الملك فهد</p>
                    </div>
                </div>
            </div>

            <!-- Contact Form -->
            <div class="row mt-5">
                <div class="col-lg-8 mx-auto">
                    <div class="card shadow-lg border-0" data-aos="fade-up" data-aos-delay="400">
                        <div class="card-body p-5">
                            <h4 class="text-center mb-4">أرسل لنا رسالة</h4>
                            <form>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">الاسم الكامل</label>
                                        <input type="text" class="form-control form-control-lg" required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">البريد الإلكتروني</label>
                                        <input type="email" class="form-control form-control-lg" required>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">رقم الهاتف</label>
                                        <input type="tel" class="form-control form-control-lg">
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">اسم الشركة</label>
                                        <input type="text" class="form-control form-control-lg">
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">الموضوع</label>
                                    <select class="form-select form-select-lg">
                                        <option>استفسار عام</option>
                                        <option>طلب عرض سعر</option>
                                        <option>دعم فني</option>
                                        <option>شراكة</option>
                                    </select>
                                </div>
                                <div class="mb-4">
                                    <label class="form-label">الرسالة</label>
                                    <textarea class="form-control" rows="5" required></textarea>
                                </div>
                                <div class="text-center">
                                    <button type="submit" class="btn btn-primary btn-lg px-5">
                                        <i class="fas fa-paper-plane me-2"></i>
                                        إرسال الرسالة
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="row">
                <div class="col-lg-4 mb-4">
                    <h5 class="text-white mb-3">
                        <i class="fas fa-plane me-2"></i>
                        قمة الوعد للسفريات
                    </h5>
                    <p class="text-light">
                        نظام إدارة شامل ومتطور لوكالات السفر والسياحة مع أحدث تقنيات الأمان والحماية.
                    </p>
                    <div class="social-links">
                        <a href="#" class="text-light me-3"><i class="fab fa-facebook fa-lg"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-twitter fa-lg"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-linkedin fa-lg"></i></a>
                        <a href="#" class="text-light me-3"><i class="fab fa-instagram fa-lg"></i></a>
                    </div>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="text-white mb-3">الروابط السريعة</h6>
                    <ul class="list-unstyled">
                        <li><a href="#features" class="text-light">المميزات</a></li>
                        <li><a href="#demo" class="text-light">العرض التوضيحي</a></li>
                        <li><a href="#pricing" class="text-light">الأسعار</a></li>
                        <li><a href="#contact" class="text-light">تواصل معنا</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="text-white mb-3">الخدمات</h6>
                    <ul class="list-unstyled">
                        <li><a href="#" class="text-light">إدارة الحجوزات</a></li>
                        <li><a href="#" class="text-light">النظام المحاسبي</a></li>
                        <li><a href="#" class="text-light">إدارة المستخدمين</a></li>
                        <li><a href="#" class="text-light">التقارير</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="text-white mb-3">الدعم</h6>
                    <ul class="list-unstyled">
                        <li><a href="#" class="text-light">مركز المساعدة</a></li>
                        <li><a href="#" class="text-light">الدعم الفني</a></li>
                        <li><a href="#" class="text-light">التدريب</a></li>
                        <li><a href="#" class="text-light">الأسئلة الشائعة</a></li>
                    </ul>
                </div>
                <div class="col-lg-2 col-md-6 mb-4">
                    <h6 class="text-white mb-3">الشركة</h6>
                    <ul class="list-unstyled">
                        <li><a href="#" class="text-light">من نحن</a></li>
                        <li><a href="#" class="text-light">فريق العمل</a></li>
                        <li><a href="#" class="text-light">الوظائف</a></li>
                        <li><a href="#" class="text-light">الأخبار</a></li>
                    </ul>
                </div>
            </div>
            <hr class="my-4" style="border-color: rgba(255,255,255,0.2);">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <p class="text-light mb-0">
                        © 2024 قمة الوعد للسفريات. جميع الحقوق محفوظة.
                    </p>
                </div>
                <div class="col-md-6 text-md-end">
                    <a href="#" class="text-light me-3">سياسة الخصوصية</a>
                    <a href="#" class="text-light me-3">شروط الاستخدام</a>
                    <a href="#" class="text-light">ملفات تعريف الارتباط</a>
                </div>
            </div>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
    <script>
        // Initialize AOS with enhanced settings
        AOS.init({
            duration: 1000,
            once: true,
            offset: 100,
            easing: 'ease-out-cubic'
        });

        // Enhanced Navbar Scroll Effect
        window.addEventListener('scroll', function() {
            const navbar = document.querySelector('.navbar');
            if (window.scrollY > 50) {
                navbar.classList.add('scrolled');
            } else {
                navbar.classList.remove('scrolled');
            }
        });

        // Smooth Scrolling for Navigation Links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Animate counters with enhanced effect
        function animateCounters() {
            const counters = document.querySelectorAll('.stat-number');
            counters.forEach(counter => {
                const target = parseInt(counter.getAttribute('data-count'));
                const increment = target / 100;
                let current = 0;

                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }
                    counter.textContent = Math.floor(current);
                }, 20);
            });
        }

        // Enhanced Intersection Observer for multiple elements
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    if (entry.target.classList.contains('stats-section')) {
                        animateCounters();
                    }

                    // Add animation classes
                    entry.target.classList.add('animate-in');
                    observer.unobserve(entry.target);
                }
            });
        }, observerOptions);

        // Observe multiple sections
        document.addEventListener('DOMContentLoaded', function() {
            const sections = document.querySelectorAll('.stats-section, .feature-card, .testimonial-card');
            sections.forEach(section => {
                observer.observe(section);
            });
        });

        // Typing Effect for Hero Title
        function typeWriter(element, text, speed = 100) {
            let i = 0;
            element.innerHTML = '';

            function type() {
                if (i < text.length) {
                    element.innerHTML += text.charAt(i);
                    i++;
                    setTimeout(type, speed);
                }
            }
            type();
        }

        // Parallax Effect for Hero Section
        window.addEventListener('scroll', function() {
            const scrolled = window.pageYOffset;
            const parallax = document.querySelector('.hero-section');
            const speed = scrolled * 0.5;

            if (parallax) {
                parallax.style.transform = `translateY(${speed}px)`;
            }
        });

        // Interactive Pricing Cards
        document.querySelectorAll('.pricing-table').forEach(card => {
            card.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-10px) scale(1.02)';
            });

            card.addEventListener('mouseleave', function() {
                if (!this.classList.contains('featured')) {
                    this.style.transform = 'translateY(0) scale(1)';
                } else {
                    this.style.transform = 'translateY(0) scale(1.05)';
                }
            });
        });

        // Contact Form Enhancement
        document.querySelector('form').addEventListener('submit', function(e) {
            e.preventDefault();

            // Show loading state
            const submitBtn = this.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>جاري الإرسال...';
            submitBtn.disabled = true;

            // Simulate form submission
            setTimeout(() => {
                // Show success message
                const alert = document.createElement('div');
                alert.className = 'alert alert-success mt-3';
                alert.innerHTML = '<i class="fas fa-check-circle me-2"></i>تم إرسال رسالتك بنجاح! سنتواصل معك قريباً.';
                this.appendChild(alert);

                // Reset form
                this.reset();

                // Reset button
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;

                // Remove alert after 5 seconds
                setTimeout(() => {
                    alert.remove();
                }, 5000);
            }, 2000);
        });

        // Dynamic Particle System
        function createParticles() {
            const particlesContainer = document.querySelector('.hero-particles');
            const particleCount = 15;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.width = (Math.random() * 4 + 2) + 'px';
                particle.style.height = particle.style.width;
                particle.style.animationDelay = Math.random() * 8 + 's';
                particle.style.animationDuration = (Math.random() * 4 + 6) + 's';

                particlesContainer.appendChild(particle);
            }
        }

        // Initialize particles
        createParticles();

        // Testimonials Carousel Auto-play
        let currentTestimonial = 0;
        const testimonials = document.querySelectorAll('.testimonial-card');

        function rotateTestimonials() {
            testimonials.forEach((testimonial, index) => {
                testimonial.style.opacity = index === currentTestimonial ? '1' : '0.7';
                testimonial.style.transform = index === currentTestimonial ? 'scale(1.05)' : 'scale(1)';
            });

            currentTestimonial = (currentTestimonial + 1) % testimonials.length;
        }

        // Start testimonials rotation
        setInterval(rotateTestimonials, 4000);

        // Loading Screen
        window.addEventListener('load', function() {
            const loader = document.createElement('div');
            loader.id = 'loader';
            loader.innerHTML = `
                <div class="loader-content">
                    <div class="loader-logo">
                        <i class="fas fa-plane fa-3x text-primary mb-3"></i>
                        <h4>قمة الوعد للسفريات</h4>
                        <div class="loader-spinner"></div>
                    </div>
                </div>
            `;
            loader.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background: white;
                display: flex;
                align-items: center;
                justify-content: center;
                z-index: 9999;
                transition: opacity 0.5s ease;
            `;

            document.body.appendChild(loader);

            setTimeout(() => {
                loader.style.opacity = '0';
                setTimeout(() => {
                    loader.remove();
                }, 500);
            }, 1500);
        });

        // Add CSS for loader spinner
        const style = document.createElement('style');
        style.textContent = `
            .loader-spinner {
                width: 40px;
                height: 40px;
                border: 4px solid #f3f3f3;
                border-top: 4px solid #2c5aa0;
                border-radius: 50%;
                animation: spin 1s linear infinite;
                margin: 20px auto;
            }

            @keyframes spin {
                0% { transform: rotate(0deg); }
                100% { transform: rotate(360deg); }
            }

            .animate-in {
                animation: slideInUp 0.6s ease-out;
            }

            @keyframes slideInUp {
                from {
                    opacity: 0;
                    transform: translateY(30px);
                }
                to {
                    opacity: 1;
                    transform: translateY(0);
                }
            }
        `;
        document.head.appendChild(style);

        // Enhanced Mobile Menu
        const navbarToggler = document.querySelector('.navbar-toggler');
        const navbarCollapse = document.querySelector('.navbar-collapse');

        if (navbarToggler) {
            navbarToggler.addEventListener('click', function() {
                navbarCollapse.classList.toggle('show');
            });
        }

        // Close mobile menu when clicking on links
        document.querySelectorAll('.navbar-nav .nav-link').forEach(link => {
            link.addEventListener('click', function() {
                if (window.innerWidth < 992) {
                    navbarCollapse.classList.remove('show');
                }
            });
        });

        // Performance optimization: Lazy load images
        const images = document.querySelectorAll('img[data-src]');
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });

        images.forEach(img => imageObserver.observe(img));
    </script>
</body>
</html>
