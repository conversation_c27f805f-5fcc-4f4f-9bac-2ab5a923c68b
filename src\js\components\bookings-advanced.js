/**
 * ===================================
 * إدارة الحجوزات المتقدمة - Advanced Bookings Management
 * قمة الوعد للسفريات
 * ===================================
 */

window.AdvancedBookings = {
    // إعدادات النظام
    config: {
        itemsPerPage: 20,
        autoSaveInterval: 30000, // 30 ثانية
        bookingTypes: {
            flight: 'تذاكر طيران',
            hotel: 'حجز فنادق',
            hajj: 'حج وعمرة',
            visa: 'تأشيرات',
            package: 'باقات سياحية',
            transport: 'نقل ومواصلات'
        },
        bookingStatuses: {
            pending: 'في الانتظار',
            confirmed: 'مؤكد',
            cancelled: 'ملغي',
            completed: 'مكتمل',
            refunded: 'مسترد'
        },
        paymentStatuses: {
            unpaid: 'غير مدفوع',
            partial: 'مدفوع جزئياً',
            paid: 'مدفوع بالكامل',
            refunded: 'مسترد'
        }
    },

    // حالة النظام
    state: {
        bookings: [],
        filteredBookings: [],
        currentPage: 1,
        totalPages: 1,
        isLoading: false,
        selectedBookings: [],
        sortBy: 'created_at',
        sortOrder: 'desc',
        filters: {
            search: '',
            type: '',
            status: '',
            paymentStatus: '',
            dateFrom: '',
            dateTo: '',
            customer: '',
            agent: ''
        },
        currentBooking: null,
        viewMode: 'table' // table, cards, calendar
    },

    /**
     * تهيئة نظام إدارة الحجوزات
     */
    init: function() {
        console.log('🔄 تهيئة نظام إدارة الحجوزات المتقدم');
        
        try {
            // تحميل البيانات
            this.loadBookings();
            
            // تهيئة الأحداث
            this.initEvents();
            
            // بدء الحفظ التلقائي
            this.startAutoSave();
            
            console.log('✅ تم تهيئة نظام إدارة الحجوزات بنجاح');
            
        } catch (error) {
            console.error('❌ خطأ في تهيئة نظام إدارة الحجوزات:', error);
        }
    },

    /**
     * عرض صفحة إدارة الحجوزات
     */
    show: function() {
        const content = `
            <div class="bookings-container fade-in">
                <!-- Header Section -->
                <div class="page-header mb-4">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h1 class="page-title">
                                <i class="fas fa-calendar-alt me-3"></i>
                                إدارة الحجوزات
                            </h1>
                            <p class="page-subtitle text-muted">
                                إدارة شاملة لجميع أنواع الحجوزات والخدمات
                            </p>
                        </div>
                        <div class="col-md-6 text-md-end">
                            <div class="page-actions">
                                <div class="btn-group me-2">
                                    <button class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                                        <i class="fas fa-plus me-2"></i>حجز جديد
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" onclick="AdvancedBookings.showNewBookingModal('flight')">
                                            <i class="fas fa-plane me-2"></i>تذاكر طيران
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="AdvancedBookings.showNewBookingModal('hotel')">
                                            <i class="fas fa-hotel me-2"></i>حجز فنادق
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="AdvancedBookings.showNewBookingModal('hajj')">
                                            <i class="fas fa-kaaba me-2"></i>حج وعمرة
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="AdvancedBookings.showNewBookingModal('visa')">
                                            <i class="fas fa-passport me-2"></i>تأشيرات
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="AdvancedBookings.showNewBookingModal('package')">
                                            <i class="fas fa-suitcase me-2"></i>باقات سياحية
                                        </a></li>
                                    </ul>
                                </div>
                                <div class="btn-group">
                                    <button class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                        <i class="fas fa-download me-2"></i>تصدير
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" onclick="AdvancedBookings.exportData('excel')">
                                            <i class="fas fa-file-excel me-2"></i>Excel
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="AdvancedBookings.exportData('pdf')">
                                            <i class="fas fa-file-pdf me-2"></i>PDF
                                        </a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Stats -->
                <div class="row mb-4">
                    <div class="col-xl-2 col-md-4 col-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon primary">
                                <i class="fas fa-calendar-check"></i>
                            </div>
                            <div class="stat-number" id="totalBookings">0</div>
                            <div class="stat-label">إجمالي الحجوزات</div>
                        </div>
                    </div>
                    <div class="col-xl-2 col-md-4 col-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon success">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="stat-number" id="confirmedBookings">0</div>
                            <div class="stat-label">مؤكدة</div>
                        </div>
                    </div>
                    <div class="col-xl-2 col-md-4 col-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon warning">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="stat-number" id="pendingBookings">0</div>
                            <div class="stat-label">في الانتظار</div>
                        </div>
                    </div>
                    <div class="col-xl-2 col-md-4 col-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon danger">
                                <i class="fas fa-times-circle"></i>
                            </div>
                            <div class="stat-number" id="cancelledBookings">0</div>
                            <div class="stat-label">ملغية</div>
                        </div>
                    </div>
                    <div class="col-xl-2 col-md-4 col-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon info">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                            <div class="stat-number" id="totalRevenue">0</div>
                            <div class="stat-label">إجمالي الإيرادات</div>
                        </div>
                    </div>
                    <div class="col-xl-2 col-md-4 col-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon success">
                                <i class="fas fa-credit-card"></i>
                            </div>
                            <div class="stat-number" id="paidAmount">0</div>
                            <div class="stat-label">المبلغ المدفوع</div>
                        </div>
                    </div>
                </div>

                <!-- Filters and Search -->
                <div class="dashboard-card mb-4">
                    <div class="card-header-enhanced">
                        <h5>
                            <i class="fas fa-filter"></i>
                            البحث والتصفية المتقدمة
                        </h5>
                        <div class="d-flex align-items-center">
                            <button class="btn btn-sm btn-outline-secondary me-2" onclick="AdvancedBookings.clearFilters()">
                                <i class="fas fa-times me-1"></i>مسح الفلاتر
                            </button>
                            <button class="btn btn-sm btn-outline-primary" onclick="AdvancedBookings.saveFilters()">
                                <i class="fas fa-save me-1"></i>حفظ الفلاتر
                            </button>
                        </div>
                    </div>
                    <div class="card-body-enhanced">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label class="form-label">البحث العام</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                    <input type="text" class="form-control" id="searchInput" 
                                           placeholder="رقم الحجز، اسم العميل، أو الوجهة">
                                </div>
                            </div>
                            <div class="col-md-2 mb-3">
                                <label class="form-label">نوع الحجز</label>
                                <select class="form-select" id="typeFilter">
                                    <option value="">جميع الأنواع</option>
                                    ${Object.entries(this.config.bookingTypes).map(([key, value]) => 
                                        `<option value="${key}">${value}</option>`
                                    ).join('')}
                                </select>
                            </div>
                            <div class="col-md-2 mb-3">
                                <label class="form-label">حالة الحجز</label>
                                <select class="form-select" id="statusFilter">
                                    <option value="">جميع الحالات</option>
                                    ${Object.entries(this.config.bookingStatuses).map(([key, value]) => 
                                        `<option value="${key}">${value}</option>`
                                    ).join('')}
                                </select>
                            </div>
                            <div class="col-md-2 mb-3">
                                <label class="form-label">حالة الدفع</label>
                                <select class="form-select" id="paymentStatusFilter">
                                    <option value="">جميع حالات الدفع</option>
                                    ${Object.entries(this.config.paymentStatuses).map(([key, value]) => 
                                        `<option value="${key}">${value}</option>`
                                    ).join('')}
                                </select>
                            </div>
                            <div class="col-md-1.5 mb-3">
                                <label class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="dateFromFilter">
                            </div>
                            <div class="col-md-1.5 mb-3">
                                <label class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="dateToFilter">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- View Controls -->
                <div class="dashboard-card mb-4">
                    <div class="card-header-enhanced">
                        <h5>
                            <i class="fas fa-list"></i>
                            قائمة الحجوزات
                        </h5>
                        <div class="d-flex align-items-center">
                            <span class="text-muted me-3" id="bookingsCount">عرض 0 من 0 حجز</span>
                            <div class="btn-group btn-group-sm me-3">
                                <button class="btn btn-outline-primary active" onclick="AdvancedBookings.toggleView('table')" id="tableViewBtn">
                                    <i class="fas fa-table"></i> جدول
                                </button>
                                <button class="btn btn-outline-primary" onclick="AdvancedBookings.toggleView('cards')" id="cardsViewBtn">
                                    <i class="fas fa-th-large"></i> بطاقات
                                </button>
                                <button class="btn btn-outline-primary" onclick="AdvancedBookings.toggleView('calendar')" id="calendarViewBtn">
                                    <i class="fas fa-calendar"></i> تقويم
                                </button>
                            </div>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-secondary" onclick="AdvancedBookings.bulkAction('confirm')" title="تأكيد المحدد">
                                    <i class="fas fa-check"></i>
                                </button>
                                <button class="btn btn-outline-secondary" onclick="AdvancedBookings.bulkAction('cancel')" title="إلغاء المحدد">
                                    <i class="fas fa-times"></i>
                                </button>
                                <button class="btn btn-outline-secondary" onclick="AdvancedBookings.bulkAction('delete')" title="حذف المحدد">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body-enhanced p-0">
                        <!-- Table View -->
                        <div id="tableView" class="table-enhanced">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th width="40">
                                                <input type="checkbox" class="form-check-input" id="selectAllBookings">
                                            </th>
                                            <th>رقم الحجز</th>
                                            <th>العميل</th>
                                            <th>نوع الحجز</th>
                                            <th>التفاصيل</th>
                                            <th>التاريخ</th>
                                            <th>المبلغ</th>
                                            <th>حالة الحجز</th>
                                            <th>حالة الدفع</th>
                                            <th width="120">الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="bookingsTableBody">
                                        <!-- سيتم ملؤها ديناميكياً -->
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Cards View -->
                        <div id="cardsView" class="d-none p-3">
                            <div class="row" id="bookingsCardsContainer">
                                <!-- سيتم ملؤها ديناميكياً -->
                            </div>
                        </div>

                        <!-- Calendar View -->
                        <div id="calendarView" class="d-none p-3">
                            <div id="bookingsCalendar">
                                <!-- سيتم ملؤها ديناميكياً -->
                            </div>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-between align-items-center p-3 border-top">
                            <div class="pagination-info">
                                <span class="text-muted" id="paginationInfo">عرض 1-20 من 500 حجز</span>
                            </div>
                            <nav>
                                <ul class="pagination pagination-sm mb-0" id="paginationNav">
                                    <!-- سيتم ملؤها ديناميكياً -->
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Booking Modals -->
            ${this.renderBookingModals()}
        `;

        // عرض المحتوى
        if (window.UI && window.UI.showPage) {
            window.UI.showPage(content);
        } else {
            document.getElementById('main-content').innerHTML = content;
        }

        // تحميل البيانات
        this.loadBookings();
        
        // تهيئة الأحداث
        this.initEvents();
    }
};
