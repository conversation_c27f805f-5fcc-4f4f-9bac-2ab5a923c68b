/**
 * ===================================
 * إدارة الحجوزات المتقدمة - Advanced Bookings Management
 * قمة الوعد للسفريات
 * ===================================
 */

window.AdvancedBookings = {
    // إعدادات النظام
    config: {
        itemsPerPage: 20,
        autoSaveInterval: 30000, // 30 ثانية
        bookingTypes: {
            flight: 'تذاكر طيران',
            hotel: 'حجز فنادق',
            hajj: 'حج وعمرة',
            visa: 'تأشيرات',
            package: 'باقات سياحية',
            transport: 'نقل ومواصلات'
        },
        bookingStatuses: {
            pending: 'في الانتظار',
            confirmed: 'مؤكد',
            cancelled: 'ملغي',
            completed: 'مكتمل',
            refunded: 'مسترد'
        },
        paymentStatuses: {
            unpaid: 'غير مدفوع',
            partial: 'مدفوع جزئياً',
            paid: 'مدفوع بالكامل',
            refunded: 'مسترد'
        }
    },

    // حالة النظام
    state: {
        bookings: [],
        filteredBookings: [],
        currentPage: 1,
        totalPages: 1,
        isLoading: false,
        selectedBookings: [],
        sortBy: 'created_at',
        sortOrder: 'desc',
        filters: {
            search: '',
            type: '',
            status: '',
            paymentStatus: '',
            dateFrom: '',
            dateTo: '',
            customer: '',
            agent: ''
        },
        currentBooking: null,
        viewMode: 'table' // table, cards, calendar
    },

    /**
     * تهيئة نظام إدارة الحجوزات
     */
    init: function() {
        console.log('🔄 تهيئة نظام إدارة الحجوزات المتقدم');
        
        try {
            // تحميل البيانات
            this.loadBookings();
            
            // تهيئة الأحداث
            this.initEvents();
            
            // بدء الحفظ التلقائي
            this.startAutoSave();
            
            console.log('✅ تم تهيئة نظام إدارة الحجوزات بنجاح');
            
        } catch (error) {
            console.error('❌ خطأ في تهيئة نظام إدارة الحجوزات:', error);
        }
    },

    /**
     * عرض صفحة إدارة الحجوزات
     */
    show: function() {
        const content = `
            <div class="bookings-container fade-in">
                <!-- Header Section -->
                <div class="page-header mb-4">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h1 class="page-title">
                                <i class="fas fa-calendar-alt me-3"></i>
                                إدارة الحجوزات
                            </h1>
                            <p class="page-subtitle text-muted">
                                إدارة شاملة لجميع أنواع الحجوزات والخدمات
                            </p>
                        </div>
                        <div class="col-md-6 text-md-end">
                            <div class="page-actions">
                                <div class="btn-group me-2">
                                    <button class="btn btn-outline-primary dropdown-toggle" data-bs-toggle="dropdown">
                                        <i class="fas fa-plus me-2"></i>حجز جديد
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" onclick="AdvancedBookings.showNewBookingModal('flight')">
                                            <i class="fas fa-plane me-2"></i>تذاكر طيران
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="AdvancedBookings.showNewBookingModal('hotel')">
                                            <i class="fas fa-hotel me-2"></i>حجز فنادق
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="AdvancedBookings.showNewBookingModal('hajj')">
                                            <i class="fas fa-kaaba me-2"></i>حج وعمرة
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="AdvancedBookings.showNewBookingModal('visa')">
                                            <i class="fas fa-passport me-2"></i>تأشيرات
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="AdvancedBookings.showNewBookingModal('package')">
                                            <i class="fas fa-suitcase me-2"></i>باقات سياحية
                                        </a></li>
                                    </ul>
                                </div>
                                <div class="btn-group">
                                    <button class="btn btn-outline-secondary dropdown-toggle" data-bs-toggle="dropdown">
                                        <i class="fas fa-download me-2"></i>تصدير
                                    </button>
                                    <ul class="dropdown-menu">
                                        <li><a class="dropdown-item" href="#" onclick="AdvancedBookings.exportData('excel')">
                                            <i class="fas fa-file-excel me-2"></i>Excel
                                        </a></li>
                                        <li><a class="dropdown-item" href="#" onclick="AdvancedBookings.exportData('pdf')">
                                            <i class="fas fa-file-pdf me-2"></i>PDF
                                        </a></li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Quick Stats -->
                <div class="row mb-4">
                    <div class="col-xl-2 col-md-4 col-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon primary">
                                <i class="fas fa-calendar-check"></i>
                            </div>
                            <div class="stat-number" id="totalBookings">0</div>
                            <div class="stat-label">إجمالي الحجوزات</div>
                        </div>
                    </div>
                    <div class="col-xl-2 col-md-4 col-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon success">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="stat-number" id="confirmedBookings">0</div>
                            <div class="stat-label">مؤكدة</div>
                        </div>
                    </div>
                    <div class="col-xl-2 col-md-4 col-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon warning">
                                <i class="fas fa-clock"></i>
                            </div>
                            <div class="stat-number" id="pendingBookings">0</div>
                            <div class="stat-label">في الانتظار</div>
                        </div>
                    </div>
                    <div class="col-xl-2 col-md-4 col-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon danger">
                                <i class="fas fa-times-circle"></i>
                            </div>
                            <div class="stat-number" id="cancelledBookings">0</div>
                            <div class="stat-label">ملغية</div>
                        </div>
                    </div>
                    <div class="col-xl-2 col-md-4 col-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon info">
                                <i class="fas fa-dollar-sign"></i>
                            </div>
                            <div class="stat-number" id="totalRevenue">0</div>
                            <div class="stat-label">إجمالي الإيرادات</div>
                        </div>
                    </div>
                    <div class="col-xl-2 col-md-4 col-6 mb-3">
                        <div class="stat-card">
                            <div class="stat-icon success">
                                <i class="fas fa-credit-card"></i>
                            </div>
                            <div class="stat-number" id="paidAmount">0</div>
                            <div class="stat-label">المبلغ المدفوع</div>
                        </div>
                    </div>
                </div>

                <!-- Filters and Search -->
                <div class="dashboard-card mb-4">
                    <div class="card-header-enhanced">
                        <h5>
                            <i class="fas fa-filter"></i>
                            البحث والتصفية المتقدمة
                        </h5>
                        <div class="d-flex align-items-center">
                            <button class="btn btn-sm btn-outline-secondary me-2" onclick="AdvancedBookings.clearFilters()">
                                <i class="fas fa-times me-1"></i>مسح الفلاتر
                            </button>
                            <button class="btn btn-sm btn-outline-primary" onclick="AdvancedBookings.saveFilters()">
                                <i class="fas fa-save me-1"></i>حفظ الفلاتر
                            </button>
                        </div>
                    </div>
                    <div class="card-body-enhanced">
                        <div class="row">
                            <div class="col-md-3 mb-3">
                                <label class="form-label">البحث العام</label>
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-search"></i>
                                    </span>
                                    <input type="text" class="form-control" id="searchInput" 
                                           placeholder="رقم الحجز، اسم العميل، أو الوجهة">
                                </div>
                            </div>
                            <div class="col-md-2 mb-3">
                                <label class="form-label">نوع الحجز</label>
                                <select class="form-select" id="typeFilter">
                                    <option value="">جميع الأنواع</option>
                                    ${Object.entries(this.config.bookingTypes).map(([key, value]) => 
                                        `<option value="${key}">${value}</option>`
                                    ).join('')}
                                </select>
                            </div>
                            <div class="col-md-2 mb-3">
                                <label class="form-label">حالة الحجز</label>
                                <select class="form-select" id="statusFilter">
                                    <option value="">جميع الحالات</option>
                                    ${Object.entries(this.config.bookingStatuses).map(([key, value]) => 
                                        `<option value="${key}">${value}</option>`
                                    ).join('')}
                                </select>
                            </div>
                            <div class="col-md-2 mb-3">
                                <label class="form-label">حالة الدفع</label>
                                <select class="form-select" id="paymentStatusFilter">
                                    <option value="">جميع حالات الدفع</option>
                                    ${Object.entries(this.config.paymentStatuses).map(([key, value]) => 
                                        `<option value="${key}">${value}</option>`
                                    ).join('')}
                                </select>
                            </div>
                            <div class="col-md-1.5 mb-3">
                                <label class="form-label">من تاريخ</label>
                                <input type="date" class="form-control" id="dateFromFilter">
                            </div>
                            <div class="col-md-1.5 mb-3">
                                <label class="form-label">إلى تاريخ</label>
                                <input type="date" class="form-control" id="dateToFilter">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- View Controls -->
                <div class="dashboard-card mb-4">
                    <div class="card-header-enhanced">
                        <h5>
                            <i class="fas fa-list"></i>
                            قائمة الحجوزات
                        </h5>
                        <div class="d-flex align-items-center">
                            <span class="text-muted me-3" id="bookingsCount">عرض 0 من 0 حجز</span>
                            <div class="btn-group btn-group-sm me-3">
                                <button class="btn btn-outline-primary active" onclick="AdvancedBookings.toggleView('table')" id="tableViewBtn">
                                    <i class="fas fa-table"></i> جدول
                                </button>
                                <button class="btn btn-outline-primary" onclick="AdvancedBookings.toggleView('cards')" id="cardsViewBtn">
                                    <i class="fas fa-th-large"></i> بطاقات
                                </button>
                                <button class="btn btn-outline-primary" onclick="AdvancedBookings.toggleView('calendar')" id="calendarViewBtn">
                                    <i class="fas fa-calendar"></i> تقويم
                                </button>
                            </div>
                            <div class="btn-group btn-group-sm">
                                <button class="btn btn-outline-secondary" onclick="AdvancedBookings.bulkAction('confirm')" title="تأكيد المحدد">
                                    <i class="fas fa-check"></i>
                                </button>
                                <button class="btn btn-outline-secondary" onclick="AdvancedBookings.bulkAction('cancel')" title="إلغاء المحدد">
                                    <i class="fas fa-times"></i>
                                </button>
                                <button class="btn btn-outline-secondary" onclick="AdvancedBookings.bulkAction('delete')" title="حذف المحدد">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body-enhanced p-0">
                        <!-- Table View -->
                        <div id="tableView" class="table-enhanced">
                            <div class="table-responsive">
                                <table class="table table-hover mb-0">
                                    <thead>
                                        <tr>
                                            <th width="40">
                                                <input type="checkbox" class="form-check-input" id="selectAllBookings">
                                            </th>
                                            <th>رقم الحجز</th>
                                            <th>العميل</th>
                                            <th>نوع الحجز</th>
                                            <th>التفاصيل</th>
                                            <th>التاريخ</th>
                                            <th>المبلغ</th>
                                            <th>حالة الحجز</th>
                                            <th>حالة الدفع</th>
                                            <th width="120">الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody id="bookingsTableBody">
                                        <!-- سيتم ملؤها ديناميكياً -->
                                    </tbody>
                                </table>
                            </div>
                        </div>

                        <!-- Cards View -->
                        <div id="cardsView" class="d-none p-3">
                            <div class="row" id="bookingsCardsContainer">
                                <!-- سيتم ملؤها ديناميكياً -->
                            </div>
                        </div>

                        <!-- Calendar View -->
                        <div id="calendarView" class="d-none p-3">
                            <div id="bookingsCalendar">
                                <!-- سيتم ملؤها ديناميكياً -->
                            </div>
                        </div>

                        <!-- Pagination -->
                        <div class="d-flex justify-content-between align-items-center p-3 border-top">
                            <div class="pagination-info">
                                <span class="text-muted" id="paginationInfo">عرض 1-20 من 500 حجز</span>
                            </div>
                            <nav>
                                <ul class="pagination pagination-sm mb-0" id="paginationNav">
                                    <!-- سيتم ملؤها ديناميكياً -->
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Booking Modals -->
            ${this.renderBookingModals()}
        `;

        // عرض المحتوى
        if (window.UI && window.UI.showPage) {
            window.UI.showPage(content);
        } else {
            document.getElementById('main-content').innerHTML = content;
        }

        // تحميل البيانات
        this.loadBookings();

        // تهيئة الأحداث
        this.initEvents();
    },

    /**
     * تحميل بيانات الحجوزات
     */
    loadBookings: function() {
        this.state.isLoading = true;

        try {
            setTimeout(() => {
                // بيانات وهمية للحجوزات
                this.state.bookings = this.generateSampleBookings();

                // تطبيق الفلاتر
                this.applyFilters();

                // تحديث الإحصائيات
                this.updateStatistics();

                // عرض البيانات
                this.renderBookings();

                this.state.isLoading = false;

            }, 1000);

        } catch (error) {
            console.error('خطأ في تحميل بيانات الحجوزات:', error);
            this.state.isLoading = false;
        }
    },

    /**
     * توليد بيانات حجوزات وهمية
     */
    generateSampleBookings: function() {
        const bookings = [];
        const customers = ['أحمد محمد', 'فاطمة أحمد', 'محمد علي', 'سارة محمد', 'علي حسن'];
        const types = Object.keys(this.config.bookingTypes);
        const statuses = Object.keys(this.config.bookingStatuses);
        const paymentStatuses = Object.keys(this.config.paymentStatuses);

        for (let i = 1; i <= 200; i++) {
            const type = types[Math.floor(Math.random() * types.length)];
            const status = statuses[Math.floor(Math.random() * statuses.length)];
            const paymentStatus = paymentStatuses[Math.floor(Math.random() * paymentStatuses.length)];

            bookings.push({
                id: i,
                bookingNumber: `BK${String(i).padStart(6, '0')}`,
                customer: customers[Math.floor(Math.random() * customers.length)],
                type: type,
                status: status,
                paymentStatus: paymentStatus,
                amount: Math.floor(Math.random() * 10000) + 1000,
                paidAmount: Math.floor(Math.random() * 8000),
                details: this.getBookingDetails(type),
                bookingDate: new Date(Date.now() - Math.random() * 90 * 24 * 60 * 60 * 1000),
                travelDate: new Date(Date.now() + Math.random() * 180 * 24 * 60 * 60 * 1000),
                createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000)
            });
        }

        return bookings;
    },

    /**
     * الحصول على تفاصيل الحجز حسب النوع
     */
    getBookingDetails: function(type) {
        const details = {
            flight: 'الرياض - جدة',
            hotel: 'فندق الريتز كارلتون',
            hajj: 'حج 2024 - باقة VIP',
            visa: 'تأشيرة سياحية - دبي',
            package: 'باقة تركيا 7 أيام',
            transport: 'نقل من المطار'
        };
        return details[type] || 'تفاصيل الحجز';
    },

    /**
     * تطبيق الفلاتر
     */
    applyFilters: function() {
        let filtered = [...this.state.bookings];

        // فلتر البحث
        if (this.state.filters.search) {
            const search = this.state.filters.search.toLowerCase();
            filtered = filtered.filter(booking =>
                booking.bookingNumber.toLowerCase().includes(search) ||
                booking.customer.toLowerCase().includes(search) ||
                booking.details.toLowerCase().includes(search)
            );
        }

        // فلتر النوع
        if (this.state.filters.type) {
            filtered = filtered.filter(booking => booking.type === this.state.filters.type);
        }

        // فلتر الحالة
        if (this.state.filters.status) {
            filtered = filtered.filter(booking => booking.status === this.state.filters.status);
        }

        // فلتر حالة الدفع
        if (this.state.filters.paymentStatus) {
            filtered = filtered.filter(booking => booking.paymentStatus === this.state.filters.paymentStatus);
        }

        // فلتر التاريخ
        if (this.state.filters.dateFrom) {
            const fromDate = new Date(this.state.filters.dateFrom);
            filtered = filtered.filter(booking => booking.bookingDate >= fromDate);
        }

        if (this.state.filters.dateTo) {
            const toDate = new Date(this.state.filters.dateTo);
            filtered = filtered.filter(booking => booking.bookingDate <= toDate);
        }

        // ترتيب النتائج
        filtered.sort((a, b) => {
            const aValue = a[this.state.sortBy];
            const bValue = b[this.state.sortBy];

            if (this.state.sortOrder === 'asc') {
                return aValue > bValue ? 1 : -1;
            } else {
                return aValue < bValue ? 1 : -1;
            }
        });

        this.state.filteredBookings = filtered;
        this.state.totalPages = Math.ceil(filtered.length / this.config.itemsPerPage);
        this.state.currentPage = 1;
    },

    /**
     * تحديث الإحصائيات
     */
    updateStatistics: function() {
        const total = this.state.bookings.length;
        const confirmed = this.state.bookings.filter(b => b.status === 'confirmed').length;
        const pending = this.state.bookings.filter(b => b.status === 'pending').length;
        const cancelled = this.state.bookings.filter(b => b.status === 'cancelled').length;
        const totalRevenue = this.state.bookings.reduce((sum, b) => sum + b.amount, 0);
        const paidAmount = this.state.bookings.reduce((sum, b) => sum + b.paidAmount, 0);

        // تحديث الأرقام مع تأثير العد التدريجي
        this.animateNumber('totalBookings', total);
        this.animateNumber('confirmedBookings', confirmed);
        this.animateNumber('pendingBookings', pending);
        this.animateNumber('cancelledBookings', cancelled);
        this.animateNumber('totalRevenue', totalRevenue, 'currency');
        this.animateNumber('paidAmount', paidAmount, 'currency');
    },

    /**
     * عرض الحجوزات
     */
    renderBookings: function() {
        const startIndex = (this.state.currentPage - 1) * this.config.itemsPerPage;
        const endIndex = startIndex + this.config.itemsPerPage;
        const pageBookings = this.state.filteredBookings.slice(startIndex, endIndex);

        // عرض في الجدول
        this.renderBookingsTable(pageBookings);

        // عرض في البطاقات
        this.renderBookingsCards(pageBookings);

        // تحديث معلومات الصفحة
        this.updatePagination();
    },

    /**
     * عرض الحجوزات في الجدول
     */
    renderBookingsTable: function(bookings) {
        const tbody = document.getElementById('bookingsTableBody');
        if (!tbody) return;

        tbody.innerHTML = bookings.map(booking => `
            <tr>
                <td>
                    <input type="checkbox" class="form-check-input booking-checkbox" value="${booking.id}">
                </td>
                <td>
                    <div>
                        <strong>${booking.bookingNumber}</strong><br>
                        <small class="text-muted">${this.formatDate(booking.bookingDate)}</small>
                    </div>
                </td>
                <td>
                    <div>
                        <h6 class="mb-0">${booking.customer}</h6>
                        <small class="text-muted">عميل</small>
                    </div>
                </td>
                <td>
                    <span class="badge bg-${this.getTypeBadgeColor(booking.type)}">
                        ${this.config.bookingTypes[booking.type]}
                    </span>
                </td>
                <td>
                    <div>
                        <strong>${booking.details}</strong><br>
                        <small class="text-muted">تاريخ السفر: ${this.formatDate(booking.travelDate)}</small>
                    </div>
                </td>
                <td>
                    <small class="text-muted">${this.formatDate(booking.createdAt)}</small>
                </td>
                <td>
                    <div>
                        <strong>${booking.amount.toLocaleString('ar-SA')} ر.س</strong><br>
                        <small class="text-muted">مدفوع: ${booking.paidAmount.toLocaleString('ar-SA')} ر.س</small>
                    </div>
                </td>
                <td>
                    <span class="badge bg-${this.getStatusBadgeColor(booking.status)}">
                        ${this.config.bookingStatuses[booking.status]}
                    </span>
                </td>
                <td>
                    <span class="badge bg-${this.getPaymentStatusBadgeColor(booking.paymentStatus)}">
                        ${this.config.paymentStatuses[booking.paymentStatus]}
                    </span>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="AdvancedBookings.viewBooking(${booking.id})" title="عرض">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-outline-success" onclick="AdvancedBookings.editBooking(${booking.id})" title="تعديل">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="AdvancedBookings.deleteBooking(${booking.id})" title="حذف">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    },

    /**
     * عرض الحجوزات في البطاقات
     */
    renderBookingsCards: function(bookings) {
        const container = document.getElementById('bookingsCardsContainer');
        if (!container) return;

        container.innerHTML = bookings.map(booking => `
            <div class="col-xl-4 col-lg-6 mb-4">
                <div class="dashboard-card h-100">
                    <div class="card-body-enhanced">
                        <div class="d-flex justify-content-between align-items-start mb-3">
                            <div>
                                <h5 class="card-title">${booking.bookingNumber}</h5>
                                <p class="text-muted mb-0">${booking.customer}</p>
                            </div>
                            <span class="badge bg-${this.getStatusBadgeColor(booking.status)}">
                                ${this.config.bookingStatuses[booking.status]}
                            </span>
                        </div>

                        <div class="mb-3">
                            <span class="badge bg-${this.getTypeBadgeColor(booking.type)} me-2">
                                ${this.config.bookingTypes[booking.type]}
                            </span>
                            <span class="badge bg-${this.getPaymentStatusBadgeColor(booking.paymentStatus)}">
                                ${this.config.paymentStatuses[booking.paymentStatus]}
                            </span>
                        </div>

                        <p class="card-text">${booking.details}</p>

                        <div class="row text-center mb-3">
                            <div class="col-6">
                                <h6 class="text-primary mb-0">${booking.amount.toLocaleString('ar-SA')}</h6>
                                <small class="text-muted">المبلغ الإجمالي</small>
                            </div>
                            <div class="col-6">
                                <h6 class="text-success mb-0">${booking.paidAmount.toLocaleString('ar-SA')}</h6>
                                <small class="text-muted">المبلغ المدفوع</small>
                            </div>
                        </div>

                        <div class="mb-3">
                            <small class="text-muted">
                                <i class="fas fa-calendar me-1"></i>
                                تاريخ السفر: ${this.formatDate(booking.travelDate)}
                            </small>
                        </div>

                        <div class="btn-group w-100">
                            <button class="btn btn-outline-primary btn-sm" onclick="AdvancedBookings.viewBooking(${booking.id})">
                                <i class="fas fa-eye me-1"></i>عرض
                            </button>
                            <button class="btn btn-outline-success btn-sm" onclick="AdvancedBookings.editBooking(${booking.id})">
                                <i class="fas fa-edit me-1"></i>تعديل
                            </button>
                            <button class="btn btn-outline-danger btn-sm" onclick="AdvancedBookings.deleteBooking(${booking.id})">
                                <i class="fas fa-trash me-1"></i>حذف
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    },

    /**
     * تحديث الترقيم
     */
    updatePagination: function() {
        const paginationNav = document.getElementById('paginationNav');
        const paginationInfo = document.getElementById('paginationInfo');
        const bookingsCount = document.getElementById('bookingsCount');

        if (!paginationNav) return;

        // تحديث معلومات الصفحة
        const startIndex = (this.state.currentPage - 1) * this.config.itemsPerPage + 1;
        const endIndex = Math.min(this.state.currentPage * this.config.itemsPerPage, this.state.filteredBookings.length);

        if (paginationInfo) {
            paginationInfo.textContent = `عرض ${startIndex}-${endIndex} من ${this.state.filteredBookings.length} حجز`;
        }

        if (bookingsCount) {
            bookingsCount.textContent = `عرض ${this.state.filteredBookings.length} من ${this.state.bookings.length} حجز`;
        }

        // إنشاء أزرار الترقيم
        let paginationHTML = '';

        // زر السابق
        paginationHTML += `
            <li class="page-item ${this.state.currentPage === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="AdvancedBookings.goToPage(${this.state.currentPage - 1})">السابق</a>
            </li>
        `;

        // أرقام الصفحات
        const maxVisiblePages = 5;
        let startPage = Math.max(1, this.state.currentPage - Math.floor(maxVisiblePages / 2));
        let endPage = Math.min(this.state.totalPages, startPage + maxVisiblePages - 1);

        if (endPage - startPage + 1 < maxVisiblePages) {
            startPage = Math.max(1, endPage - maxVisiblePages + 1);
        }

        for (let i = startPage; i <= endPage; i++) {
            paginationHTML += `
                <li class="page-item ${i === this.state.currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="AdvancedBookings.goToPage(${i})">${i}</a>
                </li>
            `;
        }

        // زر التالي
        paginationHTML += `
            <li class="page-item ${this.state.currentPage === this.state.totalPages ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="AdvancedBookings.goToPage(${this.state.currentPage + 1})">التالي</a>
            </li>
        `;

        paginationNav.innerHTML = paginationHTML;
    },

    /**
     * الانتقال إلى صفحة معينة
     */
    goToPage: function(page) {
        if (page < 1 || page > this.state.totalPages) return;

        this.state.currentPage = page;
        this.renderBookings();
    },

    /**
     * تهيئة الأحداث
     */
    initEvents: function() {
        // البحث
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            let searchTimeout;
            searchInput.addEventListener('input', (e) => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.state.filters.search = e.target.value;
                    this.applyFilters();
                    this.renderBookings();
                }, 300);
            });
        }

        // فلاتر
        ['typeFilter', 'statusFilter', 'paymentStatusFilter', 'dateFromFilter', 'dateToFilter'].forEach(filterId => {
            const element = document.getElementById(filterId);
            if (element) {
                element.addEventListener('change', (e) => {
                    const filterKey = filterId.replace('Filter', '').replace('dateFrom', 'dateFrom').replace('dateTo', 'dateTo').replace('paymentStatus', 'paymentStatus');
                    this.state.filters[filterKey] = e.target.value;
                    this.applyFilters();
                    this.renderBookings();
                });
            }
        });

        // تحديد الكل
        const selectAll = document.getElementById('selectAllBookings');
        if (selectAll) {
            selectAll.addEventListener('change', (e) => {
                const checkboxes = document.querySelectorAll('.booking-checkbox');
                checkboxes.forEach(cb => cb.checked = e.target.checked);
                this.updateSelectedBookings();
            });
        }
    },

    /**
     * تحديث الحجوزات المحددة
     */
    updateSelectedBookings: function() {
        const checkboxes = document.querySelectorAll('.booking-checkbox:checked');
        this.state.selectedBookings = Array.from(checkboxes).map(cb => parseInt(cb.value));
    },

    /**
     * تبديل العرض
     */
    toggleView: function(view) {
        const tableView = document.getElementById('tableView');
        const cardsView = document.getElementById('cardsView');
        const calendarView = document.getElementById('calendarView');
        const tableBtn = document.getElementById('tableViewBtn');
        const cardsBtn = document.getElementById('cardsViewBtn');
        const calendarBtn = document.getElementById('calendarViewBtn');

        // إخفاء جميع العروض
        [tableView, cardsView, calendarView].forEach(v => v && v.classList.add('d-none'));
        [tableBtn, cardsBtn, calendarBtn].forEach(b => b && b.classList.remove('active'));

        // إظهار العرض المحدد
        if (view === 'table' && tableView) {
            tableView.classList.remove('d-none');
            tableBtn && tableBtn.classList.add('active');
        } else if (view === 'cards' && cardsView) {
            cardsView.classList.remove('d-none');
            cardsBtn && cardsBtn.classList.add('active');
        } else if (view === 'calendar' && calendarView) {
            calendarView.classList.remove('d-none');
            calendarBtn && calendarBtn.classList.add('active');
            this.renderCalendar();
        }

        this.state.viewMode = view;
    },

    /**
     * عرض الحجز
     */
    viewBooking: function(bookingId) {
        const booking = this.state.bookings.find(b => b.id === bookingId);
        if (!booking) return;

        this.showBookingModal(booking, 'view');
    },

    /**
     * تعديل الحجز
     */
    editBooking: function(bookingId) {
        const booking = this.state.bookings.find(b => b.id === bookingId);
        if (!booking) return;

        this.showBookingModal(booking, 'edit');
    },

    /**
     * حذف الحجز
     */
    deleteBooking: function(bookingId) {
        if (confirm('هل أنت متأكد من حذف هذا الحجز؟')) {
            this.state.bookings = this.state.bookings.filter(b => b.id !== bookingId);
            this.applyFilters();
            this.updateStatistics();
            this.renderBookings();
            this.showNotification('تم حذف الحجز بنجاح', 'success');
        }
    },

    /**
     * عرض نافذة حجز جديد
     */
    showNewBookingModal: function(type) {
        this.showBookingModal(null, 'add', type);
    },

    /**
     * عرض نافذة الحجز
     */
    showBookingModal: function(booking = null, mode = 'add', bookingType = 'flight') {
        const modalId = 'bookingModal';
        const isEdit = mode === 'edit';
        const isView = mode === 'view';

        const modalHTML = `
            <div class="modal fade" id="${modalId}" tabindex="-1">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">
                                <i class="fas fa-calendar-alt me-2"></i>
                                ${isView ? 'عرض الحجز' : isEdit ? 'تعديل الحجز' : 'حجز جديد'}
                            </h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="bookingForm">
                                <div class="row">
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">رقم الحجز</label>
                                        <input type="text" class="form-control" id="bookingNumber"
                                               value="${booking ? booking.bookingNumber : 'BK' + String(Date.now()).slice(-6)}" readonly>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">العميل *</label>
                                        <input type="text" class="form-control" id="bookingCustomer"
                                               value="${booking ? booking.customer : ''}" ${isView ? 'readonly' : ''} required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">نوع الحجز *</label>
                                        <select class="form-select" id="bookingType" ${isView ? 'disabled' : ''} required>
                                            ${Object.entries(this.config.bookingTypes).map(([key, value]) =>
                                                `<option value="${key}" ${(booking && booking.type === key) || (!booking && bookingType === key) ? 'selected' : ''}>${value}</option>`
                                            ).join('')}
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">حالة الحجز</label>
                                        <select class="form-select" id="bookingStatus" ${isView ? 'disabled' : ''}>
                                            ${Object.entries(this.config.bookingStatuses).map(([key, value]) =>
                                                `<option value="${key}" ${booking && booking.status === key ? 'selected' : ''}>${value}</option>`
                                            ).join('')}
                                        </select>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">المبلغ الإجمالي *</label>
                                        <input type="number" class="form-control" id="bookingAmount"
                                               value="${booking ? booking.amount : ''}" ${isView ? 'readonly' : ''} required>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">المبلغ المدفوع</label>
                                        <input type="number" class="form-control" id="bookingPaidAmount"
                                               value="${booking ? booking.paidAmount : '0'}" ${isView ? 'readonly' : ''}>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">تاريخ الحجز</label>
                                        <input type="date" class="form-control" id="bookingDate"
                                               value="${booking ? booking.bookingDate.toISOString().split('T')[0] : new Date().toISOString().split('T')[0]}" ${isView ? 'readonly' : ''}>
                                    </div>
                                    <div class="col-md-6 mb-3">
                                        <label class="form-label">تاريخ السفر</label>
                                        <input type="date" class="form-control" id="bookingTravelDate"
                                               value="${booking ? booking.travelDate.toISOString().split('T')[0] : ''}" ${isView ? 'readonly' : ''}>
                                    </div>
                                    <div class="col-12 mb-3">
                                        <label class="form-label">تفاصيل الحجز *</label>
                                        <textarea class="form-control" id="bookingDetails" rows="3" ${isView ? 'readonly' : ''} required>${booking ? booking.details : ''}</textarea>
                                    </div>
                                    <div class="col-12 mb-3">
                                        <label class="form-label">ملاحظات</label>
                                        <textarea class="form-control" id="bookingNotes" rows="3" ${isView ? 'readonly' : ''}>${booking ? booking.notes || '' : ''}</textarea>
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                ${isView ? 'إغلاق' : 'إلغاء'}
                            </button>
                            ${!isView ? `
                                <button type="button" class="btn btn-primary" onclick="AdvancedBookings.saveBooking(${booking ? booking.id : 'null'})">
                                    <i class="fas fa-save me-2"></i>
                                    ${isEdit ? 'تحديث' : 'حفظ'}
                                </button>
                            ` : ''}
                        </div>
                    </div>
                </div>
            </div>
        `;

        // إزالة النافذة السابقة إن وجدت
        const existingModal = document.getElementById(modalId);
        if (existingModal) {
            existingModal.remove();
        }

        // إضافة النافذة الجديدة
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // إظهار النافذة
        const modal = new bootstrap.Modal(document.getElementById(modalId));
        modal.show();

        // إزالة النافذة عند الإغلاق
        document.getElementById(modalId).addEventListener('hidden.bs.modal', function() {
            this.remove();
        });
    },

    /**
     * حفظ الحجز
     */
    saveBooking: function(bookingId = null) {
        const form = document.getElementById('bookingForm');
        if (!form.checkValidity()) {
            form.reportValidity();
            return;
        }

        const bookingData = {
            bookingNumber: document.getElementById('bookingNumber').value.trim(),
            customer: document.getElementById('bookingCustomer').value.trim(),
            type: document.getElementById('bookingType').value,
            status: document.getElementById('bookingStatus').value,
            amount: parseFloat(document.getElementById('bookingAmount').value) || 0,
            paidAmount: parseFloat(document.getElementById('bookingPaidAmount').value) || 0,
            bookingDate: new Date(document.getElementById('bookingDate').value),
            travelDate: new Date(document.getElementById('bookingTravelDate').value),
            details: document.getElementById('bookingDetails').value.trim(),
            notes: document.getElementById('bookingNotes').value.trim()
        };

        // تحديد حالة الدفع
        if (bookingData.paidAmount >= bookingData.amount) {
            bookingData.paymentStatus = 'paid';
        } else if (bookingData.paidAmount > 0) {
            bookingData.paymentStatus = 'partial';
        } else {
            bookingData.paymentStatus = 'unpaid';
        }

        if (bookingId) {
            // تحديث حجز موجود
            const bookingIndex = this.state.bookings.findIndex(b => b.id === bookingId);
            if (bookingIndex !== -1) {
                this.state.bookings[bookingIndex] = {
                    ...this.state.bookings[bookingIndex],
                    ...bookingData
                };
                this.showNotification('تم تحديث الحجز بنجاح', 'success');
            }
        } else {
            // إضافة حجز جديد
            const newBooking = {
                id: Date.now(),
                ...bookingData,
                createdAt: new Date()
            };
            this.state.bookings.push(newBooking);
            this.showNotification('تم إضافة الحجز بنجاح', 'success');
        }

        // تحديث العرض
        this.applyFilters();
        this.updateStatistics();
        this.renderBookings();

        // إغلاق النافذة
        const modal = bootstrap.Modal.getInstance(document.getElementById('bookingModal'));
        modal.hide();
    },

    /**
     * الوظائف المساعدة
     */
    getStatusBadgeColor: function(status) {
        const colors = {
            pending: 'warning',
            confirmed: 'success',
            cancelled: 'danger',
            completed: 'info',
            refunded: 'secondary'
        };
        return colors[status] || 'secondary';
    },

    getPaymentStatusBadgeColor: function(status) {
        const colors = {
            unpaid: 'danger',
            partial: 'warning',
            paid: 'success',
            refunded: 'info'
        };
        return colors[status] || 'secondary';
    },

    getTypeBadgeColor: function(type) {
        const colors = {
            flight: 'primary',
            hotel: 'info',
            hajj: 'success',
            visa: 'warning',
            package: 'dark',
            transport: 'secondary'
        };
        return colors[type] || 'secondary';
    },

    formatDate: function(date) {
        if (!date) return '-';
        return new Date(date).toLocaleDateString('ar-SA');
    },

    animateNumber: function(elementId, targetValue, format = 'number') {
        const element = document.getElementById(elementId);
        if (!element) return;

        const startValue = 0;
        const duration = 2000;
        const startTime = performance.now();

        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);

            const easeOutQuart = 1 - Math.pow(1 - progress, 4);
            const currentValue = startValue + (targetValue - startValue) * easeOutQuart;

            let displayValue;
            if (format === 'currency') {
                displayValue = Math.round(currentValue).toLocaleString('ar-SA') + ' ر.س';
            } else {
                displayValue = Math.round(currentValue).toLocaleString('ar-SA');
            }

            element.textContent = displayValue;

            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };

        requestAnimationFrame(animate);
    },

    showNotification: function(message, type = 'info') {
        if (window.AdvancedApp && window.AdvancedApp.showNotification) {
            window.AdvancedApp.showNotification(message, type);
        } else {
            alert(message);
        }
    },

    exportData: function(format) {
        this.showNotification(`جاري تصدير البيانات بصيغة ${format.toUpperCase()}...`, 'info');

        setTimeout(() => {
            this.showNotification(`تم تصدير البيانات بصيغة ${format.toUpperCase()} بنجاح`, 'success');
        }, 2000);
    },

    renderBookingModals: function() {
        return ''; // سيتم إنشاؤها ديناميكياً
    },

    renderCalendar: function() {
        // تنفيذ عرض التقويم
        const calendarContainer = document.getElementById('bookingsCalendar');
        if (calendarContainer) {
            calendarContainer.innerHTML = `
                <div class="text-center py-5">
                    <i class="fas fa-calendar-alt fa-3x text-muted mb-3"></i>
                    <h5 class="text-muted">عرض التقويم</h5>
                    <p class="text-muted">سيتم تطوير عرض التقويم قريباً</p>
                </div>
            `;
        }
    },

    bulkAction: function(action) {
        const selectedBookings = this.state.selectedBookings;
        if (selectedBookings.length === 0) {
            this.showNotification('يرجى تحديد حجوزات للتنفيذ', 'warning');
            return;
        }

        let message = '';
        switch (action) {
            case 'confirm':
                message = `تأكيد ${selectedBookings.length} حجز`;
                break;
            case 'cancel':
                message = `إلغاء ${selectedBookings.length} حجز`;
                break;
            case 'delete':
                message = `حذف ${selectedBookings.length} حجز`;
                break;
        }

        if (confirm(`هل أنت متأكد من ${message}؟`)) {
            // تنفيذ الإجراء
            selectedBookings.forEach(bookingId => {
                const bookingIndex = this.state.bookings.findIndex(b => b.id === bookingId);
                if (bookingIndex !== -1) {
                    if (action === 'delete') {
                        this.state.bookings.splice(bookingIndex, 1);
                    } else {
                        this.state.bookings[bookingIndex].status = action === 'confirm' ? 'confirmed' : 'cancelled';
                    }
                }
            });

            this.applyFilters();
            this.updateStatistics();
            this.renderBookings();
            this.showNotification(`تم ${message} بنجاح`, 'success');
        }
    },

    clearFilters: function() {
        this.state.filters = {
            search: '',
            type: '',
            status: '',
            paymentStatus: '',
            dateFrom: '',
            dateTo: '',
            customer: '',
            agent: ''
        };

        // مسح قيم الحقول
        const inputs = ['searchInput', 'typeFilter', 'statusFilter', 'paymentStatusFilter', 'dateFromFilter', 'dateToFilter'];
        inputs.forEach(id => {
            const element = document.getElementById(id);
            if (element) element.value = '';
        });

        this.applyFilters();
        this.renderBookings();
    },

    startAutoSave: function() {
        setInterval(() => {
            // حفظ تلقائي للبيانات
            localStorage.setItem('bookingsData', JSON.stringify(this.state.bookings));
        }, this.config.autoSaveInterval);
    }
};
