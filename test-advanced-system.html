<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار النظام المتقدم - قمة الوعد للسفريات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        .test-card {
            border-left: 4px solid #007bff;
            transition: all 0.3s ease;
        }
        
        .test-card:hover {
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        
        .test-success {
            border-left-color: #28a745;
        }
        
        .test-error {
            border-left-color: #dc3545;
        }
        
        .test-warning {
            border-left-color: #ffc107;
        }
        
        .test-result {
            min-height: 100px;
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-top: 10px;
        }
        
        .loading-spinner {
            display: none;
        }
        
        .progress-bar {
            transition: width 0.3s ease;
        }
        
        .test-log {
            max-height: 300px;
            overflow-y: auto;
            background: #1e1e1e;
            color: #fff;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 0.9rem;
        }
        
        .log-success { color: #28a745; }
        .log-error { color: #dc3545; }
        .log-warning { color: #ffc107; }
        .log-info { color: #17a2b8; }
    </style>
</head>
<body>
    <div class="container-fluid py-4">
        <div class="row">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <div>
                        <h1><i class="fas fa-vial me-2"></i>اختبار النظام المتقدم</h1>
                        <p class="text-muted">اختبار شامل لجميع مكونات النظام المحاسبي المتطور</p>
                    </div>
                    <div>
                        <button class="btn btn-primary" onclick="runAllTests()">
                            <i class="fas fa-play me-2"></i>تشغيل جميع الاختبارات
                        </button>
                        <button class="btn btn-outline-secondary" onclick="clearResults()">
                            <i class="fas fa-trash me-2"></i>مسح النتائج
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- شريط التقدم العام -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h5>تقدم الاختبارات</h5>
                        <div class="progress mb-2" style="height: 25px;">
                            <div id="overallProgress" class="progress-bar progress-bar-striped progress-bar-animated" 
                                 role="progressbar" style="width: 0%">0%</div>
                        </div>
                        <div class="d-flex justify-content-between">
                            <span id="testStatus">جاهز للبدء</span>
                            <span id="testCount">0 / 0</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row">
            <!-- اختبارات المصادقة والأمان -->
            <div class="col-lg-6 mb-4">
                <div class="card test-card" id="authTestCard">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-shield-alt me-2"></i>اختبارات المصادقة والأمان</h5>
                        <button class="btn btn-sm btn-outline-primary" onclick="testAuthentication()">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="test-result" id="authTestResult">
                            <div class="text-center text-muted">
                                <i class="fas fa-clock fa-2x mb-2"></i>
                                <p>انقر على زر التشغيل لبدء الاختبار</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- اختبارات إدارة المستخدمين -->
            <div class="col-lg-6 mb-4">
                <div class="card test-card" id="userTestCard">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-users me-2"></i>اختبارات إدارة المستخدمين</h5>
                        <button class="btn btn-sm btn-outline-primary" onclick="testUserManagement()">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="test-result" id="userTestResult">
                            <div class="text-center text-muted">
                                <i class="fas fa-clock fa-2x mb-2"></i>
                                <p>انقر على زر التشغيل لبدء الاختبار</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- اختبارات التشفير -->
            <div class="col-lg-6 mb-4">
                <div class="card test-card" id="encryptionTestCard">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-lock me-2"></i>اختبارات التشفير</h5>
                        <button class="btn btn-sm btn-outline-primary" onclick="testEncryption()">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="test-result" id="encryptionTestResult">
                            <div class="text-center text-muted">
                                <i class="fas fa-clock fa-2x mb-2"></i>
                                <p>انقر على زر التشغيل لبدء الاختبار</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- اختبارات قاعدة البيانات -->
            <div class="col-lg-6 mb-4">
                <div class="card test-card" id="databaseTestCard">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-database me-2"></i>اختبارات قاعدة البيانات</h5>
                        <button class="btn btn-sm btn-outline-primary" onclick="testDatabase()">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="test-result" id="databaseTestResult">
                            <div class="text-center text-muted">
                                <i class="fas fa-clock fa-2x mb-2"></i>
                                <p>انقر على زر التشغيل لبدء الاختبار</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- اختبارات الأداء -->
            <div class="col-lg-6 mb-4">
                <div class="card test-card" id="performanceTestCard">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-tachometer-alt me-2"></i>اختبارات الأداء</h5>
                        <button class="btn btn-sm btn-outline-primary" onclick="testPerformance()">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="test-result" id="performanceTestResult">
                            <div class="text-center text-muted">
                                <i class="fas fa-clock fa-2x mb-2"></i>
                                <p>انقر على زر التشغيل لبدء الاختبار</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- اختبارات الواجهة -->
            <div class="col-lg-6 mb-4">
                <div class="card test-card" id="uiTestCard">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-desktop me-2"></i>اختبارات الواجهة</h5>
                        <button class="btn btn-sm btn-outline-primary" onclick="testUI()">
                            <i class="fas fa-play"></i>
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="test-result" id="uiTestResult">
                            <div class="text-center text-muted">
                                <i class="fas fa-clock fa-2x mb-2"></i>
                                <p>انقر على زر التشغيل لبدء الاختبار</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- سجل الاختبارات -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5><i class="fas fa-list me-2"></i>سجل الاختبارات</h5>
                        <button class="btn btn-sm btn-outline-secondary" onclick="clearLog()">
                            <i class="fas fa-trash"></i> مسح السجل
                        </button>
                    </div>
                    <div class="card-body">
                        <div id="testLog" class="test-log">
                            <div class="log-info">[INFO] نظام الاختبار جاهز</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- تحميل المكتبات -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- تحميل ملفات النظام -->
    <script src="src/js/core/database.js"></script>
    <script src="src/js/core/auth.js"></script>
    <script src="src/js/utils/encryption.js"></script>
    <script src="src/js/utils/security.js"></script>
    <script src="src/js/components/user-management.js"></script>

    <script>
        // متغيرات الاختبار
        let testResults = {
            total: 0,
            passed: 0,
            failed: 0,
            warnings: 0
        };

        let currentTest = 0;
        let totalTests = 6;

        /**
         * تشغيل جميع الاختبارات
         */
        async function runAllTests() {
            log('بدء تشغيل جميع الاختبارات...', 'info');
            
            testResults = { total: 0, passed: 0, failed: 0, warnings: 0 };
            currentTest = 0;
            
            updateProgress(0, 'جاري تشغيل الاختبارات...');
            
            try {
                await testAuthentication();
                await delay(500);
                
                await testUserManagement();
                await delay(500);
                
                await testEncryption();
                await delay(500);
                
                await testDatabase();
                await delay(500);
                
                await testPerformance();
                await delay(500);
                
                await testUI();
                
                // عرض النتائج النهائية
                showFinalResults();
                
            } catch (error) {
                log(`خطأ في تشغيل الاختبارات: ${error.message}`, 'error');
            }
        }

        /**
         * اختبار المصادقة والأمان
         */
        async function testAuthentication() {
            log('بدء اختبار المصادقة والأمان...', 'info');
            
            const card = document.getElementById('authTestCard');
            const result = document.getElementById('authTestResult');
            
            card.classList.remove('test-success', 'test-error', 'test-warning');
            result.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin fa-2x"></i><p>جاري الاختبار...</p></div>';
            
            let tests = [];
            
            try {
                // اختبار تهيئة نظام المصادقة
                if (window.Auth && typeof window.Auth.init === 'function') {
                    window.Auth.init();
                    tests.push({ name: 'تهيئة نظام المصادقة', status: 'success' });
                } else {
                    tests.push({ name: 'تهيئة نظام المصادقة', status: 'error', message: 'النظام غير متوفر' });
                }
                
                // اختبار تسجيل الدخول
                try {
                    const loginResult = await window.Auth.login({
                        username: 'admin',
                        password: 'admin123'
                    });
                    tests.push({ name: 'تسجيل الدخول', status: 'success' });
                } catch (error) {
                    tests.push({ name: 'تسجيل الدخول', status: 'warning', message: 'بيانات اختبار غير صحيحة' });
                }
                
                // اختبار التحقق من الصلاحيات
                if (window.Auth.hasPermission && window.Auth.hasRole) {
                    tests.push({ name: 'التحقق من الصلاحيات', status: 'success' });
                } else {
                    tests.push({ name: 'التحقق من الصلاحيات', status: 'error', message: 'دوال الصلاحيات غير متوفرة' });
                }
                
                // اختبار الأمان
                if (window.Security && typeof window.Security.init === 'function') {
                    window.Security.init();
                    tests.push({ name: 'نظام الأمان', status: 'success' });
                } else {
                    tests.push({ name: 'نظام الأمان', status: 'error', message: 'نظام الأمان غير متوفر' });
                }
                
                displayTestResults(result, tests, card);
                updateTestCount(tests);
                
            } catch (error) {
                log(`خطأ في اختبار المصادقة: ${error.message}`, 'error');
                result.innerHTML = `<div class="alert alert-danger">خطأ: ${error.message}</div>`;
                card.classList.add('test-error');
            }
            
            currentTest++;
            updateProgress((currentTest / totalTests) * 100);
        }

        /**
         * اختبار إدارة المستخدمين
         */
        async function testUserManagement() {
            log('بدء اختبار إدارة المستخدمين...', 'info');
            
            const card = document.getElementById('userTestCard');
            const result = document.getElementById('userTestResult');
            
            card.classList.remove('test-success', 'test-error', 'test-warning');
            result.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin fa-2x"></i><p>جاري الاختبار...</p></div>';
            
            let tests = [];
            
            try {
                // اختبار تهيئة نظام إدارة المستخدمين
                if (window.UserManagement && typeof window.UserManagement.init === 'function') {
                    window.UserManagement.init();
                    tests.push({ name: 'تهيئة إدارة المستخدمين', status: 'success' });
                } else {
                    tests.push({ name: 'تهيئة إدارة المستخدمين', status: 'error', message: 'النظام غير متوفر' });
                }
                
                // اختبار تحميل المستخدمين
                if (window.UserManagement && typeof window.UserManagement.loadUsers === 'function') {
                    window.UserManagement.loadUsers();
                    tests.push({ name: 'تحميل المستخدمين', status: 'success' });
                } else {
                    tests.push({ name: 'تحميل المستخدمين', status: 'error', message: 'دالة التحميل غير متوفرة' });
                }
                
                // اختبار الصلاحيات المحددة بالأيام
                const testUser = {
                    subscription_start_date: new Date().toISOString().split('T')[0],
                    subscription_end_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
                };
                
                if (window.Auth && typeof window.Auth.checkSubscriptionValidity === 'function') {
                    const validity = window.Auth.checkSubscriptionValidity(testUser);
                    if (validity.isValid) {
                        tests.push({ name: 'التحقق من صلاحية الاشتراك', status: 'success' });
                    } else {
                        tests.push({ name: 'التحقق من صلاحية الاشتراك', status: 'warning', message: validity.message });
                    }
                } else {
                    tests.push({ name: 'التحقق من صلاحية الاشتراك', status: 'error', message: 'دالة التحقق غير متوفرة' });
                }
                
                displayTestResults(result, tests, card);
                updateTestCount(tests);
                
            } catch (error) {
                log(`خطأ في اختبار إدارة المستخدمين: ${error.message}`, 'error');
                result.innerHTML = `<div class="alert alert-danger">خطأ: ${error.message}</div>`;
                card.classList.add('test-error');
            }
            
            currentTest++;
            updateProgress((currentTest / totalTests) * 100);
        }

        /**
         * اختبار التشفير
         */
        async function testEncryption() {
            log('بدء اختبار التشفير...', 'info');
            
            const card = document.getElementById('encryptionTestCard');
            const result = document.getElementById('encryptionTestResult');
            
            card.classList.remove('test-success', 'test-error', 'test-warning');
            result.innerHTML = '<div class="text-center"><i class="fas fa-spinner fa-spin fa-2x"></i><p>جاري الاختبار...</p></div>';
            
            let tests = [];
            
            try {
                // اختبار تهيئة نظام التشفير
                if (window.Encryption && typeof window.Encryption.init === 'function') {
                    window.Encryption.init();
                    tests.push({ name: 'تهيئة نظام التشفير', status: 'success' });
                } else {
                    tests.push({ name: 'تهيئة نظام التشفير', status: 'error', message: 'النظام غير متوفر' });
                }
                
                // اختبار التشفير وفك التشفير
                if (window.Encryption && typeof window.Encryption.encrypt === 'function') {
                    const testData = 'بيانات اختبار التشفير';
                    const encrypted = window.Encryption.encrypt(testData);
                    const decrypted = window.Encryption.decrypt(encrypted);
                    
                    if (decrypted === testData) {
                        tests.push({ name: 'تشفير وفك التشفير', status: 'success' });
                    } else {
                        tests.push({ name: 'تشفير وفك التشفير', status: 'error', message: 'البيانات لا تطابق' });
                    }
                } else {
                    tests.push({ name: 'تشفير وفك التشفير', status: 'error', message: 'دوال التشفير غير متوفرة' });
                }
                
                // اختبار تشفير كلمة المرور
                if (window.Encryption && typeof window.Encryption.hashPassword === 'function') {
                    const password = 'test123';
                    const hashed = window.Encryption.hashPassword(password);
                    const verified = window.Encryption.verifyPassword(password, hashed.hash, hashed.salt);
                    
                    if (verified) {
                        tests.push({ name: 'تشفير كلمة المرور', status: 'success' });
                    } else {
                        tests.push({ name: 'تشفير كلمة المرور', status: 'error', message: 'التحقق فشل' });
                    }
                } else {
                    tests.push({ name: 'تشفير كلمة المرور', status: 'error', message: 'دوال تشفير كلمة المرور غير متوفرة' });
                }
                
                displayTestResults(result, tests, card);
                updateTestCount(tests);
                
            } catch (error) {
                log(`خطأ في اختبار التشفير: ${error.message}`, 'error');
                result.innerHTML = `<div class="alert alert-danger">خطأ: ${error.message}</div>`;
                card.classList.add('test-error');
            }
            
            currentTest++;
            updateProgress((currentTest / totalTests) * 100);
        }

        /**
         * عرض نتائج الاختبار
         */
        function displayTestResults(container, tests, card) {
            let html = '<div class="row">';
            let hasError = false;
            let hasWarning = false;
            
            tests.forEach(test => {
                let iconClass = '';
                let textClass = '';
                
                switch (test.status) {
                    case 'success':
                        iconClass = 'fas fa-check-circle text-success';
                        textClass = 'text-success';
                        break;
                    case 'error':
                        iconClass = 'fas fa-times-circle text-danger';
                        textClass = 'text-danger';
                        hasError = true;
                        break;
                    case 'warning':
                        iconClass = 'fas fa-exclamation-triangle text-warning';
                        textClass = 'text-warning';
                        hasWarning = true;
                        break;
                }
                
                html += `
                    <div class="col-12 mb-2">
                        <div class="d-flex align-items-center">
                            <i class="${iconClass} me-2"></i>
                            <span class="${textClass}">${test.name}</span>
                            ${test.message ? `<small class="text-muted ms-2">(${test.message})</small>` : ''}
                        </div>
                    </div>
                `;
            });
            
            html += '</div>';
            container.innerHTML = html;
            
            // تحديث لون البطاقة
            if (hasError) {
                card.classList.add('test-error');
            } else if (hasWarning) {
                card.classList.add('test-warning');
            } else {
                card.classList.add('test-success');
            }
        }

        /**
         * تحديث عداد الاختبارات
         */
        function updateTestCount(tests) {
            tests.forEach(test => {
                testResults.total++;
                switch (test.status) {
                    case 'success':
                        testResults.passed++;
                        break;
                    case 'error':
                        testResults.failed++;
                        break;
                    case 'warning':
                        testResults.warnings++;
                        break;
                }
            });
        }

        /**
         * تحديث شريط التقدم
         */
        function updateProgress(percentage, status = null) {
            const progressBar = document.getElementById('overallProgress');
            const testStatus = document.getElementById('testStatus');
            const testCount = document.getElementById('testCount');
            
            progressBar.style.width = percentage + '%';
            progressBar.textContent = Math.round(percentage) + '%';
            
            if (status) {
                testStatus.textContent = status;
            }
            
            testCount.textContent = `${currentTest} / ${totalTests}`;
        }

        /**
         * عرض النتائج النهائية
         */
        function showFinalResults() {
            const status = testResults.failed > 0 ? 'فشل في بعض الاختبارات' : 
                          testResults.warnings > 0 ? 'اكتملت مع تحذيرات' : 'اكتملت بنجاح';
            
            updateProgress(100, status);
            
            log(`انتهت جميع الاختبارات - النجح: ${testResults.passed}, فشل: ${testResults.failed}, تحذيرات: ${testResults.warnings}`, 
                testResults.failed > 0 ? 'error' : testResults.warnings > 0 ? 'warning' : 'success');
        }

        /**
         * تسجيل رسالة في السجل
         */
        function log(message, type = 'info') {
            const logContainer = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString('ar-SA');
            const typeClass = `log-${type}`;
            const typeLabel = {
                'info': 'INFO',
                'success': 'SUCCESS',
                'warning': 'WARNING',
                'error': 'ERROR'
            }[type] || 'INFO';
            
            const logEntry = `<div class="${typeClass}">[${typeLabel}] ${timestamp} - ${message}</div>`;
            logContainer.innerHTML += logEntry;
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        /**
         * مسح السجل
         */
        function clearLog() {
            document.getElementById('testLog').innerHTML = '<div class="log-info">[INFO] تم مسح السجل</div>';
        }

        /**
         * مسح جميع النتائج
         */
        function clearResults() {
            const cards = document.querySelectorAll('.test-card');
            cards.forEach(card => {
                card.classList.remove('test-success', 'test-error', 'test-warning');
            });
            
            const results = document.querySelectorAll('.test-result');
            results.forEach(result => {
                result.innerHTML = `
                    <div class="text-center text-muted">
                        <i class="fas fa-clock fa-2x mb-2"></i>
                        <p>انقر على زر التشغيل لبدء الاختبار</p>
                    </div>
                `;
            });
            
            testResults = { total: 0, passed: 0, failed: 0, warnings: 0 };
            currentTest = 0;
            updateProgress(0, 'جاهز للبدء');
            clearLog();
        }

        /**
         * تأخير
         */
        function delay(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }

        // باقي الاختبارات (سيتم إضافتها في الجزء التالي)
        async function testDatabase() {
            // اختبار قاعدة البيانات
            log('بدء اختبار قاعدة البيانات...', 'info');
            // تنفيذ الاختبارات...
            currentTest++;
            updateProgress((currentTest / totalTests) * 100);
        }

        async function testPerformance() {
            // اختبار الأداء
            log('بدء اختبار الأداء...', 'info');
            // تنفيذ الاختبارات...
            currentTest++;
            updateProgress((currentTest / totalTests) * 100);
        }

        async function testUI() {
            // اختبار الواجهة
            log('بدء اختبار الواجهة...', 'info');
            // تنفيذ الاختبارات...
            currentTest++;
            updateProgress((currentTest / totalTests) * 100);
        }
    </script>
</body>
</html>
