/**
 * ===================================
 * صفحة الهبوط المتقدمة - Advanced Landing Page Styles
 * قمة الوعد للسفريات
 * ===================================
 */

/* Enhanced Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(50px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

@keyframes glow {
    0% {
        box-shadow: 0 0 5px rgba(44, 90, 160, 0.3);
    }
    50% {
        box-shadow: 0 0 20px rgba(44, 90, 160, 0.6);
    }
    100% {
        box-shadow: 0 0 5px rgba(44, 90, 160, 0.3);
    }
}

/* Enhanced Button Styles */
.btn-primary-enhanced {
    background: linear-gradient(135deg, #2c5aa0 0%, #1e3c72 100%);
    border: none;
    padding: 15px 35px;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1.1rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(44, 90, 160, 0.3);
}

.btn-primary-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.btn-primary-enhanced:hover::before {
    left: 100%;
}

.btn-primary-enhanced:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(44, 90, 160, 0.4);
}

.btn-outline-enhanced {
    border: 2px solid #2c5aa0;
    color: #2c5aa0;
    padding: 13px 35px;
    border-radius: 50px;
    font-weight: 600;
    font-size: 1.1rem;
    background: transparent;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.btn-outline-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0;
    height: 100%;
    background: #2c5aa0;
    transition: width 0.3s ease;
    z-index: -1;
}

.btn-outline-enhanced:hover::before {
    width: 100%;
}

.btn-outline-enhanced:hover {
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(44, 90, 160, 0.3);
}

/* Enhanced Card Styles */
.card-enhanced {
    background: white;
    border-radius: 20px;
    padding: 2.5rem;
    box-shadow: 0 10px 40px rgba(0,0,0,0.08);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    border: 1px solid rgba(0,0,0,0.05);
    position: relative;
    overflow: hidden;
}

.card-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(135deg, #2c5aa0, #28a745);
    transform: scaleX(0);
    transition: transform 0.3s ease;
}

.card-enhanced:hover {
    transform: translateY(-15px);
    box-shadow: 0 25px 60px rgba(0,0,0,0.15);
}

.card-enhanced:hover::before {
    transform: scaleX(1);
}

/* Enhanced Typography */
.display-enhanced {
    font-weight: 900;
    line-height: 1.1;
    background: linear-gradient(135deg, #2c5aa0, #1e3c72);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-gradient {
    background: linear-gradient(135deg, #2c5aa0, #28a745);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Enhanced Icons */
.icon-enhanced {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #2c5aa0, #28a745);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2rem;
    margin: 0 auto 1.5rem;
    position: relative;
    box-shadow: 0 10px 30px rgba(44, 90, 160, 0.3);
    transition: all 0.3s ease;
}

.icon-enhanced::after {
    content: '';
    position: absolute;
    top: -5px;
    left: -5px;
    right: -5px;
    bottom: -5px;
    background: linear-gradient(135deg, #28a745, #2c5aa0);
    border-radius: 50%;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.icon-enhanced:hover {
    transform: scale(1.1);
    animation: pulse 2s infinite;
}

.icon-enhanced:hover::after {
    opacity: 1;
}

/* Enhanced Navigation */
.navbar-enhanced {
    background: rgba(255,255,255,0.95) !important;
    backdrop-filter: blur(20px);
    box-shadow: 0 2px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    padding: 1rem 0;
}

.navbar-enhanced.scrolled {
    background: rgba(255,255,255,0.98) !important;
    box-shadow: 0 5px 40px rgba(0,0,0,0.15);
    padding: 0.5rem 0;
}

.nav-link-enhanced {
    font-weight: 500;
    color: #2c3e50 !important;
    margin: 0 0.5rem;
    padding: 0.8rem 1.5rem !important;
    border-radius: 30px;
    transition: all 0.3s ease;
    position: relative;
}

.nav-link-enhanced::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: #2c5aa0;
    transition: all 0.3s ease;
    transform: translateX(-50%);
}

.nav-link-enhanced:hover {
    background: rgba(44, 90, 160, 0.1);
    color: #2c5aa0 !important;
}

.nav-link-enhanced:hover::before {
    width: 80%;
}

/* Enhanced Hero Section */
.hero-enhanced {
    background: linear-gradient(135deg, #2c5aa0 0%, #1e3c72 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    position: relative;
    overflow: hidden;
}

.hero-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 100" fill="white" opacity="0.1"><polygon points="1000,100 1000,0 0,100"/></svg>');
    background-size: cover;
}

.hero-enhanced::after {
    content: '';
    position: absolute;
    top: -50%;
    right: -20%;
    width: 100%;
    height: 200%;
    background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
    animation: float 6s ease-in-out infinite;
}

/* Enhanced Stats Section */
.stats-enhanced {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    padding: 5rem 0;
    position: relative;
}

.stat-item-enhanced {
    text-align: center;
    padding: 2rem;
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    margin: 1rem 0;
}

.stat-item-enhanced:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
}

.stat-number-enhanced {
    font-size: 3.5rem;
    font-weight: 900;
    background: linear-gradient(135deg, #2c5aa0, #28a745);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    display: block;
    line-height: 1;
}

/* Enhanced Testimonials */
.testimonial-enhanced {
    background: white;
    border-radius: 20px;
    padding: 2.5rem;
    box-shadow: 0 10px 40px rgba(0,0,0,0.08);
    transition: all 0.3s ease;
    text-align: center;
    position: relative;
    margin: 1rem 0;
}

.testimonial-enhanced::before {
    content: '"';
    position: absolute;
    top: -30px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 5rem;
    color: #2c5aa0;
    opacity: 0.2;
    font-family: serif;
}

.testimonial-enhanced:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 60px rgba(0,0,0,0.15);
}

.client-avatar-enhanced {
    width: 90px;
    height: 90px;
    border-radius: 50%;
    margin: 0 auto 1.5rem;
    background: linear-gradient(135deg, #2c5aa0, #28a745);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.8rem;
    font-weight: bold;
    box-shadow: 0 10px 30px rgba(44, 90, 160, 0.3);
}

/* Enhanced Footer */
.footer-enhanced {
    background: linear-gradient(135deg, #2c3e50 0%, #1a252f 100%);
    color: white;
    padding: 4rem 0 2rem;
    position: relative;
}

.footer-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(135deg, #2c5aa0, #28a745);
}

.social-links-enhanced a {
    display: inline-block;
    width: 50px;
    height: 50px;
    background: rgba(255,255,255,0.1);
    border-radius: 50%;
    text-align: center;
    line-height: 50px;
    margin: 0 0.5rem;
    transition: all 0.3s ease;
}

.social-links-enhanced a:hover {
    background: #2c5aa0;
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(44, 90, 160, 0.4);
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .hero-enhanced {
        min-height: 80vh;
    }
    
    .display-enhanced {
        font-size: 2.5rem;
    }
    
    .btn-primary-enhanced,
    .btn-outline-enhanced {
        padding: 12px 25px;
        font-size: 1rem;
    }
    
    .card-enhanced {
        padding: 2rem;
        margin: 1rem 0;
    }
    
    .icon-enhanced {
        width: 70px;
        height: 70px;
        font-size: 1.8rem;
    }
    
    .stat-number-enhanced {
        font-size: 2.5rem;
    }
}

/* Loading Animation */
.loading-dots {
    display: inline-block;
}

.loading-dots::after {
    content: '';
    animation: dots 2s infinite;
}

@keyframes dots {
    0%, 20% { content: ''; }
    40% { content: '.'; }
    60% { content: '..'; }
    80%, 100% { content: '...'; }
}
