/**
 * ===================================
 * واجهة المستخدم المتقدمة - Advanced UI Utilities
 * قمة الوعد للسفريات
 * ===================================
 */

window.AdvancedUI = {
    // إعدادات الواجهة
    config: {
        animationDuration: 300,
        toastDuration: 5000,
        modalBackdrop: 'static',
        tooltipDelay: 500,
        loadingSpinnerDelay: 200,
        sidebarBreakpoint: 992,
        autoHideAlerts: true,
        enableAnimations: true,
        enableSounds: false
    },

    // حالة الواجهة
    state: {
        activeModals: [],
        loadingStates: new Map(),
        tooltips: new Map(),
        sidebarCollapsed: false,
        currentTheme: 'light',
        animations: []
    },

    /**
     * تهيئة واجهة المستخدم المتقدمة
     */
    init: function() {
        console.log('🎨 تهيئة واجهة المستخدم المتقدمة');
        
        try {
            // تهيئة المكونات الأساسية
            this.initTooltips();
            this.initModals();
            this.initSidebar();
            this.initNavbar();
            this.initScrollEffects();
            this.initKeyboardNavigation();
            this.initAccessibility();
            
            console.log('✅ تم تهيئة واجهة المستخدم المتقدمة بنجاح');
            
        } catch (error) {
            console.error('❌ خطأ في تهيئة واجهة المستخدم:', error);
        }
    },

    /**
     * عرض صفحة بتأثيرات متقدمة
     */
    showPage: function(content, options = {}) {
        const container = document.getElementById('main-content');
        if (!container) {
            console.error('عنصر العرض الرئيسي غير موجود');
            return false;
        }

        const settings = {
            animation: 'fadeIn',
            duration: this.config.animationDuration,
            showLoading: true,
            ...options
        };

        // إظهار مؤشر التحميل
        if (settings.showLoading) {
            this.showLoading(container);
        }

        // تأخير لإظهار التأثير
        setTimeout(() => {
            // إخفاء المحتوى الحالي
            if (this.config.enableAnimations) {
                container.style.opacity = '0';
                container.style.transform = 'translateY(20px)';
            }

            setTimeout(() => {
                // عرض المحتوى الجديد
                container.innerHTML = content;
                
                // تطبيق التأثيرات
                if (this.config.enableAnimations) {
                    container.style.transition = `all ${settings.duration}ms ease`;
                    container.style.opacity = '1';
                    container.style.transform = 'translateY(0)';
                }

                // تهيئة المكونات الجديدة
                this.initPageComponents(container);
                
                // إخفاء مؤشر التحميل
                this.hideLoading();

            }, settings.showLoading ? this.config.loadingSpinnerDelay : 0);

        }, 50);

        return true;
    },

    /**
     * إظهار مؤشر التحميل
     */
    showLoading: function(container = null, message = 'جاري التحميل...') {
        const target = container || document.body;
        const loadingId = 'loading-' + Date.now();

        const loadingElement = document.createElement('div');
        loadingElement.id = loadingId;
        loadingElement.className = 'loading-overlay d-flex align-items-center justify-content-center';
        loadingElement.innerHTML = `
            <div class="loading-content text-center">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <div class="loading-message">${message}</div>
            </div>
        `;

        // إضافة الأنماط
        loadingElement.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            z-index: 1000;
            backdrop-filter: blur(2px);
        `;

        if (container) {
            container.style.position = 'relative';
        }

        target.appendChild(loadingElement);
        this.state.loadingStates.set(loadingId, { element: loadingElement, target });

        return loadingId;
    },

    /**
     * إخفاء مؤشر التحميل
     */
    hideLoading: function(loadingId = null) {
        if (loadingId) {
            const loadingState = this.state.loadingStates.get(loadingId);
            if (loadingState) {
                loadingState.element.remove();
                this.state.loadingStates.delete(loadingId);
            }
        } else {
            // إخفاء جميع مؤشرات التحميل
            this.state.loadingStates.forEach((state, id) => {
                state.element.remove();
                this.state.loadingStates.delete(id);
            });
        }
    },

    /**
     * إظهار نافذة منبثقة متقدمة
     */
    showModal: function(options) {
        const settings = {
            id: 'modal-' + Date.now(),
            title: 'نافذة',
            content: '',
            size: 'lg', // sm, lg, xl
            backdrop: this.config.modalBackdrop,
            keyboard: true,
            focus: true,
            showHeader: true,
            showFooter: true,
            buttons: [],
            onShow: null,
            onHide: null,
            ...options
        };

        // إنشاء النافذة
        const modalHTML = `
            <div class="modal fade" id="${settings.id}" tabindex="-1" data-bs-backdrop="${settings.backdrop}" data-bs-keyboard="${settings.keyboard}">
                <div class="modal-dialog modal-${settings.size}">
                    <div class="modal-content">
                        ${settings.showHeader ? `
                            <div class="modal-header">
                                <h5 class="modal-title">${settings.title}</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                        ` : ''}
                        <div class="modal-body">
                            ${settings.content}
                        </div>
                        ${settings.showFooter ? `
                            <div class="modal-footer">
                                ${settings.buttons.map(btn => `
                                    <button type="button" class="btn btn-${btn.type || 'secondary'}" 
                                            ${btn.dismiss ? 'data-bs-dismiss="modal"' : ''} 
                                            onclick="${btn.onclick || ''}">
                                        ${btn.icon ? `<i class="${btn.icon} me-2"></i>` : ''}
                                        ${btn.text}
                                    </button>
                                `).join('')}
                                ${settings.buttons.length === 0 ? '<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>' : ''}
                            </div>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;

        // إضافة النافذة للصفحة
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        
        const modalElement = document.getElementById(settings.id);
        const modal = new bootstrap.Modal(modalElement, {
            backdrop: settings.backdrop,
            keyboard: settings.keyboard,
            focus: settings.focus
        });

        // إضافة الأحداث
        if (settings.onShow) {
            modalElement.addEventListener('shown.bs.modal', settings.onShow);
        }

        if (settings.onHide) {
            modalElement.addEventListener('hidden.bs.modal', settings.onHide);
        }

        // إزالة النافذة عند الإغلاق
        modalElement.addEventListener('hidden.bs.modal', () => {
            modalElement.remove();
            const index = this.state.activeModals.indexOf(settings.id);
            if (index > -1) {
                this.state.activeModals.splice(index, 1);
            }
        });

        // عرض النافذة
        modal.show();
        this.state.activeModals.push(settings.id);

        return modal;
    },

    /**
     * إظهار رسالة تأكيد
     */
    showConfirm: function(message, options = {}) {
        return new Promise((resolve) => {
            const settings = {
                title: 'تأكيد',
                confirmText: 'تأكيد',
                cancelText: 'إلغاء',
                confirmType: 'primary',
                cancelType: 'secondary',
                ...options
            };

            this.showModal({
                title: settings.title,
                content: `<p class="mb-0">${message}</p>`,
                size: 'sm',
                buttons: [
                    {
                        text: settings.cancelText,
                        type: settings.cancelType,
                        dismiss: true,
                        onclick: () => resolve(false)
                    },
                    {
                        text: settings.confirmText,
                        type: settings.confirmType,
                        onclick: () => {
                            resolve(true);
                            bootstrap.Modal.getInstance(document.querySelector('.modal.show')).hide();
                        }
                    }
                ]
            });
        });
    },

    /**
     * إظهار رسالة تنبيه
     */
    showAlert: function(message, type = 'info', options = {}) {
        const settings = {
            title: this.getAlertTitle(type),
            dismissible: true,
            autoHide: this.config.autoHideAlerts,
            duration: this.config.toastDuration,
            position: 'top-right',
            ...options
        };

        const alertId = 'alert-' + Date.now();
        const alertHTML = `
            <div id="${alertId}" class="alert alert-${type} ${settings.dismissible ? 'alert-dismissible' : ''} fade show position-fixed" 
                 style="top: 20px; right: 20px; z-index: 9999; min-width: 300px; max-width: 500px;">
                <div class="d-flex align-items-center">
                    <i class="fas fa-${this.getAlertIcon(type)} me-2"></i>
                    <div class="flex-grow-1">
                        ${settings.title ? `<h6 class="alert-heading mb-1">${settings.title}</h6>` : ''}
                        ${message}
                    </div>
                    ${settings.dismissible ? '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' : ''}
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', alertHTML);

        // إخفاء تلقائي
        if (settings.autoHide) {
            setTimeout(() => {
                const alertElement = document.getElementById(alertId);
                if (alertElement) {
                    alertElement.remove();
                }
            }, settings.duration);
        }

        return alertId;
    },

    /**
     * إظهار Toast
     */
    showToast: function(message, type = 'info', options = {}) {
        const settings = {
            title: '',
            duration: this.config.toastDuration,
            position: 'top-right',
            ...options
        };

        const toastId = 'toast-' + Date.now();
        const toastHTML = `
            <div id="${toastId}" class="toast position-fixed" style="top: 20px; right: 20px; z-index: 9999;">
                <div class="toast-header">
                    <i class="fas fa-${this.getAlertIcon(type)} text-${type} me-2"></i>
                    <strong class="me-auto">${settings.title || this.getAlertTitle(type)}</strong>
                    <small class="text-muted">الآن</small>
                    <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                </div>
                <div class="toast-body">
                    ${message}
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', toastHTML);
        
        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement, {
            delay: settings.duration
        });

        // إزالة العنصر بعد الإخفاء
        toastElement.addEventListener('hidden.bs.toast', () => {
            toastElement.remove();
        });

        toast.show();
        return toast;
    },

    /**
     * تهيئة مكونات الصفحة
     */
    initPageComponents: function(container) {
        // تهيئة التلميحات
        const tooltipElements = container.querySelectorAll('[data-bs-toggle="tooltip"]');
        tooltipElements.forEach(element => {
            new bootstrap.Tooltip(element);
        });

        // تهيئة القوائم المنسدلة
        const dropdownElements = container.querySelectorAll('[data-bs-toggle="dropdown"]');
        dropdownElements.forEach(element => {
            new bootstrap.Dropdown(element);
        });

        // تهيئة الأكورديون
        const accordionElements = container.querySelectorAll('.accordion');
        accordionElements.forEach(element => {
            // تطبيق تأثيرات إضافية إذا لزم الأمر
        });

        // تهيئة الجداول التفاعلية
        this.initInteractiveTables(container);

        // تهيئة النماذج المحسنة
        this.initEnhancedForms(container);
    },

    /**
     * تهيئة الجداول التفاعلية
     */
    initInteractiveTables: function(container) {
        const tables = container.querySelectorAll('.table-enhanced');
        tables.forEach(table => {
            // إضافة تأثيرات hover
            const rows = table.querySelectorAll('tbody tr');
            rows.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.style.backgroundColor = 'rgba(0,123,255,0.1)';
                });
                row.addEventListener('mouseleave', function() {
                    this.style.backgroundColor = '';
                });
            });

            // إضافة وظيفة الفرز
            const headers = table.querySelectorAll('th[data-sort]');
            headers.forEach(header => {
                header.style.cursor = 'pointer';
                header.addEventListener('click', () => {
                    this.sortTable(table, header.dataset.sort);
                });
            });
        });
    },

    /**
     * تهيئة النماذج المحسنة
     */
    initEnhancedForms: function(container) {
        const forms = container.querySelectorAll('form');
        forms.forEach(form => {
            // إضافة التحقق المباشر
            const inputs = form.querySelectorAll('input, select, textarea');
            inputs.forEach(input => {
                input.addEventListener('blur', () => {
                    this.validateField(input);
                });
            });

            // إضافة حفظ تلقائي
            if (form.dataset.autoSave === 'true') {
                this.enableAutoSave(form);
            }
        });
    },

    /**
     * الحصول على عنوان التنبيه
     */
    getAlertTitle: function(type) {
        const titles = {
            success: 'نجح',
            error: 'خطأ',
            warning: 'تحذير',
            info: 'معلومات'
        };
        return titles[type] || 'إشعار';
    },

    /**
     * الحصول على أيقونة التنبيه
     */
    getAlertIcon: function(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    },

    /**
     * تبديل الشريط الجانبي
     */
    toggleSidebar: function() {
        const sidebar = document.querySelector('.sidebar');
        const mainContent = document.querySelector('.main-content');
        
        if (sidebar) {
            this.state.sidebarCollapsed = !this.state.sidebarCollapsed;
            
            if (this.state.sidebarCollapsed) {
                sidebar.classList.add('collapsed');
                if (mainContent) {
                    mainContent.classList.add('sidebar-collapsed');
                }
            } else {
                sidebar.classList.remove('collapsed');
                if (mainContent) {
                    mainContent.classList.remove('sidebar-collapsed');
                }
            }
            
            // حفظ الحالة
            localStorage.setItem('sidebarCollapsed', this.state.sidebarCollapsed);
        }
    },

    /**
     * تطبيق تأثيرات التمرير
     */
    initScrollEffects: function() {
        // تأثير إخفاء/إظهار شريط التنقل عند التمرير
        let lastScrollTop = 0;
        const navbar = document.querySelector('.navbar');
        
        if (navbar) {
            window.addEventListener('scroll', () => {
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                
                if (scrollTop > lastScrollTop && scrollTop > 100) {
                    // التمرير لأسفل
                    navbar.style.transform = 'translateY(-100%)';
                } else {
                    // التمرير لأعلى
                    navbar.style.transform = 'translateY(0)';
                }
                
                lastScrollTop = scrollTop;
            });
        }
    }
};
