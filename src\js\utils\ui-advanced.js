/**
 * ===================================
 * واجهة المستخدم المتقدمة - Advanced UI Utilities
 * قمة الوعد للسفريات
 * ===================================
 */

window.AdvancedUI = {
    // إعدادات الواجهة
    config: {
        animationDuration: 300,
        toastDuration: 5000,
        modalBackdrop: 'static',
        tooltipDelay: 500,
        loadingSpinnerDelay: 200,
        sidebarBreakpoint: 992,
        autoHideAlerts: true,
        enableAnimations: true,
        enableSounds: false
    },

    // حالة الواجهة
    state: {
        activeModals: [],
        loadingStates: new Map(),
        tooltips: new Map(),
        sidebarCollapsed: false,
        currentTheme: 'light',
        animations: []
    },

    /**
     * تهيئة واجهة المستخدم المتقدمة
     */
    init: function() {
        console.log('🎨 تهيئة واجهة المستخدم المتقدمة');
        
        try {
            // تهيئة المكونات الأساسية
            this.initTooltips();
            this.initModals();
            this.initSidebar();
            this.initNavbar();
            this.initScrollEffects();
            this.initKeyboardNavigation();
            this.initAccessibility();
            
            console.log('✅ تم تهيئة واجهة المستخدم المتقدمة بنجاح');
            
        } catch (error) {
            console.error('❌ خطأ في تهيئة واجهة المستخدم:', error);
        }
    },

    /**
     * عرض صفحة بتأثيرات متقدمة
     */
    showPage: function(content, options = {}) {
        const container = document.getElementById('main-content');
        if (!container) {
            console.error('عنصر العرض الرئيسي غير موجود');
            return false;
        }

        const settings = {
            animation: 'fadeIn',
            duration: this.config.animationDuration,
            showLoading: true,
            ...options
        };

        // إظهار مؤشر التحميل
        if (settings.showLoading) {
            this.showLoading(container);
        }

        // تأخير لإظهار التأثير
        setTimeout(() => {
            // إخفاء المحتوى الحالي
            if (this.config.enableAnimations) {
                container.style.opacity = '0';
                container.style.transform = 'translateY(20px)';
            }

            setTimeout(() => {
                // عرض المحتوى الجديد
                container.innerHTML = content;
                
                // تطبيق التأثيرات
                if (this.config.enableAnimations) {
                    container.style.transition = `all ${settings.duration}ms ease`;
                    container.style.opacity = '1';
                    container.style.transform = 'translateY(0)';
                }

                // تهيئة المكونات الجديدة
                this.initPageComponents(container);
                
                // إخفاء مؤشر التحميل
                this.hideLoading();

            }, settings.showLoading ? this.config.loadingSpinnerDelay : 0);

        }, 50);

        return true;
    },

    /**
     * إظهار مؤشر التحميل
     */
    showLoading: function(container = null, message = 'جاري التحميل...') {
        const target = container || document.body;
        const loadingId = 'loading-' + Date.now();

        const loadingElement = document.createElement('div');
        loadingElement.id = loadingId;
        loadingElement.className = 'loading-overlay d-flex align-items-center justify-content-center';
        loadingElement.innerHTML = `
            <div class="loading-content text-center">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">جاري التحميل...</span>
                </div>
                <div class="loading-message">${message}</div>
            </div>
        `;

        // إضافة الأنماط
        loadingElement.style.cssText = `
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(255, 255, 255, 0.9);
            z-index: 1000;
            backdrop-filter: blur(2px);
        `;

        if (container) {
            container.style.position = 'relative';
        }

        target.appendChild(loadingElement);
        this.state.loadingStates.set(loadingId, { element: loadingElement, target });

        return loadingId;
    },

    /**
     * إخفاء مؤشر التحميل
     */
    hideLoading: function(loadingId = null) {
        if (loadingId) {
            const loadingState = this.state.loadingStates.get(loadingId);
            if (loadingState) {
                loadingState.element.remove();
                this.state.loadingStates.delete(loadingId);
            }
        } else {
            // إخفاء جميع مؤشرات التحميل
            this.state.loadingStates.forEach((state, id) => {
                state.element.remove();
                this.state.loadingStates.delete(id);
            });
        }
    },

    /**
     * إظهار نافذة منبثقة متقدمة
     */
    showModal: function(options) {
        const settings = {
            id: 'modal-' + Date.now(),
            title: 'نافذة',
            content: '',
            size: 'lg', // sm, lg, xl
            backdrop: this.config.modalBackdrop,
            keyboard: true,
            focus: true,
            showHeader: true,
            showFooter: true,
            buttons: [],
            onShow: null,
            onHide: null,
            ...options
        };

        // إنشاء النافذة
        const modalHTML = `
            <div class="modal fade" id="${settings.id}" tabindex="-1" data-bs-backdrop="${settings.backdrop}" data-bs-keyboard="${settings.keyboard}">
                <div class="modal-dialog modal-${settings.size}">
                    <div class="modal-content">
                        ${settings.showHeader ? `
                            <div class="modal-header">
                                <h5 class="modal-title">${settings.title}</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                        ` : ''}
                        <div class="modal-body">
                            ${settings.content}
                        </div>
                        ${settings.showFooter ? `
                            <div class="modal-footer">
                                ${settings.buttons.map(btn => `
                                    <button type="button" class="btn btn-${btn.type || 'secondary'}" 
                                            ${btn.dismiss ? 'data-bs-dismiss="modal"' : ''} 
                                            onclick="${btn.onclick || ''}">
                                        ${btn.icon ? `<i class="${btn.icon} me-2"></i>` : ''}
                                        ${btn.text}
                                    </button>
                                `).join('')}
                                ${settings.buttons.length === 0 ? '<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>' : ''}
                            </div>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;

        // إضافة النافذة للصفحة
        document.body.insertAdjacentHTML('beforeend', modalHTML);
        
        const modalElement = document.getElementById(settings.id);
        const modal = new bootstrap.Modal(modalElement, {
            backdrop: settings.backdrop,
            keyboard: settings.keyboard,
            focus: settings.focus
        });

        // إضافة الأحداث
        if (settings.onShow) {
            modalElement.addEventListener('shown.bs.modal', settings.onShow);
        }

        if (settings.onHide) {
            modalElement.addEventListener('hidden.bs.modal', settings.onHide);
        }

        // إزالة النافذة عند الإغلاق
        modalElement.addEventListener('hidden.bs.modal', () => {
            modalElement.remove();
            const index = this.state.activeModals.indexOf(settings.id);
            if (index > -1) {
                this.state.activeModals.splice(index, 1);
            }
        });

        // عرض النافذة
        modal.show();
        this.state.activeModals.push(settings.id);

        return modal;
    },

    /**
     * إظهار رسالة تأكيد
     */
    showConfirm: function(message, options = {}) {
        return new Promise((resolve) => {
            const settings = {
                title: 'تأكيد',
                confirmText: 'تأكيد',
                cancelText: 'إلغاء',
                confirmType: 'primary',
                cancelType: 'secondary',
                ...options
            };

            this.showModal({
                title: settings.title,
                content: `<p class="mb-0">${message}</p>`,
                size: 'sm',
                buttons: [
                    {
                        text: settings.cancelText,
                        type: settings.cancelType,
                        dismiss: true,
                        onclick: () => resolve(false)
                    },
                    {
                        text: settings.confirmText,
                        type: settings.confirmType,
                        onclick: () => {
                            resolve(true);
                            bootstrap.Modal.getInstance(document.querySelector('.modal.show')).hide();
                        }
                    }
                ]
            });
        });
    },

    /**
     * إظهار رسالة تنبيه
     */
    showAlert: function(message, type = 'info', options = {}) {
        const settings = {
            title: this.getAlertTitle(type),
            dismissible: true,
            autoHide: this.config.autoHideAlerts,
            duration: this.config.toastDuration,
            position: 'top-right',
            ...options
        };

        const alertId = 'alert-' + Date.now();
        const alertHTML = `
            <div id="${alertId}" class="alert alert-${type} ${settings.dismissible ? 'alert-dismissible' : ''} fade show position-fixed" 
                 style="top: 20px; right: 20px; z-index: 9999; min-width: 300px; max-width: 500px;">
                <div class="d-flex align-items-center">
                    <i class="fas fa-${this.getAlertIcon(type)} me-2"></i>
                    <div class="flex-grow-1">
                        ${settings.title ? `<h6 class="alert-heading mb-1">${settings.title}</h6>` : ''}
                        ${message}
                    </div>
                    ${settings.dismissible ? '<button type="button" class="btn-close" data-bs-dismiss="alert"></button>' : ''}
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', alertHTML);

        // إخفاء تلقائي
        if (settings.autoHide) {
            setTimeout(() => {
                const alertElement = document.getElementById(alertId);
                if (alertElement) {
                    alertElement.remove();
                }
            }, settings.duration);
        }

        return alertId;
    },

    /**
     * إظهار Toast
     */
    showToast: function(message, type = 'info', options = {}) {
        const settings = {
            title: '',
            duration: this.config.toastDuration,
            position: 'top-right',
            ...options
        };

        const toastId = 'toast-' + Date.now();
        const toastHTML = `
            <div id="${toastId}" class="toast position-fixed" style="top: 20px; right: 20px; z-index: 9999;">
                <div class="toast-header">
                    <i class="fas fa-${this.getAlertIcon(type)} text-${type} me-2"></i>
                    <strong class="me-auto">${settings.title || this.getAlertTitle(type)}</strong>
                    <small class="text-muted">الآن</small>
                    <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                </div>
                <div class="toast-body">
                    ${message}
                </div>
            </div>
        `;

        document.body.insertAdjacentHTML('beforeend', toastHTML);
        
        const toastElement = document.getElementById(toastId);
        const toast = new bootstrap.Toast(toastElement, {
            delay: settings.duration
        });

        // إزالة العنصر بعد الإخفاء
        toastElement.addEventListener('hidden.bs.toast', () => {
            toastElement.remove();
        });

        toast.show();
        return toast;
    },

    /**
     * تهيئة مكونات الصفحة
     */
    initPageComponents: function(container) {
        // تهيئة التلميحات
        const tooltipElements = container.querySelectorAll('[data-bs-toggle="tooltip"]');
        tooltipElements.forEach(element => {
            new bootstrap.Tooltip(element);
        });

        // تهيئة القوائم المنسدلة
        const dropdownElements = container.querySelectorAll('[data-bs-toggle="dropdown"]');
        dropdownElements.forEach(element => {
            new bootstrap.Dropdown(element);
        });

        // تهيئة الأكورديون
        const accordionElements = container.querySelectorAll('.accordion');
        accordionElements.forEach(element => {
            // تطبيق تأثيرات إضافية إذا لزم الأمر
        });

        // تهيئة الجداول التفاعلية
        this.initInteractiveTables(container);

        // تهيئة النماذج المحسنة
        this.initEnhancedForms(container);
    },

    /**
     * تهيئة الجداول التفاعلية
     */
    initInteractiveTables: function(container) {
        const tables = container.querySelectorAll('.table-enhanced');
        tables.forEach(table => {
            // إضافة تأثيرات hover
            const rows = table.querySelectorAll('tbody tr');
            rows.forEach(row => {
                row.addEventListener('mouseenter', function() {
                    this.style.backgroundColor = 'rgba(0,123,255,0.1)';
                });
                row.addEventListener('mouseleave', function() {
                    this.style.backgroundColor = '';
                });
            });

            // إضافة وظيفة الفرز
            const headers = table.querySelectorAll('th[data-sort]');
            headers.forEach(header => {
                header.style.cursor = 'pointer';
                header.addEventListener('click', () => {
                    this.sortTable(table, header.dataset.sort);
                });
            });
        });
    },

    /**
     * تهيئة النماذج المحسنة
     */
    initEnhancedForms: function(container) {
        const forms = container.querySelectorAll('form');
        forms.forEach(form => {
            // إضافة التحقق المباشر
            const inputs = form.querySelectorAll('input, select, textarea');
            inputs.forEach(input => {
                input.addEventListener('blur', () => {
                    this.validateField(input);
                });
            });

            // إضافة حفظ تلقائي
            if (form.dataset.autoSave === 'true') {
                this.enableAutoSave(form);
            }
        });
    },

    /**
     * الحصول على عنوان التنبيه
     */
    getAlertTitle: function(type) {
        const titles = {
            success: 'نجح',
            error: 'خطأ',
            warning: 'تحذير',
            info: 'معلومات'
        };
        return titles[type] || 'إشعار';
    },

    /**
     * الحصول على أيقونة التنبيه
     */
    getAlertIcon: function(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    },

    /**
     * تبديل الشريط الجانبي
     */
    toggleSidebar: function() {
        const sidebar = document.querySelector('.sidebar');
        const mainContent = document.querySelector('.main-content');
        
        if (sidebar) {
            this.state.sidebarCollapsed = !this.state.sidebarCollapsed;
            
            if (this.state.sidebarCollapsed) {
                sidebar.classList.add('collapsed');
                if (mainContent) {
                    mainContent.classList.add('sidebar-collapsed');
                }
            } else {
                sidebar.classList.remove('collapsed');
                if (mainContent) {
                    mainContent.classList.remove('sidebar-collapsed');
                }
            }
            
            // حفظ الحالة
            localStorage.setItem('sidebarCollapsed', this.state.sidebarCollapsed);
        }
    },

    /**
     * تطبيق تأثيرات التمرير
     */
    initScrollEffects: function() {
        // تأثير إخفاء/إظهار شريط التنقل عند التمرير
        let lastScrollTop = 0;
        const navbar = document.querySelector('.navbar');
        
        if (navbar) {
            window.addEventListener('scroll', () => {
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                
                if (scrollTop > lastScrollTop && scrollTop > 100) {
                    // التمرير لأسفل
                    navbar.style.transform = 'translateY(-100%)';
                } else {
                    // التمرير لأعلى
                    navbar.style.transform = 'translateY(0)';
                }
                
                lastScrollTop = scrollTop;
            });
        }
    },

    /**
     * تهيئة التلميحات
     */
    initTooltips: function() {
        const tooltipElements = document.querySelectorAll('[data-bs-toggle="tooltip"]');
        tooltipElements.forEach(element => {
            new bootstrap.Tooltip(element, {
                delay: { show: this.config.tooltipDelay, hide: 100 }
            });
        });
    },

    /**
     * تهيئة النوافذ المنبثقة
     */
    initModals: function() {
        // إضافة تأثيرات للنوافذ المنبثقة
        document.addEventListener('show.bs.modal', (e) => {
            const modal = e.target;
            modal.style.opacity = '0';
            modal.style.transform = 'scale(0.9)';

            setTimeout(() => {
                modal.style.transition = 'all 0.3s ease';
                modal.style.opacity = '1';
                modal.style.transform = 'scale(1)';
            }, 10);
        });
    },

    /**
     * تهيئة الشريط الجانبي
     */
    initSidebar: function() {
        // استعادة حالة الشريط الجانبي
        const sidebarCollapsed = localStorage.getItem('sidebarCollapsed') === 'true';
        this.state.sidebarCollapsed = sidebarCollapsed;

        if (sidebarCollapsed) {
            const sidebar = document.querySelector('.sidebar');
            const mainContent = document.querySelector('.main-content');

            if (sidebar) sidebar.classList.add('collapsed');
            if (mainContent) mainContent.classList.add('sidebar-collapsed');
        }
    },

    /**
     * تهيئة شريط التنقل
     */
    initNavbar: function() {
        // إضافة تأثيرات التمرير
        this.initScrollEffects();

        // تهيئة القوائم المنسدلة
        const dropdowns = document.querySelectorAll('.dropdown-toggle');
        dropdowns.forEach(dropdown => {
            new bootstrap.Dropdown(dropdown);
        });
    },

    /**
     * تهيئة التنقل بلوحة المفاتيح
     */
    initKeyboardNavigation: function() {
        document.addEventListener('keydown', (e) => {
            // Alt + M لتبديل القائمة
            if (e.altKey && e.key === 'm') {
                e.preventDefault();
                this.toggleSidebar();
            }

            // Alt + N لإضافة جديد
            if (e.altKey && e.key === 'n') {
                e.preventDefault();
                this.triggerAddNew();
            }

            // Alt + S للبحث
            if (e.altKey && e.key === 's') {
                e.preventDefault();
                this.focusSearch();
            }
        });
    },

    /**
     * تهيئة إمكانية الوصول
     */
    initAccessibility: function() {
        // إضافة تسميات ARIA
        const buttons = document.querySelectorAll('button:not([aria-label])');
        buttons.forEach(button => {
            const icon = button.querySelector('i');
            if (icon && !button.textContent.trim()) {
                const title = button.getAttribute('title') || 'زر';
                button.setAttribute('aria-label', title);
            }
        });

        // إضافة دعم لقارئ الشاشة
        const tables = document.querySelectorAll('table');
        tables.forEach(table => {
            if (!table.getAttribute('role')) {
                table.setAttribute('role', 'table');
            }
        });
    },

    /**
     * فرز الجدول
     */
    sortTable: function(table, column) {
        const tbody = table.querySelector('tbody');
        const rows = Array.from(tbody.querySelectorAll('tr'));
        const columnIndex = Array.from(table.querySelectorAll('th')).findIndex(th =>
            th.getAttribute('data-sort') === column
        );

        if (columnIndex === -1) return;

        // تحديد اتجاه الفرز
        const currentOrder = table.getAttribute('data-sort-order') || 'asc';
        const newOrder = currentOrder === 'asc' ? 'desc' : 'asc';
        table.setAttribute('data-sort-order', newOrder);

        // فرز الصفوف
        rows.sort((a, b) => {
            const aValue = a.cells[columnIndex].textContent.trim();
            const bValue = b.cells[columnIndex].textContent.trim();

            // محاولة تحويل إلى رقم
            const aNum = parseFloat(aValue.replace(/[^\d.-]/g, ''));
            const bNum = parseFloat(bValue.replace(/[^\d.-]/g, ''));

            let comparison = 0;
            if (!isNaN(aNum) && !isNaN(bNum)) {
                comparison = aNum - bNum;
            } else {
                comparison = aValue.localeCompare(bValue, 'ar');
            }

            return newOrder === 'asc' ? comparison : -comparison;
        });

        // إعادة ترتيب الصفوف
        rows.forEach(row => tbody.appendChild(row));

        // تحديث مؤشرات الفرز
        table.querySelectorAll('th').forEach(th => {
            th.classList.remove('sorted-asc', 'sorted-desc');
        });

        const sortedHeader = table.querySelector(`th[data-sort="${column}"]`);
        if (sortedHeader) {
            sortedHeader.classList.add(`sorted-${newOrder}`);
        }
    },

    /**
     * التحقق من صحة الحقل
     */
    validateField: function(field) {
        const value = field.value.trim();
        const type = field.type;
        const required = field.hasAttribute('required');

        // إزالة رسائل الخطأ السابقة
        this.clearFieldError(field);

        // التحقق من الحقول المطلوبة
        if (required && !value) {
            this.showFieldError(field, 'هذا الحقل مطلوب');
            return false;
        }

        // التحقق من البريد الإلكتروني
        if (type === 'email' && value) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(value)) {
                this.showFieldError(field, 'البريد الإلكتروني غير صحيح');
                return false;
            }
        }

        // التحقق من رقم الهاتف
        if (type === 'tel' && value) {
            const phoneRegex = /^[\+]?[0-9\s\-\(\)]{10,}$/;
            if (!phoneRegex.test(value)) {
                this.showFieldError(field, 'رقم الهاتف غير صحيح');
                return false;
            }
        }

        // التحقق من كلمة المرور
        if (type === 'password' && value) {
            if (value.length < 8) {
                this.showFieldError(field, 'كلمة المرور يجب أن تكون 8 أحرف على الأقل');
                return false;
            }
        }

        return true;
    },

    /**
     * إظهار خطأ في الحقل
     */
    showFieldError: function(field, message) {
        field.classList.add('is-invalid');

        const errorDiv = document.createElement('div');
        errorDiv.className = 'invalid-feedback';
        errorDiv.textContent = message;

        field.parentNode.appendChild(errorDiv);
    },

    /**
     * مسح خطأ الحقل
     */
    clearFieldError: function(field) {
        field.classList.remove('is-invalid');

        const errorDiv = field.parentNode.querySelector('.invalid-feedback');
        if (errorDiv) {
            errorDiv.remove();
        }
    },

    /**
     * تفعيل الحفظ التلقائي
     */
    enableAutoSave: function(form) {
        const inputs = form.querySelectorAll('input, select, textarea');

        inputs.forEach(input => {
            input.addEventListener('input', () => {
                clearTimeout(this.autoSaveTimeout);
                this.autoSaveTimeout = setTimeout(() => {
                    this.autoSaveForm(form);
                }, 2000); // حفظ بعد ثانيتين من التوقف عن الكتابة
            });
        });
    },

    /**
     * حفظ النموذج تلقائياً
     */
    autoSaveForm: function(form) {
        const formData = new FormData(form);
        const data = Object.fromEntries(formData.entries());

        // حفظ في التخزين المحلي
        const formId = form.id || 'autoSaveForm';
        localStorage.setItem(`autoSave_${formId}`, JSON.stringify(data));

        // إظهار مؤشر الحفظ
        this.showAutoSaveIndicator();
    },

    /**
     * إظهار مؤشر الحفظ التلقائي
     */
    showAutoSaveIndicator: function() {
        const indicator = document.createElement('div');
        indicator.className = 'auto-save-indicator position-fixed';
        indicator.style.cssText = 'bottom: 20px; left: 20px; z-index: 9999;';
        indicator.innerHTML = `
            <div class="alert alert-success alert-sm">
                <i class="fas fa-check me-2"></i>تم الحفظ تلقائياً
            </div>
        `;

        document.body.appendChild(indicator);

        setTimeout(() => {
            indicator.remove();
        }, 2000);
    },

    /**
     * التركيز على البحث
     */
    focusSearch: function() {
        const searchInput = document.querySelector('#searchInput, input[type="search"], .search-input');
        if (searchInput) {
            searchInput.focus();
            searchInput.select();
        }
    },

    /**
     * تشغيل إضافة جديد
     */
    triggerAddNew: function() {
        const addButton = document.querySelector('[onclick*="showAdd"], [onclick*="Add"], .btn-add-new');
        if (addButton) {
            addButton.click();
        }
    },

    /**
     * تحديث الشريط الجانبي
     */
    updateSidebar: function(menuItems) {
        const sidebar = document.querySelector('.sidebar-menu');
        if (!sidebar) return;

        const menuHTML = menuItems.map(item => `
            <li class="nav-item">
                <a class="nav-link ${item.active ? 'active' : ''}" href="#" onclick="${item.onclick}">
                    <i class="${item.icon} me-2"></i>
                    <span>${item.title}</span>
                </a>
            </li>
        `).join('');

        sidebar.innerHTML = menuHTML;
    },

    /**
     * إضافة تأثيرات الحركة
     */
    addAnimations: function() {
        // تأثير ظهور العناصر عند التمرير
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-in');
                }
            });
        }, observerOptions);

        // مراقبة العناصر القابلة للحركة
        document.querySelectorAll('.dashboard-card, .stat-card, .chart-container').forEach(el => {
            observer.observe(el);
        });
    },

    /**
     * تحديث الثيم
     */
    updateTheme: function(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        localStorage.setItem('theme', theme);
        this.state.currentTheme = theme;
    },

    /**
     * تحديث اللغة
     */
    updateLanguage: function(language) {
        document.documentElement.setAttribute('lang', language);
        document.documentElement.setAttribute('dir', language === 'ar' ? 'rtl' : 'ltr');
        localStorage.setItem('language', language);
    }
};
