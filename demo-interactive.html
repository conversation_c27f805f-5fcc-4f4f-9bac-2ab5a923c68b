<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>العرض التوضيحي التفاعلي - قمة الوعد للسفريات</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
    <style>
        * {
            font-family: 'Cairo', sans-serif;
        }
        
        body {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            min-height: 100vh;
        }
        
        .demo-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 60px rgba(0,0,0,0.1);
            overflow: hidden;
            margin: 2rem 0;
        }
        
        .demo-header {
            background: linear-gradient(135deg, #2c5aa0, #1e3c72);
            color: white;
            padding: 2rem;
            text-align: center;
        }
        
        .demo-nav {
            background: #f8f9fa;
            padding: 1rem;
            border-bottom: 1px solid #dee2e6;
        }
        
        .demo-nav .nav-pills .nav-link {
            border-radius: 25px;
            margin: 0 0.5rem;
            transition: all 0.3s ease;
        }
        
        .demo-nav .nav-pills .nav-link.active {
            background: #2c5aa0;
        }
        
        .demo-content {
            padding: 2rem;
            min-height: 600px;
        }
        
        .feature-demo {
            display: none;
            animation: fadeIn 0.5s ease-in-out;
        }
        
        .feature-demo.active {
            display: block;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .demo-card {
            background: #f8f9fa;
            border-radius: 15px;
            padding: 1.5rem;
            margin: 1rem 0;
            border-left: 4px solid #2c5aa0;
            transition: all 0.3s ease;
        }
        
        .demo-card:hover {
            transform: translateX(10px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .demo-screenshot {
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            transition: transform 0.3s ease;
        }
        
        .demo-screenshot:hover {
            transform: scale(1.05);
        }
        
        .step-indicator {
            display: flex;
            align-items: center;
            margin: 1rem 0;
        }
        
        .step-number {
            width: 40px;
            height: 40px;
            background: #2c5aa0;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-left: 1rem;
        }
        
        .interactive-button {
            background: linear-gradient(135deg, #28a745, #20c997);
            border: none;
            color: white;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: 600;
            transition: all 0.3s ease;
            margin: 0.5rem;
        }
        
        .interactive-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(40, 167, 69, 0.3);
        }
        
        .demo-stats {
            background: linear-gradient(135deg, #2c5aa0, #28a745);
            color: white;
            border-radius: 15px;
            padding: 2rem;
            text-align: center;
            margin: 1rem 0;
        }
        
        .stat-item {
            margin: 1rem 0;
        }
        
        .stat-number {
            font-size: 2.5rem;
            font-weight: bold;
            display: block;
        }
        
        .video-container {
            position: relative;
            width: 100%;
            height: 400px;
            background: #000;
            border-radius: 15px;
            overflow: hidden;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .play-button {
            width: 80px;
            height: 80px;
            background: rgba(255,255,255,0.9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .play-button:hover {
            transform: scale(1.1);
            background: white;
        }
        
        .play-button i {
            color: #2c5aa0;
            font-size: 2rem;
            margin-right: -5px;
        }
    </style>
</head>
<body>
    <div class="container-fluid">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="demo-container">
                    <!-- Header -->
                    <div class="demo-header">
                        <h1><i class="fas fa-play-circle me-3"></i>العرض التوضيحي التفاعلي</h1>
                        <p class="lead mb-0">اكتشف قوة نظام قمة الوعد للسفريات</p>
                    </div>
                    
                    <!-- Navigation -->
                    <div class="demo-nav">
                        <ul class="nav nav-pills justify-content-center">
                            <li class="nav-item">
                                <a class="nav-link active" href="#" data-demo="overview">نظرة عامة</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#" data-demo="users">إدارة المستخدمين</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#" data-demo="bookings">إدارة الحجوزات</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#" data-demo="accounting">النظام المحاسبي</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#" data-demo="security">الأمان والحماية</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#" data-demo="reports">التقارير</a>
                            </li>
                        </ul>
                    </div>
                    
                    <!-- Content -->
                    <div class="demo-content">
                        <!-- Overview Demo -->
                        <div class="feature-demo active" id="overview">
                            <div class="row">
                                <div class="col-lg-6">
                                    <h3><i class="fas fa-chart-line text-primary me-2"></i>لوحة التحكم الذكية</h3>
                                    <p class="text-muted">
                                        لوحة تحكم شاملة تعرض جميع المعلومات المهمة في مكان واحد
                                    </p>
                                    
                                    <div class="demo-stats">
                                        <div class="row">
                                            <div class="col-4">
                                                <div class="stat-item">
                                                    <span class="stat-number" data-count="1250">0</span>
                                                    <small>حجز هذا الشهر</small>
                                                </div>
                                            </div>
                                            <div class="col-4">
                                                <div class="stat-item">
                                                    <span class="stat-number" data-count="85">0</span>
                                                    <small>عميل نشط</small>
                                                </div>
                                            </div>
                                            <div class="col-4">
                                                <div class="stat-item">
                                                    <span class="stat-number" data-count="95">0</span>
                                                    <small>% معدل الرضا</small>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <button class="interactive-button" onclick="simulateAction('dashboard')">
                                        <i class="fas fa-eye me-2"></i>عرض لوحة التحكم
                                    </button>
                                </div>
                                <div class="col-lg-6">
                                    <img src="https://via.placeholder.com/500x350/f8f9fa/2c5aa0?text=لوحة+التحكم+الذكية" 
                                         alt="لوحة التحكم" class="img-fluid demo-screenshot">
                                </div>
                            </div>
                        </div>
                        
                        <!-- Users Demo -->
                        <div class="feature-demo" id="users">
                            <h3><i class="fas fa-users text-primary me-2"></i>إدارة المستخدمين المتقدمة</h3>
                            <p class="text-muted">
                                نظام متطور لإدارة المستخدمين مع صلاحيات محددة بالأيام
                            </p>
                            
                            <div class="row">
                                <div class="col-lg-8">
                                    <div class="demo-card">
                                        <div class="step-indicator">
                                            <div class="step-number">1</div>
                                            <div>
                                                <h5>إضافة مستخدم جديد</h5>
                                                <p class="text-muted mb-0">إنشاء حساب مستخدم مع تحديد الدور والصلاحيات</p>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="demo-card">
                                        <div class="step-indicator">
                                            <div class="step-number">2</div>
                                            <div>
                                                <h5>تحديد المدة الزمنية</h5>
                                                <p class="text-muted mb-0">تحديد فترة صلاحية الحساب بالأيام</p>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="demo-card">
                                        <div class="step-indicator">
                                            <div class="step-number">3</div>
                                            <div>
                                                <h5>إدارة الصلاحيات</h5>
                                                <p class="text-muted mb-0">تخصيص الصلاحيات حسب الحاجة</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-lg-4">
                                    <button class="interactive-button w-100 mb-2" onclick="simulateAction('add_user')">
                                        <i class="fas fa-user-plus me-2"></i>إضافة مستخدم
                                    </button>
                                    <button class="interactive-button w-100 mb-2" onclick="simulateAction('manage_permissions')">
                                        <i class="fas fa-key me-2"></i>إدارة الصلاحيات
                                    </button>
                                    <button class="interactive-button w-100" onclick="simulateAction('view_activity')">
                                        <i class="fas fa-history me-2"></i>عرض النشاط
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Bookings Demo -->
                        <div class="feature-demo" id="bookings">
                            <h3><i class="fas fa-plane text-primary me-2"></i>إدارة الحجوزات</h3>
                            <p class="text-muted">
                                نظام شامل لإدارة جميع أنواع الحجوزات والخدمات
                            </p>
                            
                            <div class="video-container">
                                <div class="play-button" onclick="playDemo('bookings')">
                                    <i class="fas fa-play"></i>
                                </div>
                            </div>
                            
                            <div class="row mt-4">
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <i class="fas fa-plane fa-3x text-primary mb-2"></i>
                                        <h6>تذاكر الطيران</h6>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <i class="fas fa-hotel fa-3x text-success mb-2"></i>
                                        <h6>حجز الفنادق</h6>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <i class="fas fa-kaaba fa-3x text-warning mb-2"></i>
                                        <h6>الحج والعمرة</h6>
                                    </div>
                                </div>
                                <div class="col-md-3">
                                    <div class="text-center">
                                        <i class="fas fa-passport fa-3x text-info mb-2"></i>
                                        <h6>التأشيرات</h6>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Accounting Demo -->
                        <div class="feature-demo" id="accounting">
                            <h3><i class="fas fa-calculator text-primary me-2"></i>النظام المحاسبي</h3>
                            <p class="text-muted">
                                نظام محاسبي متكامل مع دليل حسابات وقيود تلقائية
                            </p>
                            
                            <div class="row">
                                <div class="col-lg-6">
                                    <div class="demo-card">
                                        <h5><i class="fas fa-book text-primary me-2"></i>دليل الحسابات</h5>
                                        <p class="text-muted">دليل حسابات شامل ومرن</p>
                                        <button class="btn btn-outline-primary btn-sm">عرض الدليل</button>
                                    </div>
                                    
                                    <div class="demo-card">
                                        <h5><i class="fas fa-receipt text-success me-2"></i>القيود المحاسبية</h5>
                                        <p class="text-muted">قيود تلقائية لجميع العمليات</p>
                                        <button class="btn btn-outline-success btn-sm">عرض القيود</button>
                                    </div>
                                </div>
                                <div class="col-lg-6">
                                    <div class="demo-card">
                                        <h5><i class="fas fa-chart-pie text-warning me-2"></i>التقارير المالية</h5>
                                        <p class="text-muted">تقارير مالية شاملة ومفصلة</p>
                                        <button class="btn btn-outline-warning btn-sm">عرض التقارير</button>
                                    </div>
                                    
                                    <div class="demo-card">
                                        <h5><i class="fas fa-coins text-info me-2"></i>متعدد العملات</h5>
                                        <p class="text-muted">دعم العملات المتعددة</p>
                                        <button class="btn btn-outline-info btn-sm">إدارة العملات</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Security Demo -->
                        <div class="feature-demo" id="security">
                            <h3><i class="fas fa-shield-alt text-primary me-2"></i>الأمان والحماية</h3>
                            <p class="text-muted">
                                نظام أمان متقدم مع تشفير البيانات وحماية شاملة
                            </p>
                            
                            <div class="row">
                                <div class="col-lg-4">
                                    <div class="text-center p-3">
                                        <i class="fas fa-lock fa-4x text-primary mb-3"></i>
                                        <h5>تشفير البيانات</h5>
                                        <p class="text-muted">تشفير متقدم لجميع البيانات الحساسة</p>
                                    </div>
                                </div>
                                <div class="col-lg-4">
                                    <div class="text-center p-3">
                                        <i class="fas fa-user-shield fa-4x text-success mb-3"></i>
                                        <h5>مصادقة ثنائية</h5>
                                        <p class="text-muted">حماية إضافية للحسابات المهمة</p>
                                    </div>
                                </div>
                                <div class="col-lg-4">
                                    <div class="text-center p-3">
                                        <i class="fas fa-eye fa-4x text-warning mb-3"></i>
                                        <h5>مراقبة النشاط</h5>
                                        <p class="text-muted">تتبع شامل لجميع الأنشطة</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Reports Demo -->
                        <div class="feature-demo" id="reports">
                            <h3><i class="fas fa-chart-bar text-primary me-2"></i>التقارير الذكية</h3>
                            <p class="text-muted">
                                تقارير تفصيلية وتحليلات ذكية لاتخاذ القرارات الصحيحة
                            </p>
                            
                            <div class="row">
                                <div class="col-lg-6">
                                    <img src="https://via.placeholder.com/400x250/f8f9fa/2c5aa0?text=تقرير+المبيعات" 
                                         alt="تقرير المبيعات" class="img-fluid demo-screenshot mb-3">
                                    <h6>تقرير المبيعات الشهري</h6>
                                </div>
                                <div class="col-lg-6">
                                    <img src="https://via.placeholder.com/400x250/f8f9fa/28a745?text=تحليل+الأداء" 
                                         alt="تحليل الأداء" class="img-fluid demo-screenshot mb-3">
                                    <h6>تحليل أداء الوكلاء</h6>
                                </div>
                            </div>
                            
                            <div class="text-center mt-4">
                                <button class="interactive-button" onclick="simulateAction('generate_report')">
                                    <i class="fas fa-file-pdf me-2"></i>إنشاء تقرير
                                </button>
                                <button class="interactive-button" onclick="simulateAction('export_data')">
                                    <i class="fas fa-download me-2"></i>تصدير البيانات
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Back to Landing -->
                <div class="text-center mt-4">
                    <a href="landing.html" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-arrow-right me-2"></i>العودة للصفحة الرئيسية
                    </a>
                    <a href="login.html" class="btn btn-primary btn-lg ms-3">
                        <i class="fas fa-sign-in-alt me-2"></i>تجربة النظام
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Demo Navigation
        document.querySelectorAll('[data-demo]').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                
                // Update active nav
                document.querySelectorAll('.nav-link').forEach(nav => nav.classList.remove('active'));
                this.classList.add('active');
                
                // Show demo content
                const demoId = this.getAttribute('data-demo');
                document.querySelectorAll('.feature-demo').forEach(demo => demo.classList.remove('active'));
                document.getElementById(demoId).classList.add('active');
                
                // Animate counters if overview
                if (demoId === 'overview') {
                    animateCounters();
                }
            });
        });

        // Counter Animation
        function animateCounters() {
            document.querySelectorAll('.stat-number').forEach(counter => {
                const target = parseInt(counter.getAttribute('data-count'));
                const increment = target / 50;
                let current = 0;
                
                const timer = setInterval(() => {
                    current += increment;
                    if (current >= target) {
                        current = target;
                        clearInterval(timer);
                    }
                    counter.textContent = Math.floor(current);
                }, 30);
            });
        }

        // Simulate Actions
        function simulateAction(action) {
            const messages = {
                'dashboard': 'تم فتح لوحة التحكم الرئيسية',
                'add_user': 'تم فتح نافذة إضافة مستخدم جديد',
                'manage_permissions': 'تم فتح إدارة الصلاحيات',
                'view_activity': 'تم عرض سجل النشاط',
                'generate_report': 'جاري إنشاء التقرير...',
                'export_data': 'جاري تصدير البيانات...'
            };
            
            // Show notification
            showNotification(messages[action] || 'تم تنفيذ العملية بنجاح');
        }

        // Play Demo Video
        function playDemo(type) {
            showNotification('سيتم إضافة فيديو توضيحي قريباً');
        }

        // Show Notification
        function showNotification(message) {
            const notification = document.createElement('div');
            notification.className = 'alert alert-success position-fixed';
            notification.style.cssText = 'top: 20px; left: 20px; z-index: 9999; min-width: 300px;';
            notification.innerHTML = `
                <i class="fas fa-check-circle me-2"></i>
                ${message}
                <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                if (notification.parentElement) {
                    notification.remove();
                }
            }, 3000);
        }

        // Initialize counters on load
        document.addEventListener('DOMContentLoaded', function() {
            animateCounters();
        });
    </script>
</body>
</html>
