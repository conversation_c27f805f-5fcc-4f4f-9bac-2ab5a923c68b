/**
 * ===================================
 * التطبيق المتقدم - Advanced Application Core
 * قمة الوعد للسفريات
 * ===================================
 */

window.AdvancedApp = {
    // إعدادات التطبيق
    config: {
        version: '2.0.0',
        name: 'قمة الوعد للسفريات - النسخة المتقدمة',
        theme: 'default',
        language: 'ar',
        autoSave: true,
        autoSaveInterval: 30000,
        sessionTimeout: 1800000, // 30 دقيقة
        maxFileSize: 10485760, // 10 MB
        allowedFileTypes: ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png', 'xlsx', 'xls'],
        apiEndpoint: '/api/v1',
        enableNotifications: true,
        enableAnalytics: false,
        enableOfflineMode: true
    },

    // حالة التطبيق
    state: {
        isInitialized: false,
        currentUser: null,
        currentPage: 'dashboard',
        isOnline: navigator.onLine,
        lastActivity: Date.now(),
        notifications: [],
        theme: 'light',
        sidebarCollapsed: false,
        activeModals: [],
        loadingStates: new Map(),
        errors: [],
        warnings: []
    },

    // مكونات التطبيق
    components: {
        dashboard: null,
        customers: null,
        bookings: null,
        accounting: null,
        users: null,
        reports: null
    },

    /**
     * تهيئة التطبيق المتقدم
     */
    init: function() {
        console.log('🚀 تهيئة التطبيق المتقدم - قمة الوعد للسفريات v' + this.config.version);
        
        try {
            // التحقق من المتطلبات
            this.checkRequirements();
            
            // تهيئة الأنظمة الأساسية
            this.initCore();
            
            // تهيئة واجهة المستخدم
            this.initUI();
            
            // تهيئة المكونات
            this.initComponents();
            
            // تهيئة الأحداث
            this.initEvents();
            
            // بدء الخدمات
            this.startServices();
            
            this.state.isInitialized = true;
            console.log('✅ تم تهيئة التطبيق المتقدم بنجاح');
            
            // إظهار لوحة التحكم
            this.showDashboard();
            
        } catch (error) {
            console.error('❌ خطأ في تهيئة التطبيق:', error);
            this.handleCriticalError(error);
        }
    },

    /**
     * التحقق من المتطلبات
     */
    checkRequirements: function() {
        const requirements = [
            { name: 'localStorage', check: () => typeof Storage !== 'undefined' },
            { name: 'fetch', check: () => typeof fetch !== 'undefined' },
            { name: 'Promise', check: () => typeof Promise !== 'undefined' },
            { name: 'JSON', check: () => typeof JSON !== 'undefined' },
            { name: 'Bootstrap', check: () => typeof bootstrap !== 'undefined' },
            { name: 'Chart.js', check: () => typeof Chart !== 'undefined' }
        ];

        const missing = requirements.filter(req => !req.check());
        
        if (missing.length > 0) {
            throw new Error('متطلبات مفقودة: ' + missing.map(r => r.name).join(', '));
        }
    },

    /**
     * تهيئة الأنظمة الأساسية
     */
    initCore: function() {
        // تهيئة قاعدة البيانات
        if (window.Database) {
            window.Database.init();
        }

        // تهيئة نظام المصادقة
        if (window.Auth) {
            window.Auth.init();
        }

        // تهيئة نظام الأمان
        if (window.Security) {
            window.Security.init();
        }

        // تهيئة نظام التشفير
        if (window.Encryption) {
            window.Encryption.init();
        }

        // تحميل بيانات المستخدم
        this.loadUserData();
    },

    /**
     * تهيئة واجهة المستخدم
     */
    initUI: function() {
        // تطبيق الثيم
        this.applyTheme();
        
        // تهيئة الشريط الجانبي
        this.initSidebar();
        
        // تهيئة شريط التنقل
        this.initNavbar();
        
        // تهيئة الإشعارات
        this.initNotifications();
        
        // تهيئة اختصارات لوحة المفاتيح
        this.initKeyboardShortcuts();
    },

    /**
     * تهيئة المكونات
     */
    initComponents: function() {
        // تهيئة لوحة التحكم المتقدمة
        if (window.AdvancedDashboard) {
            this.components.dashboard = window.AdvancedDashboard;
            this.components.dashboard.init();
        }

        // تهيئة إدارة العملاء المتقدمة
        if (window.AdvancedCustomers) {
            this.components.customers = window.AdvancedCustomers;
            this.components.customers.init();
        }

        // تهيئة إدارة الحجوزات المتقدمة
        if (window.AdvancedBookings) {
            this.components.bookings = window.AdvancedBookings;
            this.components.bookings.init();
        }

        // تهيئة إدارة المستخدمين
        if (window.UserManagement) {
            this.components.users = window.UserManagement;
            this.components.users.init();
        }

        // تهيئة النظام المحاسبي
        if (window.Accounting) {
            this.components.accounting = window.Accounting;
            this.components.accounting.init();
        }
    },

    /**
     * تهيئة الأحداث
     */
    initEvents: function() {
        // مراقبة حالة الاتصال
        window.addEventListener('online', () => {
            this.state.isOnline = true;
            this.showNotification('تم استعادة الاتصال بالإنترنت', 'success');
        });

        window.addEventListener('offline', () => {
            this.state.isOnline = false;
            this.showNotification('انقطع الاتصال بالإنترنت - تم تفعيل الوضع غير المتصل', 'warning');
        });

        // مراقبة النشاط
        ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'].forEach(event => {
            document.addEventListener(event, () => {
                this.state.lastActivity = Date.now();
            }, { passive: true });
        });

        // مراقبة تغيير حجم النافذة
        window.addEventListener('resize', this.handleResize.bind(this));

        // مراقبة إغلاق النافذة
        window.addEventListener('beforeunload', this.handleBeforeUnload.bind(this));
    },

    /**
     * بدء الخدمات
     */
    startServices: function() {
        // خدمة الحفظ التلقائي
        if (this.config.autoSave) {
            this.startAutoSave();
        }

        // خدمة مراقبة الجلسة
        this.startSessionMonitoring();

        // خدمة التحديث التلقائي
        this.startAutoRefresh();

        // خدمة النسخ الاحتياطي
        this.startBackupService();
    },

    /**
     * عرض لوحة التحكم
     */
    showDashboard: function() {
        if (this.components.dashboard) {
            this.components.dashboard.show();
            this.state.currentPage = 'dashboard';
            this.updatePageTitle('لوحة التحكم');
        }
    },

    /**
     * عرض إدارة العملاء
     */
    showCustomers: function() {
        if (this.components.customers) {
            this.components.customers.show();
            this.state.currentPage = 'customers';
            this.updatePageTitle('إدارة العملاء');
        }
    },

    /**
     * عرض إدارة الحجوزات
     */
    showBookings: function() {
        if (this.components.bookings) {
            this.components.bookings.show();
            this.state.currentPage = 'bookings';
            this.updatePageTitle('إدارة الحجوزات');
        }
    },

    /**
     * عرض إدارة المستخدمين
     */
    showUsers: function() {
        if (this.components.users) {
            this.components.users.show();
            this.state.currentPage = 'users';
            this.updatePageTitle('إدارة المستخدمين');
        }
    },

    /**
     * عرض النظام المحاسبي
     */
    showAccounting: function() {
        if (this.components.accounting) {
            this.components.accounting.show();
            this.state.currentPage = 'accounting';
            this.updatePageTitle('النظام المحاسبي');
        }
    },

    /**
     * تحميل بيانات المستخدم
     */
    loadUserData: function() {
        const userData = localStorage.getItem('currentUser');
        if (userData) {
            try {
                this.state.currentUser = JSON.parse(userData);
                this.updateUserInfo();
            } catch (error) {
                console.error('خطأ في تحميل بيانات المستخدم:', error);
            }
        }
    },

    /**
     * تحديث معلومات المستخدم في الواجهة
     */
    updateUserInfo: function() {
        const userInfo = document.getElementById('user-info');
        if (userInfo && this.state.currentUser) {
            userInfo.innerHTML = `
                <div class="d-flex align-items-center">
                    <img src="${this.state.currentUser.avatar || 'https://ui-avatars.com/api/?name=' + encodeURIComponent(this.state.currentUser.fullName || 'مستخدم')}" 
                         alt="صورة المستخدم" class="rounded-circle me-2" width="32" height="32">
                    <div>
                        <div class="fw-bold">${this.state.currentUser.fullName || 'مستخدم'}</div>
                        <small class="text-muted">${this.state.currentUser.role || 'مستخدم'}</small>
                    </div>
                </div>
            `;
        }
    },

    /**
     * تطبيق الثيم
     */
    applyTheme: function() {
        const theme = localStorage.getItem('theme') || 'light';
        this.state.theme = theme;
        document.documentElement.setAttribute('data-theme', theme);
    },

    /**
     * تبديل الثيم
     */
    toggleTheme: function() {
        const newTheme = this.state.theme === 'light' ? 'dark' : 'light';
        this.state.theme = newTheme;
        localStorage.setItem('theme', newTheme);
        document.documentElement.setAttribute('data-theme', newTheme);
        this.showNotification('تم تغيير المظهر إلى ' + (newTheme === 'light' ? 'الفاتح' : 'الداكن'), 'info');
    },

    /**
     * إظهار إشعار
     */
    showNotification: function(message, type = 'info', duration = 5000) {
        const notification = {
            id: Date.now(),
            message,
            type,
            timestamp: new Date()
        };

        this.state.notifications.unshift(notification);

        // إنشاء عنصر الإشعار
        const notificationElement = document.createElement('div');
        notificationElement.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        notificationElement.style.cssText = 'top: 20px; left: 20px; z-index: 9999; min-width: 300px; max-width: 500px;';
        notificationElement.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="fas fa-${this.getNotificationIcon(type)} me-2"></i>
                <div class="flex-grow-1">${message}</div>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;

        document.body.appendChild(notificationElement);

        // إزالة الإشعار تلقائياً
        setTimeout(() => {
            if (notificationElement.parentElement) {
                notificationElement.remove();
            }
        }, duration);

        // إزالة من المصفوفة بعد دقيقة
        setTimeout(() => {
            const index = this.state.notifications.findIndex(n => n.id === notification.id);
            if (index > -1) {
                this.state.notifications.splice(index, 1);
            }
        }, 60000);
    },

    /**
     * الحصول على أيقونة الإشعار
     */
    getNotificationIcon: function(type) {
        const icons = {
            success: 'check-circle',
            error: 'exclamation-circle',
            warning: 'exclamation-triangle',
            info: 'info-circle'
        };
        return icons[type] || 'info-circle';
    },

    /**
     * تحديث عنوان الصفحة
     */
    updatePageTitle: function(pageTitle) {
        document.title = `${pageTitle} - ${this.config.name}`;
    }
};
