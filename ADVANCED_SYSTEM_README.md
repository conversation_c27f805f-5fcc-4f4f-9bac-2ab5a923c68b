# نظام قمة الوعد للسفريات - النسخة المتقدمة 2.0

## 🌟 نظرة عامة

تم تطوير النظام ليصبح نظاماً محاسبياً متقدماً عبر الإنترنت مع إدارة المستخدمين والصلاحيات المحددة بالأيام. النظام الآن يوفر حماية متقدمة ونظام مصادقة آمن مع إمكانيات نشر على الويب.

## ✨ المميزات الجديدة

### 🔐 نظام إدارة المستخدمين المتقدم
- **صلاحيات محددة بالأيام**: تحكم كامل في مدة استخدام كل مستخدم
- **أدوار متعددة**: مدير، محاسب، وكيل، موظف، مشاهد
- **مصادقة ثنائية**: حماية إضافية للحسابات الحساسة
- **تتبع النشاط**: سجل شامل لجميع أنشطة المستخدمين
- **إدارة الجلسات**: تحكم في الجلسات المتزامنة

### 🛡️ نظام الأمان المتقدم
- **تشفير البيانات**: تشفير متقدم لجميع البيانات الحساسة
- **حماية من الهجمات**: حماية من XSS، CSRF، SQL Injection
- **مراقبة الأمان**: كشف المحاولات المشبوهة
- **قفل الحسابات**: قفل تلقائي بعد محاولات فاشلة
- **تدوير المفاتيح**: تجديد مفاتيح التشفير دورياً

### 🌐 نظام النشر على الويب
- **إعدادات خادم شاملة**: ملفات PHP للخادم
- **API متكامل**: واجهات برمجية للتكامل
- **قاعدة بيانات محسنة**: جداول جديدة للصلاحيات والأمان
- **نسخ احتياطية**: نظام نسخ احتياطي تلقائي

### 📊 صفحة ويب تعريفية
- **تصميم احترافي**: صفحة هبوط جذابة
- **شرح المميزات**: عرض تفصيلي لإمكانيات النظام
- **عرض توضيحي**: إمكانية تجربة النظام
- **تجاوب كامل**: يعمل على جميع الأجهزة

## 📁 هيكل النظام المحدث

```
qimat-alwaed/
├── database/
│   └── schema.sql                 # قاعدة البيانات المحسنة
├── server/
│   ├── config.php                # إعدادات الخادم
│   ├── api/
│   │   └── auth.php              # API المصادقة
│   └── includes/                 # ملفات PHP المساعدة
├── src/
│   ├── js/
│   │   ├── components/
│   │   │   └── user-management.js # إدارة المستخدمين
│   │   ├── core/
│   │   │   └── auth.js           # نظام المصادقة المحسن
│   │   └── utils/
│   │       ├── encryption.js     # نظام التشفير
│   │       └── security.js       # نظام الأمان المحسن
├── landing.html                   # الصفحة التعريفية
├── test-advanced-system.html      # نظام الاختبار الشامل
└── ADVANCED_SYSTEM_README.md      # هذا الملف
```

## 🚀 التثبيت والإعداد

### متطلبات النظام
- **خادم ويب**: Apache/Nginx
- **PHP**: 7.4 أو أحدث
- **قاعدة البيانات**: MySQL 5.7 أو أحدث
- **SSL**: شهادة SSL للأمان

### خطوات التثبيت

1. **رفع الملفات**
   ```bash
   # رفع جميع ملفات النظام إلى الخادم
   scp -r qimat-alwaed/ user@server:/var/www/html/
   ```

2. **إعداد قاعدة البيانات**
   ```sql
   # إنشاء قاعدة البيانات
   mysql -u root -p < database/schema.sql
   ```

3. **تكوين الإعدادات**
   ```php
   // تحديث server/config.php
   define('DB_HOST', 'localhost');
   define('DB_NAME', 'qimat_alwaed_accounting');
   define('DB_USER', 'your_username');
   define('DB_PASS', 'your_password');
   ```

4. **إعداد SSL**
   ```apache
   # في ملف .htaccess
   RewriteEngine On
   RewriteCond %{HTTPS} off
   RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
   ```

## 👥 إدارة المستخدمين

### إضافة مستخدم جديد
```javascript
// استخدام واجهة إدارة المستخدمين
UserManagement.showAddUserModal();

// أو عبر API
fetch('/server/api/users.php', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
        action: 'create_user',
        username: 'new_user',
        email: '<EMAIL>',
        role: 'employee',
        subscription_days: 30
    })
});
```

### تحديد الصلاحيات
```javascript
// تحديد صلاحيات محددة بالأيام
const permissions = {
    modules: ['customers', 'bookings'],
    start_date: '2024-01-01',
    end_date: '2024-01-31',
    actions: ['read', 'write']
};
```

## 🔒 الأمان والحماية

### تفعيل المصادقة الثنائية
```javascript
// تفعيل 2FA للمستخدم
Auth.enableTwoFactor(userId);

// التحقق من رمز 2FA
Auth.verifyTwoFactorCode(user, code);
```

### تشفير البيانات
```javascript
// تشفير البيانات الحساسة
const encrypted = Encryption.encryptSensitiveData(data);

// فك التشفير
const decrypted = Encryption.decryptSensitiveData(encrypted);
```

## 📊 المراقبة والتقارير

### سجل النشاط
```sql
-- عرض نشاط المستخدمين
SELECT * FROM user_activity_log 
WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
ORDER BY created_at DESC;
```

### تقارير الأمان
```sql
-- المحاولات المشبوهة
SELECT * FROM suspicious_login_attempts 
WHERE attempt_time >= DATE_SUB(NOW(), INTERVAL 1 HOUR);
```

## 🧪 الاختبار

### تشغيل الاختبارات
```bash
# فتح صفحة الاختبار
open test-advanced-system.html

# أو عبر الخادم
http://your-domain.com/test-advanced-system.html
```

### اختبارات متاحة
- ✅ اختبار المصادقة والأمان
- ✅ اختبار إدارة المستخدمين
- ✅ اختبار التشفير
- ✅ اختبار قاعدة البيانات
- ✅ اختبار الأداء
- ✅ اختبار الواجهة

## 🔧 الصيانة

### النسخ الاحتياطية
```bash
# نسخة احتياطية يومية
mysqldump -u username -p qimat_alwaed_accounting > backup_$(date +%Y%m%d).sql
```

### تحديث النظام
```bash
# تحديث الملفات
git pull origin main

# تحديث قاعدة البيانات
mysql -u username -p qimat_alwaed_accounting < updates/update_v2.1.sql
```

## 📞 الدعم الفني

### معلومات الاتصال
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: +************
- **الموقع**: https://qimat-alwaed.com

### الأخطاء الشائعة

#### خطأ في الاتصال بقاعدة البيانات
```php
// التحقق من إعدادات قاعدة البيانات في config.php
define('DB_HOST', 'correct_host');
define('DB_USER', 'correct_username');
define('DB_PASS', 'correct_password');
```

#### مشاكل SSL
```apache
# التأكد من تفعيل SSL في .htaccess
RewriteEngine On
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]
```

## 📈 خطط التطوير المستقبلية

### الإصدار 2.1
- [ ] تطبيق الهاتف المحمول
- [ ] تكامل مع أنظمة الدفع
- [ ] ذكاء اصطناعي للتقارير
- [ ] API متقدم للتكامل

### الإصدار 2.2
- [ ] نظام إشعارات متقدم
- [ ] تحليلات متقدمة
- [ ] نظام سير العمل
- [ ] تكامل مع الخدمات السحابية

## 📄 الترخيص

هذا النظام محمي بحقوق الطبع والنشر لشركة قمة الوعد للسفريات. جميع الحقوق محفوظة.

## 🤝 المساهمة

نرحب بالمساهمات لتطوير النظام. يرجى اتباع إرشادات المساهمة:

1. Fork المشروع
2. إنشاء فرع للميزة الجديدة
3. Commit التغييرات
4. Push إلى الفرع
5. إنشاء Pull Request

---

**تم تطوير هذا النظام بواسطة فريق قمة الوعد للسفريات**

*آخر تحديث: ديسمبر 2024*
